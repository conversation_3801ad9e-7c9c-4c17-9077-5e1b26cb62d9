# Chapter 7: Stripe Payment Integration

Welcome back to the Atlas University LMS AI project! In our last chapter, [UI Component Library (Shadcn UI)](06_ui_component_library__shadcn_ui__.md), we explored how Shadcn UI helps us build a beautiful and consistent look for our application. We learned how to use pre-built components like buttons and carousels to make Atlas LMS AI visually appealing and easy to navigate.

Now, imagine our amazing learning platform. Users can sign up, generate courses, notes, flashcards, and quizzes. That's fantastic! But what if we want to offer **premium features**, like unlimited course generation, while keeping some basic features free? How do we handle payments, subscriptions, and make sure only paying users get access to those special features?

This is where **Stripe Payment Integration** comes in!

Think of this system as the **app's cashier and subscription manager**. It securely handles all the money-related parts of our application:

*   **Processing Payments:** Safely takes money from users when they want to upgrade.
*   **Managing Subscriptions:** Keeps track of who has a paid plan (monthly, yearly, etc.) and ensures they can access premium features.
*   **Updating User Status:** Automatically changes a user's account from "Free" to "Premium" (or back to "Free" if a payment fails or they cancel).

Our main goal in this chapter is to understand how Atlas LMS AI uses **Stripe** to allow users to **upgrade to premium plans** and manage their subscriptions securely.

## Our Use Case: Upgrading to a Premium Plan

Let's imagine you're a user who has generated a few courses on the free plan, but now you want unlimited access. You'll go to an "Upgrade" page in the app, see the plans, and click "Get Started" on the premium plan. Our payment system needs to:

1.  Show you the available plans (Free vs. Monthly Premium).
2.  Securely take your payment details.
3.  Start your subscription.
4.  Update your account in our app to reflect your new "Premium" status.
5.  Allow you to manage your subscription later (e.g., update your credit card, cancel).

## Our Trusted Payment Partner: Stripe

To handle all these complex payment tasks safely and reliably, we don't build our own payment system from scratch (that would be incredibly difficult and risky!). Instead, we use **Stripe**.

**Stripe** is a leading online payment processing platform. Think of it as a highly secure, digital bank that specializes in handling payments for online businesses.

**Why use Stripe?**

*   **Security:** Stripe handles all the sensitive credit card information, so our app never directly touches it. This keeps your financial data super safe.
*   **Ease of Use:** Stripe provides ready-made tools and a simple way for our app to communicate with it, making integration straightforward.
*   **Global Reach:** It supports payments from customers all over the world.
*   **Subscription Management:** It's built to handle recurring payments (subscriptions), not just one-time purchases.

## How It Works: The "Upgrade" Page

The `app\dashboard\upgrade\page.jsx` file is where users see their current plan and can choose to upgrade.

Here's a simplified look at how this page prepares for and initiates a payment:

```jsx
// File: app\dashboard\upgrade\page.jsx (Simplified)
"use client"
import { Button } from '@/components/ui/button'; // Our UI Button from Chapter 6
import { db } from '@/configs/db'; // Our database connection from Chapter 2
import { USER_TABLE } from '@/drizzle/schema'; // Our user table from Chapter 2
import { useUser } from '@clerk/nextjs'; // User info from Chapter 1
import axios from 'axios';
import { eq } from 'drizzle-orm'; // Drizzle ORM tool

function Upgrade() {
  const { user } = useUser();
  const [userDetail, setUserDetail] = React.useState();
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    user && GetUserDetail(); // Fetch user's premium status when page loads
  }, [user]);

  const GetUserDetail = async () => {
    // Check our database (using Drizzle ORM) if user is a member
    const result = await db.select().from(USER_TABLE)
      .where(eq(USER_TABLE.email, user?.primaryEmailAddress?.emailAddress));
    setUserDetail(result[0]); // Store their details (including `isMember`)
    setIsLoading(false);
  };

  const OnCheckoutClick = async () => {
    // 1. Tell our backend we want to start a Stripe checkout
    const result = await axios.post('/api/payment/checkout', {
      priceId: process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_MONTHLY // Stripe's ID for our premium plan
    });
    // 2. Redirect the user to Stripe's secure payment page
    window.open(result.data?.url);
  };

  const onPaymentMange = async () => {
    // 1. Tell our backend we want to go to Stripe's customer portal
    const result = await axios.post('/api/payment/manage-payment', {
      customerId: userDetail?.customerId // Our user's Stripe Customer ID
    });
    // 2. Redirect the user to Stripe's secure billing portal
    window.open(result.data?.url);
  };

  if (isLoading) { /* ... show loading spinner (from Chapter 6) ... */ }

  return (
    <div>
      {/* ... Display Free and Monthly Premium plans ... */}
      {userDetail?.isMember == false ? (
        <Button onClick={OnCheckoutClick} className="w-full mt-5">
          Get Started {/* Button to initiate new payment */}
        </Button>
      ) : (
        <Button onClick={onPaymentMange} className="w-full mt-5">
          Manage Payment {/* Button to go to billing portal */}
        </Button>
      )}
    </div>
  );
}
export default Upgrade;
```

**What's happening here?**

*   First, we use `GetUserDetail` (which talks to our database via [Data Persistence (Drizzle ORM)](02_data_persistence__drizzle_orm__.md)) to check if the logged-in user (`useUser()` from [User Authentication (Clerk)](01_user_authentication__clerk__.md)) is already a premium member (`isMember`).
*   If they are **not** a member, they see a "Get Started" button. When they click it, `OnCheckoutClick` runs. This function sends a request to our own backend API (`/api/payment/checkout`), telling it which `priceId` (which plan, like "Monthly Premium") they want. Our backend then asks Stripe for a special secure payment link, and `window.open` sends the user there.
*   If they **are** a member, they see a "Manage Payment" button. When they click it, `onPaymentMange` runs. This sends a request to our backend API (`/api/payment/manage-payment`), telling it to get a link to Stripe's secure "Billing Portal" for *their specific customer ID*. `window.open` then sends them to that portal.

Notice how our frontend never directly talks to Stripe; it always goes through our own backend APIs, which then talk to Stripe. This is a secure best practice.

## Under the Hood: Making Payments Work

Let's dive into how our backend communicates with Stripe to handle these payment requests.

### 1. Initiating a New Subscription (Checkout Session)

When a user clicks "Get Started," our backend API (specifically `app\api\payment\checkout\route.jsx`) steps in.

```javascript
// File: app\api\payment\checkout\route.jsx (Simplified)
import { NextResponse } from "next/server";
import Stripe from "stripe"; // The official Stripe library

export async function POST(req) {
    const stripe = new Stripe(process.env.STRIPE_SECRETE_KEY); // Connect to Stripe using our secret key

    const { priceId } = await req.json(); // Get the plan ID from our frontend

    // Ask Stripe to create a secure checkout session
    const session = await stripe.checkout.sessions.create({
        mode: 'subscription', // We want a recurring subscription
        line_items: [{ price: priceId, quantity: 1 }], // The plan and quantity
        success_url: process.env.HOST_URL + 'payment-success?session_id={CHECKOUT_SESSION_ID}', // Where to send user on success
        cancel_url: process.env.HOST_URL, // Where to send user if they cancel
    });

    return NextResponse.json(session); // Send the Stripe session URL back to our frontend
}
```

**Explanation:**

*   `new Stripe(process.env.STRIPE_SECRETE_KEY)`: This line connects our application securely to Stripe using a secret key (like a password) that only our backend knows.
*   `stripe.checkout.sessions.create`: This is the key command! We tell Stripe to create a new "checkout session."
    *   `mode: 'subscription'`: Tells Stripe we're setting up a recurring payment.
    *   `line_items`: Specifies *what* the user is buying (our `priceId` for the monthly plan).
    *   `success_url` and `cancel_url`: These are important! They tell Stripe where to send the user *back* to our application after they complete or cancel the payment process on Stripe's website.

Stripe then generates a unique, secure URL for this payment session and sends it back to our backend, which then passes it to our frontend to open in the user's browser.

Here's the sequence of events for a new subscription:

```mermaid
sequenceDiagram
    participant User
    participant Frontend as Atlas LMS Frontend
    participant BackendAPI as Atlas LMS Backend API (Checkout)
    participant Stripe as Stripe
    participant AtlasDB as Atlas DB

    User->>Frontend: Clicks "Get Started" (Upgrade)
    Frontend->>BackendAPI: POST /api/payment/checkout (sends priceId)
    BackendAPI->>Stripe: Request: create checkout session (priceId, success_url, cancel_url)
    Stripe-->>BackendAPI: Response: session object with secure checkout URL
    BackendAPI-->>Frontend: Sends secure checkout URL
    Frontend->>User: Redirects browser to Stripe's secure checkout page

    User->>Stripe: Enters payment details & confirms
    Stripe->>Stripe: Processes payment, creates subscription
    Stripe-->>User: Redirects browser to Atlas LMS success_url (payment-success page)
    Note over Stripe,AtlasDB: (Behind the scenes, Stripe prepares to notify our app via Webhook - see next section)
```

### 2. Managing an Existing Subscription (Billing Portal)

If a user is already a premium member and clicks "Manage Payment," our `app\api\payment\manage-payment\route.jsx` endpoint handles it.

```javascript
// File: app\api\payment\manage-payment\route.jsx (Simplified)
import { NextResponse } from "next/server";
import Stripe from "stripe";

export async function POST(req) {
    const stripe = new Stripe(process.env.STRIPE_SECRETE_KEY);
    const { customerId } = await req.json(); // Get Stripe's customer ID from our frontend

    // Ask Stripe to create a secure billing portal session for this customer
    const portalSession = await stripe.billingPortal.sessions.create({
        customer: customerId, // The specific customer ID
        return_url: process.env.HOST_URL, // Where to send user back after using portal
    });

    return NextResponse.json(portalSession); // Send the portal URL back to our frontend
}
```

**Explanation:**

*   `stripe.billingPortal.sessions.create`: This command tells Stripe to generate a unique, secure link to its "Customer Portal" for a specific customer (`customerId`). In this portal, users can update their payment method, view invoices, or cancel their subscription directly with Stripe.
*   `return_url`: This specifies where Stripe should send the user back after they're done in the portal.

### 3. Knowing What Happened: Stripe Webhooks (The Automatic Notifier)

The `success_url` and `cancel_url` redirect the user back to our app, but they don't *reliably* tell our app what happened with the payment (e.g., if a recurring payment succeeded next month, or if a user cancelled from the portal). This is where **Stripe Webhooks** are crucial!

Imagine Stripe is like a busy news reporter, and our app has a special "listening post." Whenever something important happens with a payment or subscription on Stripe's side (e.g., a payment succeeds, a subscription is cancelled, a payment fails), Stripe automatically sends a secure, real-time message to our listening post. Our `app\api\payment\webhook\route.jsx` file is this listening post.

```javascript
// File: app\api\payment\webhook\route.jsx (Simplified)
import { db } from "@/configs/db"; // Our database connection
import { USER_TABLE } from "@/drizzle/schema"; // Our user table
import { eq } from "drizzle-orm"; // Drizzle ORM tool
import { NextResponse } from "next/server";
import Stripe from "stripe";

export async function POST(req) {
  const stripe = new Stripe(process.env.STRIPE_SECRETE_KEY);
  let event;

  // IMPORTANT: Verify the webhook signature for security
  try {
    const rawBody = await req.text(); // Get the raw request body
    const signature = req.headers.get("stripe-signature");
    event = stripe.webhooks.constructEvent(rawBody, signature, process.env.STRIPE_WEB_HOOK_KEY);
  } catch (err) {
    console.log(`⚠️ Webhook signature verification failed:`, err.message);
    return new Response(`Webhook Error: ${err.message}`, { status: 400 });
  }

  // Handle different types of events from Stripe
  switch (event.type) {
    case 'checkout.session.completed':
      // User just paid for a new subscription!
      await db.update(USER_TABLE).set({ isMember: true }) // Update user to premium
        .where(eq(USER_TABLE.email, event.data.object.customer_details.email));
      break;

    case 'invoice.paid':
      // A recurring subscription payment was successful
      await db.update(USER_TABLE).set({ isMember: true }) // Ensure user is premium
        .where(eq(USER_TABLE.email, event.data.object.customer_email));
      break;

    case 'customer.subscription.deleted':
      // User cancelled their subscription
      await db.update(USER_TABLE).set({ isMember: false }) // Set user back to free
        .where(eq(USER_TABLE.customerId, event.data.object.customer));
      break;

    case 'invoice.payment_failed':
      // A recurring payment failed
      await db.update(USER_TABLE).set({ isMember: false }) // Set user back to free
        .where(eq(USER_TABLE.email, event.data.object.customer_email));
      break;

    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  return NextResponse.json({ received: true }); // Acknowledge receipt to Stripe
}
```

**Explanation:**

*   **Security (`stripe.webhooks.constructEvent`):** This is vital! Stripe sends a special signature with each webhook. We *must* verify this signature to make sure the message actually came from Stripe and hasn't been tampered with.
*   **`switch (event.type)`:** The `event.type` tells us *what kind of event* just happened on Stripe.
    *   `checkout.session.completed`: This is sent when a user successfully completes the initial payment for a *new* subscription. We update our `USER_TABLE` using [Data Persistence (Drizzle ORM)](02_data_persistence__drizzle_orm__.md) to set `isMember: true` for that user.
    *   `invoice.paid`: This is sent for *recurring* successful payments. We also update `isMember: true`.
    *   `customer.subscription.deleted`: This is sent when a user cancels their subscription (e.g., from Stripe's billing portal). We update `isMember: false`.
    *   `invoice.payment_failed`: This is sent if a recurring payment fails. We update `isMember: false`.

This webhook system ensures that our application's database (`isMember` status) is always in sync with the user's actual subscription status on Stripe, automatically and securely!

Here's the sequence for how a webhook updates our system:

```mermaid
sequenceDiagram
    participant Stripe as Stripe
    participant BackendAPI as Atlas LMS Backend API (Webhook)
    participant AtlasDB as Atlas DB

    Stripe->>BackendAPI: Sends Webhook Event (e.g., 'checkout.session.completed')
    BackendAPI->>BackendAPI: Verifies Stripe Signature (Security Check)
    BackendAPI->>BackendAPI: Parses Event Data (e.g., customer email, subscription status)
    BackendAPI->>AtlasDB: Updates USER_TABLE: Sets `isMember` to `true` (or `false` for cancellation)
    AtlasDB-->>BackendAPI: Confirmation
    BackendAPI-->>Stripe: Response: 200 OK (Acknowledges receipt)
```

## Conclusion

In this final chapter, we've explored **Stripe Payment Integration** in Atlas LMS AI. We learned how Stripe acts as our secure digital cashier and subscription manager, handling everything from initial upgrades to ongoing membership management. By using Stripe's Checkout Sessions, Billing Portal, and especially its powerful Webhooks, we ensure that our application can securely process payments and keep your user status (free vs. paid) accurate and up-to-date automatically.

This completes our journey through the core abstractions of the Atlas University LMS AI project! We've covered everything from user authentication and data storage to AI content generation, background task processing, UI design, and now, secure payments. You now have a foundational understanding of how all these pieces come together to create a powerful and seamless learning experience.

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)