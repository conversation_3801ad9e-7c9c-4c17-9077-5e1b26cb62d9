agent:
  config:
    max_tokens: 2000
    temperature: 0.1
  model: gpt-4.1-mini
  type: langgraph
isactive: 1 # 0 active, 1 test, 2 inactive
description:
  A bot developed by Atlas University in Istanbul, Turkey, designed to
  assist with academic research and scholarly information.
metadata:
  audience: researchers
  institution: Atlas University
  languages:
    - English
    - Turkish
  location: Istanbul, Turkey
  topics:
    - academic publications
    - research grants
    - scholarly resources
    - academic institutions
    - research methodologies
name: AtlasIQBot
prompts:
  query_prompt_path: atlasiq_bot/query.txt
  system_prompt_path: atlasiq_bot/system.txt
rate_limit:
  enabled: false
  requests_per_minute: 15
  requests_per_hour: 200
tools:
  - config:
      collection_name: atlasiq_documents
      persist_directory: ./chroma_db/atlasiq
      top_k: 8
      embedding_model: text-embedding-3-small
    enabled: true
    type: DocumentSearchTool
  - config:
      connection_string: mongodb://localhost:27017/
      database_name: academic_db
      default_collection: publications
      max_results: 15
      model: gpt-4.1-mini
      temperature: 0.0
      # Connection pool settings
      max_pool_size: 5
      min_pool_size: 1
      max_idle_time_ms: 30000
      wait_queue_timeout_ms: 5000
    enabled: false
    type: MongoDBQueryTool
  - config:
      allowed_tables:
        - researchers
        - institutions
        - grants
        - publications
      connection_string: sqlite:///C:\Users\<USER>\Desktop\WORK\Agentic RAG\tests\atlasiq_test_db.sqlite
      max_results: 50
    enabled: false
    type: SQLQueryTool
  - config:
      exclude_domains:
        - facebook.com
        - twitter.com
        - instagram.com
      include_domains:
        - edu
        - org
        - gov
        - ac.uk
      max_results: 5
      search_depth: basic
      priority_domain: "https://www.atlas.edu.tr/"
    enabled: true
    type: WebSearchTool
