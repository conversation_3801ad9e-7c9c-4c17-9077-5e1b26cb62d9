# Tutorial: agentic-workflow

The `agentic-workflow` project allows users to build and run **automated workflows**.
These workflows are like *visual recipes* made up of individual **Tasks**, which represent actions like scraping a website or using AI.
Users design workflows in a graphical **Flow Editor**, and the system handles the **Workflow Execution**, breaking down the steps into **Execution Phases** and following an **Execution Plan**.
Special **AI Agents**, powered by LLMs, are a type of Task and can be included in workflows, often requiring **Credentials** stored securely.
The system relies on a **Task & Executor Registry** to know how to define and run each Task.


**Source Repository:** [None](None)

```mermaid
flowchart TD
    A0["Workflows
"]
    A1["Tasks
"]
    A2["AI Agents
"]
    A3["Flow Editor
"]
    A4["Workflow Execution
"]
    A5["Execution Phases
"]
    A6["Execution Plan
"]
    A7["Task & Executor Registry
"]
    A8["Credentials
"]
    A0 -- "Contain" --> A1
    A3 -- "Edits" --> A0
    A4 -- "Runs" --> A0
    A0 -- "Generates" --> A6
    A4 -- "Follows" --> A6
    A4 -- "Creates" --> A5
    A7 -- "Defines" --> A1
    A4 -- "Looks up Executors" --> A7
    A1 -- "May require" --> A8
    A2 -- "Is a type of" --> A1
    A2 -- "Is registered in" --> A7
    A5 -- "Reports status to" --> A4
```

## Chapters

1. [Workflows
](01_workflows_.md)
2. [Tasks
](02_tasks_.md)
3. [AI Agents
](03_ai_agents_.md)
4. [Flow Editor
](04_flow_editor_.md)
5. [Workflow Execution
](05_workflow_execution_.md)
6. [Execution Plan
](06_execution_plan_.md)
7. [Execution Phases
](07_execution_phases_.md)
8. [Task & Executor Registry
](08_task___executor_registry_.md)
9. [Credentials
](09_credentials_.md)


---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)