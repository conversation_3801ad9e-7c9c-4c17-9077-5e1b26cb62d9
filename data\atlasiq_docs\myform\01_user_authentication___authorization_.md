# Chapter 1: User Authentication & Authorization

Welcome to the very first chapter of the `myform` tutorial! We're starting with a fundamental concept that's super important for almost any application: **User Authentication & Authorization**.

Imagine `myform` is a fancy club. You wouldn't want just anyone walking in and messing with the DJ booth or accessing the VIP lounge, right? That's exactly what User Authentication & Authorization helps us with!

## What is Authentication & Authorization?

Think of it like this:

*   **Authentication** is like the **bouncer** at the club's entrance. Their job is to check your ID and make sure you are who you say you are. Are you really "Alice" from Atlas University? Show your ID!
*   **Authorization** is like the **VIP list** or **access rules** once you're inside. Just because you're "<PERSON>" doesn't mean you can go everywhere. Are you a regular guest, allowed in the main area? Or are you a "VIP Admin" allowed backstage and in the manager's office?

In `myform`, this system handles a few key things:

1.  **Logging In:** How users prove their identity, especially by connecting to **Atlas University's system**.
2.  **Knowing Who You Are:** Keeping track of your unique `User` profile (your "ID card") in our database.
3.  **What You Can Do:** Deciding if you're a regular user who can create forms, or an "admin" with special powers.
4.  **Staying Logged In:** Using "secure tokens" (like temporary passes) so you don't have to show your ID every time you move from one page to another.

## Our Use Case: Logging In to Create a Form

Let's walk through a common scenario: you, as an Atlas University student or faculty member, want to log into `myform` to create a new survey for your class. This requires you to first "authenticate" yourself.

### How You Log In (The User Experience)

When you visit `myform`, if you're not logged in, you'll be directed to the login page (`/login`). This page uses a component called `LoginForm`.

Here's a simplified look at what you see:

```tsx
// components/auth/LoginForm.tsx (Simplified)
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useAuth } from "@/contexts/AuthContext"; // Our special Auth manager

export default function LoginForm() {
  const { login, isLoading } = useAuth(); // Get login function from our Auth manager
  const { register, handleSubmit } = useForm(); // Tool for managing form inputs

  const onSubmit = async (data) => {
    // When you click login:
    await login(data.username, data.password); // Call the login function
    // If successful, you'll be redirected!
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <input type="text" {...register("username")} placeholder="Your Atlas Username" />
      <input type="password" {...register("password")} placeholder="Your Atlas Password" />
      <button type="submit" disabled={isLoading}>
        {isLoading ? "Logging in..." : "Sign In"}
      </button>
    </form>
  );
}
```

**Explanation:**
This `LoginForm` is what you interact with. You type your Atlas University username and password. When you click "Sign In", it takes your details and hands them over to something called the `AuthContext` (our "Auth Manager"), which then handles the actual login process behind the scenes.

### The Auth Manager (`AuthContext`)

The `AuthContext` is like your wallet, keeping track of whether you have a valid "club pass" (a token) and your "ID card" (your user information). It provides the `login` function that the `LoginForm` calls.

```tsx
// contexts/AuthContext.tsx (Simplified)
import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface AuthUser { /* User details like email, name, role */ }
interface AuthContextType { /* Functions like login, logout */ }

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null); // Stores logged-in user info
  const [isLoading, setIsLoading] = useState(true); // Is login/logout happening?

  useEffect(() => {
    // When the app starts, check if you're already logged in
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      setUser(JSON.parse(storedUser)); // Load user from your browser's memory
    }
    setIsLoading(false);
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    // This is where the magic happens! We talk to our server.
    const response = await fetch('/api/atlas-auth/login', { /* ...send data... */ });
    const data = await response.json();
    if (response.ok) {
      localStorage.setItem('user', JSON.stringify(data)); // Save user data in your browser
      setUser(data); // Set user state
      // Also set cookies for server-side checks!
      document.cookie = `token=${data.token}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Strict`;
      return true; // Login successful!
    }
    setIsLoading(false);
    return false; // Login failed!
  };

  const logout = async () => { /* ...clear user data... */ };

  return (
    <AuthContext.Provider value={{ user, isLoading, isAuthenticated: !!user, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() { return useContext(AuthContext)!; }
```

**Explanation:**
The `AuthContext` is crucial. When you hit "Sign In", `login` is called. This function doesn't directly talk to Atlas University. Instead, it sends your username and password to **our own `myform` server**. If successful, it stores your user information and a special "token" (your temporary pass) in your browser's local storage and in "cookies" (small data pieces used by websites). This token is how `myform` remembers you're logged in without you having to re-enter your details on every page.

## Under the Hood: How it All Works (The Club's Internal Systems)

Let's peek behind the scenes to see how `myform`'s authentication and authorization systems are built.

### The Login Flow: A Step-by-Step Journey

Here's how the login process works from start to finish:

```mermaid
sequenceDiagram
    participant User as You (Browser)
    participant LoginForm as Login Form
    participant AuthContext as Auth Manager (Browser)
    participant MyFormServer as MyForm Server (API)
    participant AtlasAPIs as Atlas University API
    participant MyFormDB as MyForm Database

    User->>LoginForm: Enters username/password & Clicks "Sign In"
    LoginForm->>AuthContext: Calls login(username, password)
    AuthContext->>MyFormServer: Sends login request to /api/atlas-auth/login
    MyFormServer->>AtlasAPIs: Authenticates with Atlas University API
    AtlasAPIs-->>MyFormServer: Returns Atlas token & User Data
    MyFormServer->>MyFormDB: Finds or Creates User Profile
    MyFormDB-->>MyFormServer: User Profile (with _id)
    MyFormServer->>MyFormServer: Creates MyForm's JWT Token
    MyFormServer-->>AuthContext: Returns User Profile + MyForm Token (in response & cookies)
    AuthContext->>AuthContext: Stores User Profile in localStorage
    AuthContext->>User: Updates UI (Logged in)
    AuthContext->>LoginForm: Notifies login success
    LoginForm->>User: Redirects to Dashboard (or requested page)
```

**Explanation of the Flow:**
1.  **You** enter your details into the `LoginForm`.
2.  The `LoginForm` tells the `AuthContext` to start the `login` process.
3.  The `AuthContext` sends your details to our `MyFormServer` at the `/api/atlas-auth/login` address.
4.  Our `MyFormServer` then talks to the official `Atlas University API` to verify your username and password. This is super secure because we don't store your Atlas password directly!
5.  If Atlas says "Yep, they're valid!", they send back an "Atlas Token" and some basic user data.
6.  Our `MyFormServer` then checks its own `MyForm Database`. Does a user with this Atlas ID or email already exist?
    *   If yes, it updates your `User` profile (e.g., last login time, new name/photo if updated).
    *   If no, it creates a brand new `User` profile for you in our database. This profile stores your name, email, role (`user` or `admin`), and your unique ID within `myform`.
7.  Crucially, our `MyFormServer` then creates its *own* special "JWT Token" for you. This is your temporary "Myform Club Pass."
8.  The server sends this Myform Token and your updated user profile back to the `AuthContext` in your browser. It also sets this token as a "cookie" (a small piece of data websites store) which is important for server-side checks.
9.  The `AuthContext` saves your user profile in your browser's `localStorage` and updates its internal state so the app knows you're logged in.
10. Finally, the `LoginForm` sees that you're logged in and redirects you to the dashboard or the page you were trying to access.

Now, let's look at some key code files involved in this process.

### The Gatekeeper: `middleware.ts`

This file acts like the main bouncer at the entire `myform` club entrance. Before *any* page loads, this `middleware` checks if you have a valid "pass" (token) to enter.

```typescript
// middleware.ts (Simplified)
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

const publicPaths = ['/login', '/api/atlas-auth', '/sign-in']; // Pages anyone can access

function isPublicPath(path: string): boolean {
  // Check if the page is public (like the login page itself)
  return publicPaths.some(publicPath => path.startsWith(publicPath));
}

export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;
  
  if (isPublicPath(path)) {
    return NextResponse.next(); // Let public pages load
  }
  
  // Get your "pass" (token) and email from cookies
  const token = request.cookies.get('token')?.value;
  const userEmail = request.cookies.get('userEmail')?.value;
  
  if (!token && !userEmail) {
    // If no pass found, redirect to login page (the bouncer sends you away)
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('redirect', path); // Remember where you wanted to go!
    return NextResponse.redirect(loginUrl);
  }
  
  // If a pass (token or email) is found, let you through!
  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!_next/static|_next/image|favicon.ico).*)'] // Apply to almost all paths
};
```

**Explanation:**
The `middleware.ts` is the very first piece of code that runs when you try to visit any page on `myform`. It checks if the page is public (like the `/login` page itself). If not, it looks for your "token" (your temporary pass) or `userEmail` in your browser's cookies. If it doesn't find them, it sends you directly to the login page. This ensures only logged-in users can access protected content.

### The Authentication Bridge: `app/api/atlas-auth/login/route.ts`

This is the central processing unit for logging in. It's an "API Route" that `AuthContext` talks to. It handles the communication with Atlas University and our database.

```typescript
// app/api/atlas-auth/login/route.ts (Simplified)
import { NextResponse } from "next/server";
import axios from "axios"; // Tool to make requests to other APIs
import jwt from "jsonwebtoken"; // Tool to create our special tokens
import connectDB from "@/lib/mongodb"; // Connects to our database
import User from "@/models/User"; // Our User "ID card" model

export async function POST(req: Request) {
  const { username, password } = await req.json();
  const apiBaseUrl = process.env.ATLAS_API_BASE_URL; // Atlas API address
  const jwtSecret = process.env.JWT_SECRET; // Our secret key for tokens

  // 1. Authenticate with Atlas University API
  const loginResponse = await axios.post(`${apiBaseUrl}/Login/Login`, {
    username, password, applicationName: "MYATLAS"
  });
  if (!loginResponse.data.webToken || loginResponse.data.isLogin !== true) {
    return NextResponse.json({ message: "Invalid credentials" }, { status: 401 });
  }
  const atlasToken = loginResponse.data.webToken;

  // 2. Get detailed user data from Atlas (using the Atlas token)
  const userDataResponse = await axios.get(`${apiBaseUrl}/Login/LoginData`, {
    headers: { Authorization: `Bearer ${atlasToken}` }
  });
  const atlasUserData = userDataResponse.data;
  const email = atlasUserData.email || username;
  const atlasUserId = atlasUserData.userId;

  // 3. Synchronize with our MyForm Database
  await connectDB();
  let user = await User.findOne({ atlasUserId }) || await User.findOne({ email });

  if (user) {
    // Update existing user (e.g., last login, name updates)
    user.lastLoginAt = new Date();
    user.atlasToken = atlasToken;
    await user.save();
  } else {
    // Create new user in our database
    user = await User.create({ email, atlasUserId, isExternal: true, /* ...other fields... */ });
  }

  // 4. Create our own JWT token for MyForm
  const myFormToken = jwt.sign(
    { userId: user._id, email: user.email, role: user.role },
    jwtSecret, { expiresIn: '7d' } // Expires in 7 days
  );

  // 5. Set cookies and return user data
  const response = NextResponse.json({ ...user.toObject(), token: myFormToken });
  response.cookies.set('token', myFormToken, { httpOnly: true, secure: true, maxAge: 7 * 24 * 60 * 60 });
  response.cookies.set('userEmail', user.email, { httpOnly: true, secure: true, maxAge: 7 * 24 * 60 * 60 });
  return response;
}
```

**Explanation:**
This is where your Atlas University credentials are sent.
1.  It first uses `axios` to send your `username` and `password` directly to the `Atlas University API`.
2.  If Atlas confirms your identity, it sends back an `atlasToken`. We then use this `atlasToken` to get more detailed information about you (like your full name, email, etc.) from another Atlas API endpoint.
3.  Next, it connects to our `MyForm Database` (which uses MongoDB, as we'll see in [Database Layer (MongoDB/Mongoose)](07_database_layer__mongodb_mongoose_.md)). It checks if a `User` with your Atlas ID or email already exists. If so, it updates your profile in our database. If not, it creates a new `User` record for you.
4.  Finally, it creates a special `myFormToken` (a "JSON Web Token" or JWT) using your `myform` user ID and a secret key. This `myFormToken` is your main "pass" for our application. It's valid for 7 days.
5.  It then sends this `myFormToken` back to your browser and sets it as a "cookie" so your browser remembers you're logged in.

### The User's ID Card: `models/User.ts`

Our `MyForm Database` needs a way to store all your information. This is defined by the `User` **model**. It's like the template for your ID card in our system.

```typescript
// models/User.ts (Simplified)
import mongoose, { Schema, model, models } from 'mongoose';

export interface IUser {
  email: string;
  firstName: string;
  lastName: string;
  role: 'user' | 'admin'; // Are you a regular user or an admin?
  profileImage?: string;
  atlasUserId?: string; // Your unique ID from Atlas University
  isExternal: boolean; // Did you log in via an external system (Atlas)?
  lastLoginAt?: Date;
  // ...other fields like nameSurname, photoName, etc.
}

const userSchema = new Schema<IUser>({
  email: { type: String, required: true, unique: true },
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  role: { type: String, enum: ['user', 'admin'], default: 'user' },
  profileImage: { type: String },
  atlasUserId: { type: String, unique: true, sparse: true },
  isExternal: { type: Boolean, default: false },
  lastLoginAt: { type: Date },
  // ...define other fields
});

const User = models.User || model('User', userSchema);
export default User;
```

**Explanation:**
This code defines what information we store about each user in our MongoDB database. It includes basic things like your email, first name, last name, and your `role` (`user` or `admin`). It also specifically stores your `atlasUserId` to link your `myform` account to your Atlas University identity, and `isExternal` to know if you logged in through their system. This `User` model is critical for recognizing you and storing your personalized settings and forms.

### The Token Verifier: `lib/auth.ts` and `lib/actions/user.actions.ts`

Once you have a `myFormToken`, pages on `myform` need to verify it to make sure it's valid and to find out who you are. This happens behind the scenes.

```typescript
// lib/auth.ts (Simplified)
import jwt, { JwtPayload } from 'jsonwebtoken';
import { cookies } from 'next/headers'; // Access cookies on the server

export interface AuthTokenPayload extends JwtPayload {
  userId: string;
  email?: string;
  role?: string;
}

export async function verifyAuth(token: string): Promise<AuthTokenPayload> {
  const jwtSecret = process.env.JWT_SECRET;
  // This line checks if the token is valid and hasn't been tampered with.
  const decoded = jwt.verify(token, jwtSecret) as AuthTokenPayload;
  return decoded; // Returns your user ID, email, and role from the token
}

export async function getServerSession(): Promise<AuthTokenPayload | null> {
  // This function is used by our server components to get your session info
  const cookieStore = await cookies();
  const token = cookieStore.get('token')?.value; // Get the token from cookies
  if (!token) return null;
  try {
    return await verifyAuth(token); // Verify it!
  } catch (error) {
    return null;
  }
}
```

```typescript
// lib/actions/user.actions.ts (Simplified)
import { verify } from 'jsonwebtoken';
import { cookies } from 'next/headers';
import connectDB from "@/lib/mongodb";
import User from "@/models/User";

export async function getCurrentUser() {
  let token;
  let userEmail;
  try {
    const cookieStore = await cookies();
    token = cookieStore.get('token')?.value;
    userEmail = cookieStore.get('userEmail')?.value;
  } catch (error) { return null; }

  if (!token) { // If no token, try to find user by email from cookies
    if (userEmail) {
      await connectDB();
      return await User.findOne({ email: userEmail });
    }
    return null;
  }

  try {
    const jwtSecret = process.env.JWT_SECRET;
    const decoded = verify(token, jwtSecret) as { userId: string; email: string };
    
    await connectDB();
    const user = await User.findById(decoded.userId); // Find the user in our database by ID
    
    return user; // Return the full user object
  } catch (tokenError) {
    // If token is invalid/expired, try finding user by email as a fallback
    if (userEmail) {
      await connectDB();
      return await User.findOne({ email: userEmail });
    }
    return null;
  }
}
```

**Explanation:**
These files are used by our server to understand who is making a request.
*   `verifyAuth`: This function takes a `myFormToken` and checks if it's genuine and hasn't expired or been changed. It decodes the token to get your `userId`, `email`, and `role`.
*   `getServerSession`: This is a helper for server-side pages to easily get your session information (who you are) from the cookies without needing to write the `cookieStore` logic every time.
*   `getCurrentUser`: This function is the primary way our server-side code finds out who the current user is. It first tries to use your `token`. If the token is missing or invalid, it tries to use the `userEmail` from cookies as a backup to find your `User` profile in the database. This ensures that even if there's a minor hiccup with the token, we can still identify you.

### Client-side cookie persistence: `public/form-auth-handler.js`

This simple script is included directly in our HTML pages to make sure that even if the page refreshes, your login cookies (like `token` and `userEmail`) are properly set from `localStorage`. It acts as a safety net to ensure a smooth logged-in experience. If cookies are missing, but `localStorage` has user data, it helps re-establish the cookies. If no user data is found at all, it redirects to `/login`.

```javascript
// public/form-auth-handler.js (Simplified)
(function() {
  function checkAuthStatus() {
    const userStr = localStorage.getItem('user'); // Get user data from browser storage
    if (!userStr) {
      // If no user data, redirect to login!
      window.location.href = `/login?redirect=${encodeURIComponent(window.location.pathname)}`;
      return;
    }
    
    try {
      const user = JSON.parse(userStr);
      // If token/email cookies are missing, set them from stored user data
      if (!document.cookie.includes('token=') && user.token) {
        document.cookie = `token=${user.token}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Strict`;
      }
      if (!document.cookie.includes('userEmail=') && user.email) {
        document.cookie = `userEmail=${user.email}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Strict`;
      }
    } catch (error) {
      localStorage.removeItem('user'); // Clear corrupted data
      // Redirect to login on error
      window.location.href = `/login?redirect=${encodeURIComponent(window.location.pathname)}`;
    }
  }
  window.addEventListener('load', checkAuthStatus); // Run when page loads
})();
```

**Explanation:**
This small JavaScript file runs in your browser. Its main job is to act as a "cookie guardian." If you're logged in (meaning your user data is in `localStorage`), but for some reason, the important `token` or `userEmail` cookies are missing, this script will put them back. This helps `myform` maintain your logged-in session smoothly, especially when pages refresh. If it finds no login data at all, it ensures you're redirected to the login page.

## Conclusion

In this chapter, we explored the crucial system of **User Authentication & Authorization** in `myform`. We learned that it's like a club's bouncer and VIP list, ensuring only authorized users can access protected parts of the application and defining what they can do. We saw how users log in through Atlas University's system, how `myform` keeps track of their `User` profiles in the database, and how secure tokens keep them logged in across the site.

Next, we'll dive into how `myform` leverages the power of AI to make creating forms easier and smarter.

[Next Chapter: AI-Powered Form Generation](02_ai_powered_form_generation_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)