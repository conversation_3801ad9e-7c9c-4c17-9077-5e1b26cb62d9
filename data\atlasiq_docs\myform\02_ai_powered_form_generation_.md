# Chapter 2: AI-Powered Form Generation

Welcome back to the `myform` tutorial! In [Chapter 1: User Authentication & Authorization](01_user_authentication___authorization_.md), we learned how `myform` acts like a secure club, making sure only authorized users can enter and use its features. Now that you're logged in, what can you actually *do* with `myform`? Get ready for the exciting part: **AI-Powered Form Generation**!

Imagine you need to create a new form or survey, but you don't want to spend time dragging and dropping fields, choosing types, and writing labels. This is where the magic of AI comes in!

## What is AI-Powered Form Generation?

This is the "smart assistant" feature of `myform`. Instead of building forms manually, you simply tell `myform` what you want, and Artificial Intelligence (AI) handles the complex work of designing the form's structure for you.

Think of it like this:

*   **Before AI:** You would grab a blank piece of paper and draw out your form, deciding on each question, input box, and checkbox.
*   **With AI:** You tell your smart assistant, "I need a contact form with name, email, and a message box." *Poof!* The AI instantly creates the technical blueprint for that form, complete with all the right fields and settings.

It's designed to save you a lot of time and effort, especially for common tasks. `myform` also has a special trick: it can even transform uploaded documents (like PDFs or images) into ready-to-use form structures, making it incredibly powerful for digitizing existing paperwork!

## Our Use Case: Creating a Contact Form with AI

Let's walk through a common scenario: you want to quickly create a contact form for your department's website.

### How You Use It (The User Experience)

To start, you'd navigate to the "AI Form Builder" section in `myform` (which corresponds to the `app/features/ai-form-builder/generate/page.tsx` file).

Here's what you'll see on the screen:

```typescript
// app/features/ai-form-builder/generate/page.tsx (Simplified)
'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';

export default function GeneratePage() {
  const [prompt, setPrompt] = useState(''); // What you describe
  const [generatedForm, setGeneratedForm] = useState<any>(null); // What AI creates
  const [loading, setLoading] = useState(false); // Is AI working?

  const handleGenerateContent = async () => {
    setLoading(true);
    // This is where we tell our server what to do!
    const response = await fetch("/api/generate-form", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        prompt, // Your description goes here
        provider: 'gemini', // Which AI brain to use (e.g., Gemini)
        model: 'gemini-1.5-pro', // A specific model of that AI
        type: 'form', // We want a "form" not a "survey"
      }),
    });
    const data = await response.json();
    setGeneratedForm(data); // Display the form AI made
    setLoading(false);
  };

  return (
    <div>
      <textarea
        value={prompt}
        onChange={(e) => setPrompt(e.target.value)}
        placeholder="Describe your form, e.g., 'a contact form with name, email, and message'"
      />
      <button onClick={handleGenerateContent} disabled={loading}>
        {loading ? 'Creating...' : 'Create Form with AI'}
      </button>

      {generatedForm && (
        <form>
          <h3>{generatedForm.name}</h3>
          <p>{generatedForm.description}</p>
          {generatedForm.fields.map((field: any, index: number) => (
            <div key={index}>
              <label>{field.label}</label>
              <input type={field.type} name={field.name} required={field.required} />
            </div>
          ))}
          <button type="submit">Submit</button>
        </form>
      )}
    </div>
  );
}
```

**Explanation:**

1.  **Your Description (`prompt`):** You type a simple sentence like "a contact form with name, email, and message" into the `textarea`. This is your `prompt` – what you "ask" the AI to do.
2.  **The "Create" Button:** When you click the "Create Form with AI" button, the `handleGenerateContent` function kicks into action.
3.  **Talking to the Server:** This function sends your `prompt`, along with details about which AI `provider` (like Gemini) and `model` to use, to our `myform` server. It specifically calls the `/api/generate-form` address.
4.  **Displaying the Form:** Once the server responds with the AI-generated form data, `setGeneratedForm(data)` updates the screen, showing you a live preview of your new form!

## Under the Hood: How AI-Powered Generation Works

Now, let's peek behind the curtain and see what happens when you click that "Create Form with AI" button.

### The Journey: Step-by-Step AI Form Creation

This diagram shows the flow of information when you ask `myform` to generate a form:

```mermaid
sequenceDiagram
    participant You as Your Browser
    participant UI as Generate Page (Frontend)
    participant MyFormServer as MyForm Server (Backend API)
    participant AIProvider as OpenAI or Gemini (External AI)

    You->>UI: Type form description & Click "Create"
    UI->>MyFormServer: Send POST request to /api/generate-form (with prompt)
    MyFormServer->>AIProvider: Send prompt to AI (e.g., "Create contact form JSON...")
    AIProvider-->>MyFormServer: Return Form Blueprint (JSON)
    MyFormServer-->>UI: Send Form Blueprint to browser
    UI->>You: Display Editable Form
```

**Explanation of the Flow:**

1.  **You** tell the `UI` (the Generate Page) what kind of form you want.
2.  The `UI` sends this request as a `POST` (meaning it's sending data) to a special address on our `MyFormServer`: `/api/generate-form`.
3.  The `MyFormServer` takes your description and asks the `AIProvider` (like OpenAI or Gemini) to create a form blueprint based on it.
4.  The `AIProvider` processes your request and sends back a structured "Form Blueprint" – typically a JSON object that defines all the fields, their types, labels, and options.
5.  The `MyFormServer` receives this blueprint and sends it back to your `UI`.
6.  Finally, your `UI` receives this blueprint and builds the form right on your screen, ready for you to use or customize!

### The Brain: `app/api/generate-form/route.ts`

This file is the main "brain" of our AI form generation. It's an API route that connects your browser's request to the powerful AI models.

```typescript
// app/api/generate-form/route.ts (Simplified)
import { NextResponse } from 'next/server';
import OpenAI from 'openai'; // For OpenAI models
import { GoogleGenerativeAI } from '@google/generative-ai'; // For Gemini models

// This function defines what kind of JSON structure we want from the AI
function getSystemPrompts(language: string, type: 'form' | 'survey'): string {
  // A detailed instruction for the AI to follow
  return `You are a professional form builder assistant. Your task is to create a detailed JSON structure for a ${type} based on the user's description. Return ONLY valid JSON without any explanations or markdown formatting. The JSON structure should be:
{
  "name": "Form Name",
  "description": "Form description",
  "fields": [
    {
      "type": "text|email|number|select|radio|checkbox",
      "label": "Field Label",
      "name": "fieldName",
      "required": true|false,
      "options": [] // for select/radio/checkbox
    }
  ]
}`;
}

// Function to talk to OpenAI
async function generateWithOpenAI(prompt: string, model: string, type: 'form' | 'survey'): Promise<string | null> {
  const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY }); // Initialize OpenAI with our secret key
  const systemPrompt = getSystemPrompts('en', type); // Get instructions for the AI

  const response = await openai.chat.completions.create({
    model: model,
    messages: [ // These are the "messages" we send to the AI
      { role: 'system', content: systemPrompt }, // Our instructions
      { role: 'user', content: prompt }, // Your request
    ],
    temperature: 0.7, // How creative/random the AI should be
  });
  return response.choices[0]?.message?.content || null; // Return AI's JSON response
}

// Function to talk to Gemini (similar logic to OpenAI)
async function generateWithGemini(prompt: string, model: string, type: 'form' | 'survey'): Promise<string | null> {
  const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY as string); // Initialize Gemini
  const genModel = genAI.getGenerativeModel({ model: model });
  const systemPrompt = getSystemPrompts('en', type);

  const result = await genModel.generateContent([
    systemPrompt, // Our instructions
    prompt // Your request
  ]);
  return result.response.text(); // Return AI's JSON response
}

// The main function that handles requests to /api/generate-form
export async function POST(request: Request) {
  const body = await request.json();
  const { prompt, provider, model, type = 'form' } = body;

  let jsonResponse: string | null = null;
  if (provider === 'openai') {
    jsonResponse = await generateWithOpenAI(prompt, model, type);
  } else if (provider === 'gemini') {
    jsonResponse = await generateWithGemini(prompt, model, type);
  } else {
    return NextResponse.json({ error: "Invalid AI provider" }, { status: 400 });
  }

  // Ensure the AI's response is valid JSON and transform it if needed
  try {
    const formData = JSON.parse(jsonResponse || '{}'); // Parse the AI's JSON
    // ... further validation and transformation to fit our database schema ...
    return NextResponse.json(formData); // Send the structured form data back
  } catch (error) {
    console.error("Error parsing AI response:", error);
    return NextResponse.json({ error: "Failed to parse AI response" }, { status: 500 });
  }
}
```

**Explanation:**

1.  **`getSystemPrompts`:** This is crucial! It tells the AI *exactly* what kind of output format we expect (a JSON object with specific `name`, `description`, and `fields`). This helps the AI understand that we don't want a poem about forms, but a precise technical blueprint.
2.  **`generateWithOpenAI` and `generateWithGemini`:** These functions are responsible for communicating with the actual AI services (OpenAI or Google's Gemini). They take your `prompt` (your description) and the `systemPrompt` (our instructions for the AI) and send them to the AI.
3.  **The `POST` Function:** When your browser sends a request to `/api/generate-form`, this `POST` function is activated. It reads your `prompt`, decides whether to use OpenAI or Gemini based on your choice, calls the appropriate AI function, and then takes the AI's JSON response.
4.  **Parsing the Response:** It then tries to `JSON.parse` the AI's response. This converts the text-based JSON that AI sends back into a usable JavaScript object. This object is then sent back to your browser, which displays the form.

### The Document Reader: `app/api/analyze-document/route.ts`

`myform` can also turn documents into forms! This is handled by a similar API route, `app/api/analyze-document/route.ts`.

```typescript
// app/api/analyze-document/route.ts (Simplified)
import { NextResponse } from 'next/server';
import OpenAI from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Similar prompt but for analyzing documents
const FORM_ANALYSIS_PROMPT = `Analyze this document and create a form structure. Return ONLY a JSON object... (same format as before)`;

async function analyzeWithOpenAI(base64Image: string, model: string, apiKey: string) {
  const openai = new OpenAI({ apiKey: apiKey });
  const response = await openai.chat.completions.create({
    model: model,
    messages: [
      {
        role: "user",
        content: [
          { type: "text", text: FORM_ANALYSIS_PROMPT },
          { type: "image_url", image_url: { url: `data:image/jpeg;base64,${base64Image}` } }
        ]
      }
    ],
    max_tokens: 4000,
    response_format: { type: "json_object" }
  });
  return response.choices[0]?.message?.content;
}

// The main function that handles requests to /api/analyze-document
export async function POST(req: Request) {
  const formData = await req.formData();
  const file = formData.get('file') as File;
  const provider = formData.get('provider') as string;

  const bytes = await file.arrayBuffer();
  const buffer = Buffer.from(bytes);
  const base64Image = buffer.toString('base64'); // Convert file to base64 for AI

  let response;
  if (provider === 'openai') {
    response = await analyzeWithOpenAI(base64Image, 'gpt-4-vision', process.env.OPENAI_API_KEY as string);
  } else if (provider === 'gemini') {
    // Simplified: Gemini's vision model could also be used here
    response = await generateWithGemini(FORM_ANALYSIS_PROMPT, 'gemini-1.5-pro', base64Image, process.env.GEMINI_API_KEY as string);
  }

  try {
    const parsedResponse = JSON.parse(response || '{}');
    // ... transform to myform's internal structure ...
    return NextResponse.json(parsedResponse);
  } catch (error) {
    console.error("Error analyzing document:", error);
    return NextResponse.json({ error: "Failed to analyze document" }, { status: 500 });
  }
}
```

**Explanation:**

This API route works very similarly to `generate-form`, but with one key difference:
1.  **File Input:** Instead of a text `prompt`, it receives an uploaded `file` (like a PDF or image).
2.  **Base64 Conversion:** This file is converted into a `base64Image` string. This is a common way to represent binary data (like images) as text so it can be sent to APIs.
3.  **Vision AI:** The AI models used here (like `gpt-4-vision` from OpenAI or Gemini's vision models) are specially designed to "see" and understand content from images and PDFs. They use the `FORM_ANALYSIS_PROMPT` to guide them in identifying form fields within the document.
4.  **JSON Output:** Just like before, the AI returns a JSON blueprint of the form it "saw" in the document.

### API Keys and Behind-the-Scenes Configuration

You might notice that the AI functions use `process.env.OPENAI_API_KEY` and `process.env.GEMINI_API_KEY`. These are special "secret keys" that allow `myform` to communicate with OpenAI and Gemini services. These keys are crucial for security and billing, and their handling is a core part of `myform`'s design. We'll dive much deeper into how these API keys are managed in [Chapter 6: External API Key Handling](06_external_api_key_handling_.md).

## Conclusion

In this chapter, we unveiled the "magic" of **AI-Powered Form Generation** in `myform`. We saw how you can simply describe the form you need or upload an existing document, and `myform` leverages advanced AI (like OpenAI and Gemini) to instantly create a ready-to-use form structure. This powerful feature dramatically reduces the manual effort involved in form design.

Next, we'll explore what happens *after* your form is generated: how `myform` helps you manage its entire lifecycle, from creation to publication and beyond.

[Next Chapter: Form & Survey Lifecycle Management](03_form___survey_lifecycle_management_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)