# Chapter 6: Execution Plan

Welcome back! In the [previous chapter](05_workflow_execution_.md), we saw what happens when you run a workflow – the system kicks off a **[Workflow Execution](05_workflow_execution_.md)** to perform the automated task defined by your visual blueprint in the [Flow Editor](04_flow_editor_.md).

But how does the system know *exactly* what order to run the individual [Tasks](02_tasks_.md) and [AI Agents](03_ai_agents_.md) in your workflow? You drew connections (edges) in the [Flow Editor](04_flow_editor_.md), but a computer needs a strict list of instructions. It can't just "look" at the diagram like you can.

This is where the **Execution Plan** comes in.

### What is the Execution Plan?

Think back to our recipe analogy. You might have a visual recipe diagram showing ingredients connecting to mixing bowls, bowls going into an oven, and finally, a cake coming out. But when you actually cook, you follow step-by-step instructions: "Preheat oven," "Mix dry ingredients," "Mix wet ingredients," "Combine," "Pour into pan," "Bake for X minutes."

The **Execution Plan** is the `agentic-workflow` system's version of that strict, step-by-step instruction list.

It's a structured sequence of steps, or **phases**, that is derived directly from your workflow's visual definition (the nodes and edges you arranged in the [Flow Editor](04_flow_editor_.md)). Its main purpose is to determine the correct order in which the tasks must be executed, ensuring that dependencies are respected.

Dependencies mean: if Task B needs the output of Task A as its input, Task A *must* run and complete successfully before Task B can start. The Execution Plan guarantees this ordering.

### How is the Execution Plan Created?

The Execution Plan isn't something you create directly. It's **generated automatically** by the system from your visual workflow definition (the nodes and edges). This generation happens when you:

1.  **Publish** a workflow: When you mark a workflow as `PUBLISHED`, the system creates the Execution Plan and saves it alongside the definition.
2.  **Run a Draft** workflow: If you run a workflow that is still a `DRAFT`, the system generates the Execution Plan on-the-fly just for that specific run.

The core logic for creating this plan is handled by the `FlowToExecutionPlan` function.

```typescript
// Inside lib/workflow/executionPlan.ts (simplified signature)
export function FlowToExecutionPlan(
  nodes: AppNode[], // The list of task nodes from the visual editor
  edges: Edge[] // The list of connections from the visual editor
): FlowToExecutionPlanType {
  // ... logic to generate the plan ...
}
```

This function takes the raw data from your visual design (the nodes and how they are connected by edges) and converts it into the structured Execution Plan.

Let's see where this happens. When you trigger a workflow run, the `RunWorkflow` action calls `FlowToExecutionPlan` if it's a draft, or loads the pre-calculated plan if it's published:

```typescript
// Inside actions/workflows/runWorkflow.ts (simplified)
export async function RunWorkflow(form: { workflowId: string; flowDefinition?: string }) {
  // ... workflow lookup ...

  let executionPlan: WorkflowExecutionPlan;
  let workflowDefinition = flowDefinition;

  if (workflow.status === WorkflowStatus.PUBLISHED) {
    // If PUBLISHED, load the saved plan
    executionPlan = JSON.parse(workflow.executionPlan!);
    workflowDefinition = workflow.definition;
  } else {
    // If DRAFT, generate the plan from the current definition
    const flow = JSON.parse(flowDefinition!);
    const result = FlowToExecutionPlan(flow.nodes, flow.edges); // Generate the plan here!
    if (result.error) {
      throw new Error("flow definition not valid: " + result.error.type); // Handle errors
    }
    executionPlan = result.executionPlan!;
  }

  // ... create WorkflowExecution record using the executionPlan ...
  // ... call ExecuteWorkflow(execution.id); ...
}
```

Similarly, the `PublishWorkflow` action generates and saves the plan:

```typescript
// Inside actions/workflows/publishWorkflow.ts (simplified)
export async function PublishWorkflow({ id, flowDefinition }: { id: string; flowDefinition: string }) {
  // ... workflow lookup ...

  const flow = JSON.parse(flowDefinition);
  const result = FlowToExecutionPlan(flow.nodes, flow.edges); // Generate the plan here!

  if (result.error) {
    throw new Error("flow definition not valid: " + result.error.type); // Handle errors
  }

  await prisma.workflow.update({
    where: { id, userId },
    data: {
      definition: flowDefinition,
      executionPlan: JSON.stringify(result.executionPlan!), // Save the plan!
      // ... other updates ...
      status: WorkflowStatus.PUBLISHED,
    },
  });
  // ... revalidate path ...
}
```

In both cases, the visual node/edge data is fed into `FlowToExecutionPlan` to get the structured sequence.

### Structure of the Execution Plan

The `WorkflowExecutionPlan` is essentially an array of **phases**.

```typescript
// Inside types/workflow.ts (simplified)
export type WorkflowExecutionPlanPhase = {
  phase: number; // The phase number (e.g., 1, 2, 3...)
  nodes: AppNode[]; // An array of nodes (tasks) to run in THIS phase
};

export type WorkflowExecutionPlan = WorkflowExecutionPlanPhase[]; // The plan is an array of phases
```

Each **phase** contains one or more task nodes that can be executed. Why phases? Because sometimes, multiple tasks in your workflow can run *at the same time* without depending on each other. Grouping them into phases allows the execution engine to potentially run them in parallel (though the current `ExecuteWorkflow` implementation runs phases sequentially, tasks within a phase could conceptually run concurrently).

The crucial point is the *order* of the phases. Phase 1 nodes run first. Once all nodes in Phase 1 are complete, Phase 2 nodes can potentially run, and so on. The plan generator ensures that any node in Phase N only has dependencies (required inputs connected by edges) coming from nodes in phases less than N.

Let's revisit our simple example workflow from the [Flow Editor](04_flow_editor_.md) chapter:

```mermaid
graph LR
    A[Navigate URL Task] --> B[Extract Data Task]
    B --> C[AI Agent Task]
    C --> D[Deliver Result Task]
```

Based on the connections, the Execution Plan would look something like this:

```json
[
  {
    "phase": 1,
    "nodes": [
      { "id": "A", "type": "NAVIGATE_URL", "data": { /*...*/ } }
    ]
  },
  {
    "phase": 2,
    "nodes": [
      { "id": "B", "type": "EXTRACT_DATA", "data": { /*...*/ } }
    ]
  },
  {
    "phase": 3,
    "nodes": [
      { "id": "C", "type": "AGENT", "data": { /*...*/ } }
    ]
  },
  {
    "phase": 4,
    "nodes": [
      { "id": "D", "type": "DELIVER_RESULT", "data": { /*...*/ } }
    ]
  }
]
```

In this simple linear case, each phase has only one node, and they run sequentially.

What about a slightly more complex example?

```mermaid
graph LR
    A[Start] --> B[Get Data 1]
    A --> C[Get Data 2]
    B --> D[Combine Data]
    C --> D[Combine Data]
    D --> E[Save Result]
```

Here, "Get Data 1" and "Get Data 2" both depend only on "Start". They don't depend on each other. The "Combine Data" task depends on *both* "Get Data 1" and "Get Data 2". The Execution Plan generator needs to figure this out:

```json
[
  {
    "phase": 1,
    "nodes": [
      { "id": "A", "type": "START", "data": { /*...*/ } }
    ]
  },
  {
    "phase": 2,
    "nodes": [
      { "id": "B", "type": "GET_DATA_1", "data": { /*...*/ } },
      { "id": "C", "type": "GET_DATA_2", "data": { /*...*/ } }
    ]
  },
  {
    "phase": 3,
    "nodes": [
      { "id": "D", "type": "COMBINE_DATA", "data": { /*...*/ } }
    ]
  },
  {
    "phase": 4,
    "nodes": [
      { "id": "E", "type": "SAVE_RESULT", "data": { /*...*/ } }
    ]
  }
]
```

In this plan, Phase 2 contains two nodes (`B` and `C`). This indicates they can potentially run after Phase 1 completes because their dependencies (`A`) are met. Phase 3 (`D`) can only run after *both* `B` and `C` from Phase 2 are complete.

### How Plan Generation Works (Under the Hood)

The `FlowToExecutionPlan` function essentially performs a topological sort of the workflow graph, but it groups tasks into phases based on when their dependencies are met.

Here's a conceptual look at the steps `FlowToExecutionPlan` takes:

1.  **Find the Entry Point:** It first looks for the task node that's marked as the starting point (`isEntryPoint: true` in its task definition). There should be exactly one. If not, it's an error. This entry point forms the first phase (Phase 1). Mark this node as "planned".
    ```typescript
    // Inside lib/workflow/executionPlan.ts (simplified)
    const entryPoint = nodes.find((node) => {
      // Check if this node type is an entry point
      const task = (node.data.type === AGENT_TYPE ? AgentTask : TaskRegistry[node.data.type as TaskType]);
      return task && task.isEntryPoint;
    });

    if (!entryPoint) {
      return { error: { type: FlowToExecutionPlanValidationError.NO_ENTRY_POINT } };
    }

    const executionPlan: WorkflowExecutionPlan = [{ phase: 1, nodes: [entryPoint] }];
    const planned = new Set<string>(); // Keep track of nodes already placed in the plan
    planned.add(entryPoint.id);
    ```
2.  **Iterate and Build Phases:** It then enters a loop to build subsequent phases. In each iteration (representing a new phase):
    *   It looks at *all* nodes that haven't been planned yet.
    *   For each unplanned node, it checks if all of its *required* inputs are satisfied. An input is satisfied if either:
        *   It has a **manual value** set in the [Flow Editor](04_flow_editor_.md) (like a hardcoded URL).
        *   It is connected by an edge to an output of a node that is **already planned** (meaning it's in a previous phase).
    *   If a node's required inputs are all satisfied by already planned nodes or manual values, that node is ready to run in the *current* phase being built.
    *   Add all ready nodes to the current phase. Mark these nodes as "planned".
    ```typescript
    // Inside lib/workflow/executionPlan.ts (simplified loop)
    for (let phase = 2; planned.size < nodes.length; phase++) { // Continue until all nodes are planned
      const nextPhase: WorkflowExecutionPlanPhase = { phase, nodes: [] };
      let foundReadyNodes = false; // Flag to check if we added anything this phase

      for (const currentNode of nodes) {
        if (planned.has(currentNode.id)) continue; // Skip already planned nodes

        // Check if all REQUIRED inputs for this node are satisfied by planned nodes or manual values
        const invalidInputs = getInvalidInputs(currentNode, edges, planned);

        if (invalidInputs.length === 0) {
          // All required inputs are satisfied! This node is ready for this phase.
          nextPhase.nodes.push(currentNode);
          foundReadyNodes = true;
        } else {
           // Node is not ready yet, check if it's *stuck* due to a fundamental invalid input
           const incomers = getIncomers(currentNode, nodes, edges); // Find nodes leading to this one
           if (incomers.every((incomer) => planned.has(incomer.id))) {
               // All upstream nodes are planned, but this node *still* has invalid inputs?
               // This indicates a validation error in the workflow definition (e.g., missing required input)
               inputsWithErrors.push({ nodeId: currentNode.id, inputs: invalidInputs });
           }
           // Else: some upstream nodes are not yet planned, so it's okay for this node not to be ready yet.
           // Continue to the next unplanned node.
        }
      }

      if (nextPhase.nodes.length === 0 && planned.size < nodes.length) {
          // If we didn't find ANY new nodes to add to this phase, AND there are still unplanned nodes,
          // it means the remaining nodes have unresolved dependencies (e.g., a cycle, or missing entry point successor).
          // This indicates a workflow validation error (like a cycle or a disconnected graph).
          // The current simplified code might just loop infinitely or finish with unplanned nodes,
          // but a robust implementation would detect this and return an error here.
          console.error("Could not plan all nodes. Possible cycle or disconnected graph fragment.");
          break; // Exit loop to prevent infinite loop
      }

      // Add the ready nodes to the planned set
      for (const node of nextPhase.nodes) {
          planned.add(node.id);
      }

      // Add the new phase to the execution plan
      if (nextPhase.nodes.length > 0) { // Only add phase if it contains nodes
         executionPlan.push(nextPhase);
      }

       // Optional: Add a safety break for very large workflows or complex issues
       if(phase > nodes.length + 5) { // arbitrary limit
           console.error("Planning took too many phases, potentially a cycle or issue.");
           break;
       }
    }

    ```
3.  **Validation:** During this process, the `FlowToExecutionPlan` function also performs crucial validation:
    *   Is there exactly one entry point?
    *   Are there any required inputs that are *not* satisfied by either a manual value or a connection from an already planned node? (This check is done within the `getInvalidInputs` helper function).
    *   Would adding the node create a cycle in the graph? (The simplified `getInvalidInputs` check and the loop structure help detect certain blocked inputs, but a full cycle detection would be needed in a robust implementation, often done using graph traversal algorithms).
    If any of these validation checks fail, the `FlowToExecutionPlan` function returns an `error` object instead of an `executionPlan`.

    ```typescript
    // Inside lib/workflow/executionPlan.ts (simplified getInvalidInputs)
    function getInvalidInputs(node: AppNode, edges: Edge[], planned: Set<string>) {
      const invalidInputs = [];
      const task = (node.data.type === AGENT_TYPE ? AgentTask : TaskRegistry[node.data.type as TaskType]);

      if (!task) return []; // Should not happen if tasks are registered

      for (const input of task.inputs) {
        const manualValue = node.data.inputs?.[input.name]; // Check for manual value
        const hasManualValue = manualValue !== undefined && manualValue !== null && manualValue !== "";

        if (hasManualValue) {
           continue; // Input satisfied by manual value
        }

        // No manual value, look for a connection from a *planned* node
        const incomingEdge = edges.find(
          (edge) => edge.target === node.id && edge.targetHandle === input.name
        );

        const isConnectedFromPlanned = incomingEdge && planned.has(incomingEdge.source);

        if (input.required && !isConnectedFromPlanned) {
          // Required input is NOT satisfied by a planned source
          invalidInputs.push(input.name);
        }
         // Note: Non-required inputs are only problematic if they are connected from
         // an unplanned source and relied upon later. The current structure
         // focuses on required inputs for planning.
      }

      return invalidInputs; // List of required inputs that are NOT satisfied by planned sources
    }
    ```
This function is crucial for the plan generation loop because it tells `FlowToExecutionPlan` which nodes are *ready* to be added to the current phase.

Here's a simplified sequence diagram showing the process:

```mermaid
sequenceDiagram
    participant FlowEditor as Visual Editor Data (Nodes/Edges)
    participant Generator as FlowToExecutionPlan (Code)
    participant TaskRegistry as Task Definitions
    participant AgentTask as Agent Task Definition

    FlowEditor->>Generator: Provide nodes and edges
    Generator->>Generator: Find Entry Point (Phase 1)
    Generator->>TaskRegistry: Look up isEntryPoint
    Generator->>AgentTask: Look up isEntryPoint
    loop Build Phases until all nodes planned
        Generator->>Generator: Find unplanned nodes ready for this phase
        loop For each unplanned node
            Generator->>TaskRegistry: Get inputs for node type
            Generator->>AgentTask: Get inputs if AGENT type
            Generator->>Generator: Check if inputs are satisfied (manual value or planned source)
        end
        Generator->>Generator: Add ready nodes to current phase
        Generator->>Generator: Mark nodes as planned
        alt No nodes added & still unplanned
            Generator->>Generator: Detect workflow error (cycle/stuck)
            Generator-->>FlowEditor: Return Error
            break
        end
    end
    Generator-->>FlowEditor: Return Execution Plan
```

If the `FlowToExecutionPlan` function returns an error (because it couldn't generate a valid, complete plan), the system prevents the workflow from being published or executed. This ensures that only valid, runnable workflows are allowed.

### Execution Plan vs. Workflow Execution

It's important not to confuse the **Execution Plan** with the **[Workflow Execution](05_workflow_execution_.md)**.

*   The **Execution Plan** is a *static list* of steps (phases and nodes in order). It's the *plan* itself, generated once when published or at the start of a run for a draft.
*   The **[Workflow Execution](05_workflow_execution_.md)** is the *dynamic process* of *following* that plan. It's the actual running instance where tasks are performed, inputs and outputs are processed, logs are generated, and statuses are updated.

The `ExecuteWorkflow` function from the previous chapter simply takes the generated `executionPlan` and iterates through its `phases` and `nodes` to run them in the prescribed order.

```typescript
// Inside lib/workflow/executeWorkflow.ts (simplified)
export async function ExecuteWorkflow(executionId: string, nextRunAt?: Date) {
  // ... fetch execution details including the saved definition/plan ...
  const execution = await prisma.workflowExecution.findUnique({
     where: { id: executionId },
     include: { phases: { orderBy: { number: 'asc' } } }, // Get phases in order!
  });

  if (!execution || !execution.definition) { /* handle error */ }

  const workflowDefinition = JSON.parse(execution.definition); // Load nodes/edges
  const executionPlan: WorkflowExecutionPlan = JSON.parse(execution.executionPlan || execution.definition); // Load the plan

  // ... setup environment, update status ...

  // Loop through each phase in the Execution Plan
  for (const phase of execution.phases) { // Note: uses the `phases` *record* created from the plan
     // This calls executeWorkflowPhase for each task instance record linked to this phase
     // The order is guaranteed by the phase 'number' and order within the phase.
     const node = JSON.parse(phase.node) as AppNode; // Get the node data for this specific phase record

     // executeWorkflowPhase handles finding the right Executor and running the task logic
     const phaseExecutionResult = await executeWorkflowPhase(
       phase, environment, workflowDefinition.edges, execution.userId
     );

     if (!phaseExecutionResult.success) {
        // If a phase/task fails, stop the execution
        break;
     }
  }

  // ... finalize execution, cleanup ...
}
```

This snippet shows that `ExecuteWorkflow` retrieves the list of phase records (which were created *based* on the `executionPlan` structure when the `workflowExecution` record was created) and processes them sequentially. The order was determined during the plan generation.

### Conclusion

The **Execution Plan** is a critical intermediate step in `agentic-workflow`. It translates your visual, graph-based workflow design (nodes and edges) into a strict, ordered sequence of phases and tasks that the system can execute. Generated by the `FlowToExecutionPlan` function when you publish or run a workflow, it ensures that tasks run in the correct order, respecting dependencies and enabling the [Workflow Execution](05_workflow_execution_.md) engine to reliably carry out your automation. The plan generation process also validates your workflow design, preventing you from running illogical or incomplete flows.

In the next chapter, we'll look closer at the concept of **[Execution Phases](07_execution_phases_.md)** – the individual steps within the Execution Plan that represent a single task instance being run.

[Execution Phases](07_execution_phases_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)