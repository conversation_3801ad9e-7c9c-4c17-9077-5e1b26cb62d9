"""
FastAPI dependencies for the Agentic RAG system.
"""

from app.core.agentic_rag import AgenticRAG
from app.document_processing.document_processor import DocumentProcessor
from app.core.logging_config import get_logger

logger = get_logger(__name__)

# Global instances - initialized by app factory
_agentic_rag: AgenticRAG = None
_document_processor: DocumentProcessor = None


def initialize_dependencies():
    """Initialize global dependencies."""
    global _agentic_rag, _document_processor
    
    logger.info("Initializing AgenticRAG system...")
    _agentic_rag = AgenticRAG()
    logger.info("AgenticRAG system initialized successfully")
    
    logger.info("Initializing DocumentProcessor...")
    _document_processor = DocumentProcessor()
    logger.info("DocumentProcessor initialized successfully")


def get_agentic_rag() -> AgenticRAG:
    """Dependency to get the AgenticRAG instance."""
    return _agentic_rag


def get_document_processor() -> DocumentProcessor:
    """Dependency to get the DocumentProcessor instance."""
    return _document_processor
