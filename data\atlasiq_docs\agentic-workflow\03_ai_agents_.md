# Chapter 3: AI Agents

Welcome back! In the [previous chapter](02_tasks_.md), we explored **Tasks** – the individual action steps that make up a workflow. We saw how tasks like "Navigate URL" or "Extract Text" perform specific, defined actions and how their outputs become inputs for subsequent tasks.

However, sometimes you need to do more than just click buttons or extract text. What if you need to:

*   Summarize a long article found on a webpage?
*   Analyze the sentiment (positive, negative, neutral) of customer reviews?
*   Extract specific, complex information from unstructured text, like finding the key selling points from a product description?
*   Generate a short piece of marketing copy based on some data you've collected?

These kinds of tasks require understanding, creativity, or complex analysis – things that standard, rule-based tasks aren't built for. This is where **AI Agents** come in.

### What are AI Agents?

In `agentic-workflow`, an **AI Agent** is a special kind of resource you can create and configure. Think of it like hiring a virtual assistant with a specific skill set. You define its personality or role and give it instructions (its "prompt"), and then you can add it to your workflows to perform tasks that leverage the power of Large Language Models (LLMs) like those provided by OpenAI.

An AI Agent isn't a single task itself, but rather a *configuration* you create that can *be used* within an "AI Agent" task type in a workflow.

### The AI Agent Task Type

Just like "Navigate URL" is a type of Task, `agentic-workflow` has a specific Task type called **"AI Agent"**. When you add this task to your workflow, you tell it *which* of your configured AI Agents to use and *what text* to give it as input.

Let's revisit our use case: summarizing an article from a webpage.

1.  You'd use a "Navigate URL" task to visit the page.
2.  You'd use a "Page to HTML" or similar task to get the article's text content.
3.  Here's where the AI Agent comes in: You add an "AI Agent" task.
4.  You configure this "AI Agent" task to use a specific AI Agent you've created (e.g., one you designed to be a "Summarizer").
5.  You connect the output of the text extraction task (the article text) to the "input_text" of the "AI Agent" task.
6.  The AI Agent task runs, sending the text and the Agent's instructions to an LLM.
7.  The LLM processes the text according to the Agent's instructions and returns a summary.
8.  The "AI Agent" task outputs this summary as its "result", which can then be used by the next task (e.g., save it to a file).

### Defining Your AI Agent (The Prompt)

The core of an AI Agent is its **prompt**. This is a set of instructions you write that guides the LLM on how to behave and what to do with the input text it receives.

When you go to the "AI Agents" section of the application (you can see some of the code for listing and managing agents in `app/agents/page.tsx`, `app/agents/new/page.tsx`, `app/agents/[id]/page.tsx`), you can create a new agent.

You give your agent a Name (like "Article Summarizer"), a Description (like "This agent summarizes long articles"), and most importantly, a **Prompt**.

Here's a simplified look at the structure of an Agent object, likely stored in the database (like in `lib\models\Agent.ts`):

```typescript
// Inside lib/models/Agent.ts (simplified)
export interface Agent {
  id: string; // Unique ID for this agent
  userId: string; // Who created this agent
  name: string; // Display name (e.g., "Summarizer")
  description: string | null; // A short description
  prompt: string; // The critical instructions for the LLM
  createdAt: Date;
  updatedAt: Date;
}
```

And a glimpse of the code that saves it (from `lib\db\agent.ts`):

```typescript
// Inside lib/db/agent.ts (simplified createAgent function)
export async function createAgent(data: {
  name: string;
  description?: string;
  prompt: string;
}): Promise<Agent> {
  // ... (user authentication)
  const agentId = `agent_${Date.now()}`;

  await prisma.$executeRaw`
    INSERT INTO Agent (id, userId, name, description, prompt, createdAt, updatedAt)
    VALUES (
      ${agentId},
      ${userId},
      ${data.name},
      ${data.description || null},
      ${data.prompt}, // The crucial part is saved here
      ${now},
      ${now}
    )
  `;
  // ... (fetch and return the created agent)
}
```

When you create or edit an agent, you're saving this configuration, particularly the `prompt` text, in the database.

For our "Article Summarizer" example, the prompt might look something like:

```markdown
You are an expert article summarizer. Your goal is to read the provided article text and produce a concise, neutral summary that captures the main points. The summary should be no longer than 3-4 sentences. Focus on the core ideas and avoid adding your own opinions.
```

This `prompt` is what tells the underlying LLM how to process the input text when this specific Agent is used in a workflow.

You can also use the "Generate" feature (`app/agents/generate/page.tsx` and `app/api/agents/generate/route.ts`) to have an AI help you write the prompt and even suggest a name and description!

### Using an AI Agent in a Workflow

Once you've defined and saved your AI Agent, it becomes available to use in your workflows. You'll add a node of type "AI Agent" in the visual [Flow Editor](04_flow_editor_.md).

The "AI Agent" task definition (from `lib/workflow/task/AgentTask.tsx`) looks like this:

```typescript
// Inside lib/workflow/task/AgentTask.tsx (simplified)
import { TaskParamType, TaskType } from "@/types/task";
import { WorkflowTask } from "@/types/workflow";
import { BotIcon } from "lucide-react";

export const AGENT_TYPE = "AGENT"; // The internal type name

export const AgentTask: WorkflowTask = {
  type: AGENT_TYPE as any,
  label: "AI Agent", // What you see in the editor
  icon: BotIcon,
  credits: 1, // Note: AI Tasks consume credits!
  inputs: [
    {
      name: "agent_id", // Needs to know WHICH agent to use
      type: TaskParamType.STRING,
      required: true,
    },
    {
      name: "input_text", // The text to process
      type: TaskParamType.STRING,
      required: true,
    },
    {
      name: "previous_result", // Optional: context from a prior task
      type: TaskParamType.STRING,
      required: false,
    }
  ],
  outputs: [
    {
      name: "result", // The AI's response
      type: TaskParamType.STRING,
    },
  ],
};
```

In the [Flow Editor](04_flow_editor_.md), you would:

1.  Add an "AI Agent" node.
2.  Configure the `agent_id` input: You'd select one of the AI Agents you've created from a dropdown list (populated by fetching agents from the database).
3.  Connect the `input_text` input to the output of a previous task that provides the text you want the agent to process (e.g., the HTML or extracted text from a webpage).
4.  Connect the `result` output to a subsequent task that will use the AI's response (e.g., a task that saves text to a variable or sends it via a webhook).

### How AI Agents Work Internally

When a workflow execution reaches an "AI Agent" task, here's a simplified look at what happens:

```mermaid
sequenceDiagram
    participant Executor as Workflow Executor
    participant DB as Database
    participant AgentExecutor as Agent Executor (Code)
    participant LLMAPI as LLM API (e.g., OpenAI)

    Executor->>AgentExecutor: Run AGENT Task (task_id, inputs={agent_id, input_text, ...})
    AgentExecutor->>DB: Look up Agent by agent_id
    DB-->>AgentExecutor: Return Agent definition (including prompt)
    AgentExecutor->>AgentExecutor: Combine prompt + input_text
    AgentExecutor->>LLMAPI: Send Request (prompt, input_text, config)
    LLMAPI-->>AgentExecutor: Return AI Response (the "result")
    AgentExecutor->>Executor: Set task output ("result" = AI Response)
    Executor->>Executor: Continue to next task
```

Specifically, the `AgentExecutor.ts` file contains the code responsible for running the "AI Agent" task type:

```typescript
// Inside lib/workflow/executor/AgentExecutor.ts (simplified)
export async function AgentExecutor(
  environment: ExecutionEnvironment<any> // Environment provides inputs/outputs/logging
): Promise<boolean> {
  const { log } = environment;
  const agentId = environment.getInput("agent_id"); // Get the configured agent ID
  const inputText = environment.getInput("input_text"); // Get the input text

  log.info(`Agent is running (ID: ${agentId})`);

  try {
    // 1. Look up the Agent definition from the database
    const agent = await getAgentById(agentId); // Simplified, this fetches from DB
    if (!agent) {
      log.error(`Agent not found (ID: ${agentId})`);
      return false;
    }

    // 2. Call the underlying LLM API (e.g., via a Python script)
    // The AgentExecutor uses a helper script to interface with the LLM
    const scriptPath = path.join(process.cwd(), "python_scripts", "agent.py");
    const result = spawnSync("python", [
        scriptPath,
        agentId, // Pass agent ID (though prompt is also passed)
        inputText,
        agent.prompt // Pass the agent's prompt
        // previous_result can also be passed
      ], { /* ... environment and timeout config ... */ });

    if (result.status !== 0) {
       log.error(`Python script failed: ${result.stderr}`);
       return false;
    }

    // 3. Get the output from the script (the LLM's response)
    const output = result.stdout.trim();
    if (!output) {
      log.error("Agent output is empty!");
      return false;
    }

    // 4. Set the output of the Task
    environment.setOutput("result", output);
    log.info(`Agent response: ${output.substring(0, 100)}...`);
    return true; // Task succeeded
  } catch (error) {
    log.error(`Error running agent: ${error}`);
    return false; // Task failed
  }
}
```

This code shows that the `AgentExecutor` doesn't *contain* the LLM logic itself, but acts as an intermediary. It retrieves the user-defined Agent (with its `prompt`) and the input text, and then it uses a separate process (in this case, calling a Python script like `python_scripts/agent.py`) to interact with the actual LLM API.

The Python script (`python_scripts/agent.py`) then uses an LLM library (like OpenAI's) to send the `prompt` and `input_text` to the LLM service and get back the AI-generated response.

```python
# Inside python_scripts/agent.py (simplified)
import sys
import os
from openai import OpenAI # Using the OpenAI library

def run_agent(agent_id: str, input_text: str, prompt: str, previous_result: str = None):
    try:
        client = OpenAI(api_key=os.getenv("OPENAI_API_KEY")) # Needs API key

        # The core interaction with the LLM
        response = client.chat.completions.create(
            model="gpt-4.1-mini", # Uses a specific model
            messages=[
                {"role": "system", "content": prompt}, # The agent's instructions
                {"role": "user", "content": input_text}, # The input text
            ],
            temperature=0.7,
            max_tokens=500,
            timeout=20,
        )

        result = response.choices[0].message.content.strip()
        return result

    except Exception as e:
        return f"Error occurred: {str(e)}"

if __name__ == "__main__":
    # Gets arguments passed from the Node.js executor
    agent_id = sys.argv[1]
    input_text = sys.argv[2]
    prompt = sys.argv[3]
    # ... get previous_result if available ...

    final_result = run_agent(agent_id, input_text, prompt, previous_result)
    print(final_result) # Output the result to standard output
    sys.exit(0)
```

This script demonstrates the final step: taking the user's prompt and input and sending it to the LLM, then printing the result so the Node.js executor can capture it.

### Credits

It's important to note that interacting with LLMs costs money, usually based on the amount of text sent and received (tokens). In `agentic-workflow`, running an "AI Agent" task consumes **credits**. The exact cost depends on the model used and the amount of text processed, but it's something to be aware of when designing workflows that heavily utilize AI Agents.

### Conclusion

AI Agents are a powerful feature in `agentic-workflow` that allow you to incorporate complex, AI-driven tasks into your automations. By creating custom agents with specific prompts, you can leverage LLMs for tasks like summarizing, analyzing, or generating text, going beyond the capabilities of standard tasks. You define your agents separately and then use them within a workflow via the "AI Agent" task type, connecting inputs and outputs just like any other task. Running these tasks consumes credits due to the underlying LLM usage.

In the next chapter, we'll bring everything together and show you how to visually build and connect Workflows, Tasks, and AI Agents using the **[Flow Editor](04_flow_editor_.md)**.

[Flow Editor](04_flow_editor_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)