# Chapter 4: Task Management System

Welcome back to the `mytask` journey! In [Chapter 1: User Interface Components & Pages](01_user_interface_components___pages_.md), we learned how `mytask` builds all the visual parts you see. Then, in [Chapter 2: User & Authentication System](02_user___authentication_system_.md), we explored how `mytask` identifies you and keeps your tasks secure. And in [Chapter 3: Atlas University Integration](03_atlas_university_integration_.md), we saw how university members can easily log in.

Now that we know *who* can use `mytask` and *how* they log in, it's time to get to the very heart of the application: **managing tasks!** After all, `mytask` is a "task management" system.

### The Problem: Keeping Track of "Everything"

Imagine you're trying to organize a big project, like planning a university event or preparing for a major exam. You have a long list of things to do: "Book a venue," "Invite speakers," "Prepare study notes," "Review Chapter 5," "Send reminder emails."

If you just write these down on sticky notes or in a messy spreadsheet, it quickly becomes overwhelming:
*   How do you know who is doing what?
*   When is each item due?
*   Which tasks are most important?
*   What if a big task needs to be broken into smaller steps?
*   How do you know if the whole project is on track?

This messy problem is exactly what a "Task Management System" like `mytask` is designed to solve!

### Solution: Your Digital Project Planner

`mytask` acts like your personal digital project planner and organizer. It gives you all the tools you need to:
*   **Create** new tasks with all their important details.
*   **Assign** tasks to yourself or a team.
*   **Track** progress and see what stage each task is in.
*   **Organize** large projects by breaking them into smaller, connected pieces.

Let's start with the most basic step: creating a task!

#### Central Use Case: Creating a New Task

Imagine you need to add a new task: "Prepare Chapter 4 Tutorial." You want to set a due date, assign it to yourself, and mark it as high priority.

Here's how you might do it in `mytask`:

1.  **You go to the `Tasks` page:** You see a list of your existing tasks (often displayed using `TaskCard` components, as discussed in [Chapter 1: User Interface Components & Pages](01_user_interface_components___pages_.md)).
2.  **You click "Add Task":** This opens a form (likely using the `AddTask` component, which is a specialized [UI Component](01_user_interface_components___pages_.md)).
3.  **You fill in the details:** You type "Prepare Chapter 4 Tutorial," select a due date, set priority to "High," and choose team members (e.g., just yourself, or others too).
4.  **You click "Save":** The task is added to your list!

### What is a "Task" in `mytask`? (The Blueprint)

Before we look at the code, let's understand what information `mytask` keeps for each "Task." Think of it as a blueprint for a task item.

**Key Task Properties:**

*   **`title`:** The main name of your task (e.g., "Write Chapter 4").
*   **`description`:** More details about what needs to be done.
*   **`date`:** The due date.
*   **`priority`:** How important it is (High, Medium, Normal, Low).
*   **`stage`:** Where the task is in its workflow (To Do, In Progress, Completed).
*   **`progress`:** A percentage (0-100%) indicating how much of the task is done.
*   **`team`:** Who is assigned to this task (can be one or many users).
*   **`createdBy`:** Who originally created this task.
*   **`parent` & `children`:** Links to other tasks if this task is part of a bigger project or has smaller steps under it.
*   **`assets`:** Any files or attachments related to the task.
*   **`isTrashed`:** Marks if a task has been moved to the trash.

These properties are defined in a file called `taskModel.js` on the server. This is the **data structure** for a task:

```javascript
// server\models\taskModel.js (Simplified)
import mongoose, { Schema } from "mongoose";

const taskSchema = new Schema(
  {
    title: { type: String, required: true },
    description: { type: String, default: "" },
    date: { type: Date, default: new Date() },
    priority: { type: String, default: "normal" }, // "high", "medium", "low"
    stage: { type: String, default: "todo" },       // "in progress", "completed"
    progress: { type: Number, default: 0 },         // 0-100%
    parent: { type: Schema.Types.ObjectId, ref: "Task", default: null },
    children: [{ type: Schema.Types.ObjectId, ref: "Task" }],
    team: [{ type: Schema.Types.ObjectId, ref: "User" }],
    createdBy: { type: Schema.Types.Mixed, ref: "User" },
    isTrashed: { type: Boolean, default: false },
    level: { type: Number, default: 0 }, // How deep in the project hierarchy
    // ... other properties like activities, assets, mandays, cost
  },
  { timestamps: true } // Adds 'createdAt' and 'updatedAt' dates automatically
);

const Task = mongoose.model("Task", taskSchema);
export default Task;
```
**Explanation:** This `taskSchema` is like the blueprint for a task record in `mytask`'s database. It tells `mytask` exactly what information to collect and store for every task, including who it's assigned to (`team`) and its current progress. The `parent` and `children` fields are crucial for building complex project structures.

### How Task Management Works (Under the Hood)

Let's follow the journey of creating a task, from clicking the button to the task appearing in your list.

```mermaid
sequenceDiagram
    participant You
    participant MyTaskApp as MyTask App (Browser)
    participant MyTaskServer as MyTask Server
    participant Database

    You->>MyTaskApp: Fill "Add Task" form & Click "Save"
    MyTaskApp->>MyTaskServer: Send "Create Task" Request
    MyTaskServer->>Database: Save new Task (using Task Model)
    Database-->>MyTaskServer: Task data stored!
    MyTaskServer-->>MyTaskApp: Confirmation: Task Created
    MyTaskApp->>You: Update Task List & Show new Task
```

**Step-by-step Explanation:**

1.  **You interact with the App:** You open the `AddTask` component, type in the task details (title, description, due date, etc.), and click "Save."
2.  **App sends request:** The `mytask` application in your browser gathers all this information and sends it as a "Create Task" request to the `mytask` server.
3.  **Server processes request:** The `mytask` server receives the request. It then takes the task details and uses the `Task` model blueprint to create a new task record in the `Database`.
4.  **Database saves task:** The database securely stores the new task record.
5.  **Server confirms:** The server gets confirmation from the database and sends a "Task created successfully" message back to your `mytask` app.
6.  **App updates view:** Your `mytask` app receives the confirmation, updates its list of tasks (often by refreshing the data from the server), and displays your brand new task!

### Diving Deeper: The Code Behind Task Management

Let's look at some key code pieces that make this happen.

#### 1. On Your Computer (Client-Side) - Sending the Request

When you click "Save" on the `AddTask` form, the `mytask` app uses a special function to send the task data to the server. This code is part of how the [Data Persistence & API Layer](07_data_persistence___api_layer_.md) handles communication.

```javascript
// client\src\redux\slices\api\taskApiSlice.js (Simplified)
import { TASKS_URL } from "../../../utils/contants";
import { apiSlice } from "../apiSlice";

export const postApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    createTask: builder.mutation({ // This prepares a "create task" request
      query: (data) => ({
        url: `${TASKS_URL}/create`, // Server address for creating tasks
        method: "POST",             // We are "POSTing" new data
        body: data,                 // The task data (title, description, etc.)
      }),
    }),
    // ... other task-related actions like update, get, delete, duplicate
  }),
});

export const { useCreateTaskMutation } = postApiSlice;
```
**Explanation:** This snippet defines a `createTask` function that the `mytask` app uses. When the app needs to create a new task, it uses `useCreateTaskMutation` to send all the task information (`data`) securely to the `mytask` server at the `/api/tasks/create` address.

#### 2. On the Server (Backend) - Handling Task Creation

The `mytask` server receives the "Create Task" request and stores the task in the database. It also handles setting the task creator and dealing with parent-child relationships.

```javascript
// server\controllers\taskController.js (Simplified createTask)
import asyncHandler from "express-async-handler";
import Task from "../models/taskModel.js"; // Our Task blueprint

const createTask = asyncHandler(async (req, res) => {
  try {
    const { userId } = req.user; // Get the ID of the logged-in user (from token)
    const { title, description, team, date, priority, parentId } = req.body;
    
    // Prepare task data with basic info and the creator
    const taskData = {
      title, description, team, date, priority,
      createdBy: userId.toString(), // The logged-in user is the creator
    };

    // If a parent task was specified, link this new task to it
    if (parentId) {
      const parentTask = await Task.findById(parentId);
      if (!parentTask) { // Check if the parent task exists
        return res.status(404).json({ message: "Parent task not found" });
      }
      taskData.parent = parentId;
      taskData.level = parentTask.level + 1; // Set task's level in hierarchy
    }

    const task = await Task.create(taskData); // Save the new task to the database

    // If this new task is a child, update its parent to include it in children list
    if (parentId) {
      await Task.findByIdAndUpdate(parentId, {
        $push: { children: task._id } // Add new task to parent's children
      });
    }

    res.status(200).json({ status: true, task, message: "Task created successfully." });
  } catch (error) {
    console.error(error);
    return res.status(400).json({ status: false, message: error.message });
  }
});

export { createTask };
```
**Explanation:** This `createTask` function on the server receives your task details. It automatically sets the `createdBy` field to your user ID (obtained from your [User & Authentication System](02_user___authentication_system_.md) login token). If you specified a `parentId`, it links the new task to its parent and updates the parent task to include this new task as its child. Finally, it saves the task to the database using the `Task` model blueprint we saw earlier.

#### 3. Defining the Server Routes

For the `mytask` server to know which function to call for `/api/tasks/create`, we need to define routes.

```javascript
// server\routes\taskRoute.js (Simplified)
import express from "express";
import { createTask, getTasks, updateTask, trashTask } from "../controllers/taskController.js";
import { protectRoute } from "../middleware/authMiddleware.js"; // From Chapter 2

const router = express.Router();

router.post("/create", protectRoute, createTask); // When someone POSTs to /create, use createTask
router.get("/", protectRoute, getTasks);          // To get all tasks
router.put("/update/:id", protectRoute, updateTask); // To update a specific task
router.put("/:id", protectRoute, trashTask);      // To move a task to trash

export default router;
```
**Explanation:** This tells our `mytask` server: "When someone sends a `POST` request to `/api/tasks/create`, first use `protectRoute` (which verifies they are logged in, as covered in [Chapter 2: User & Authentication System](02_user___authentication_system_.md)), and then run the `createTask` function to handle the request." This is how the client-side request gets handled by the correct function on the server.

### Task Hierarchy: Breaking Down Big Projects

One of `mytask`'s powerful features is its ability to handle "parent" and "child" tasks. This means you can take a big task (like "Plan University Event") and break it down into smaller, manageable sub-tasks (like "Book Venue," "Hire Catering," "Send Invitations"). Each of these smaller tasks can then have its own details, due dates, and even further sub-tasks!

The `parent` and `children` fields in our `taskModel` (`taskSchema`) are what enable this.

You can view these relationships using components like `TaskHierarchyView` in the `mytask` user interface.

#### Updating Tasks and Hierarchy

You can also update tasks, including changing their parent, description, or stage.

```javascript
// client\src\redux\slices\api\taskApiSlice.js (Simplified update & hierarchy query)
// ...
    updateTask: builder.mutation({
      query: (data) => ({
        url: `${TASKS_URL}/update/${data._id}`, // Send updates to a specific task ID
        method: "PUT",
        body: data, // Updated task data, might include a new parentId
      }),
    }),

    getTaskHierarchy: builder.query({ // Get a task and its parent/children
      query: (id) => ({
        url: `${TASKS_URL}/hierarchy/${id}`, // Fetch hierarchy for a task
        method: "GET",
      }),
    }),
// ...
```
**Explanation:** The `updateTask` mutation sends changes to a specific task identified by its `_id`. The `getTaskHierarchy` query is used to fetch a task along with its entire chain of ancestors (parents) and descendants (children), which is very useful for visualizing complex projects in a tree-like structure.

On the server, `updateTask` handles changing properties and managing the `parent`/`children` arrays. It also includes permission checks to ensure only authorized users (admin, creator, or team member) can modify a task.

```javascript
// server\controllers\taskController.js (Simplified updateTask)
const updateTask = asyncHandler(async (req, res) => {
  const { id } = req.params; // The ID of the task to update
  const { title, description, parentId, progress, stage, team } = req.body;
  const { userId, isAdmin } = req.user; // User performing the update

  try {
    const task = await Task.findById(id);
    if (!task) return res.status(404).json({ message: "Task not found" });

    // --- Permission Check (simplified) ---
    const isTeamMember = task.team.some(member => member.toString() === userId.toString());
    const isCreator = task.createdBy && task.createdBy.toString() === userId.toString();
    if (!isAdmin && !isTeamMember && !isCreator) {
      return res.status(403).json({ message: "Not authorized to update this task." });
    }
    // --- End Permission Check ---
    
    const oldParentId = task.parent; // Remember task's current parent

    // Update basic task fields
    if (title !== undefined) task.title = title;
    if (description !== undefined) task.description = description;
    if (stage !== undefined) task.stage = stage.toLowerCase();
    if (team !== undefined) task.team = team;
    if (progress !== undefined) task.progress = progress;

    // Handle changes to the parent task relationship
    if (parentId !== undefined && parentId !== oldParentId) {
      if (oldParentId) { // If task had an old parent, remove it from that parent's children
        await Task.findByIdAndUpdate(oldParentId, { $pull: { children: task._id } });
      }
      if (parentId === null) { // If parentId is set to null, task becomes a top-level task
        task.parent = null;
        task.level = 0;
      } else { // Assign to a new parent
        const newParentTask = await Task.findById(parentId);
        if (!newParentTask) return res.status(404).json({ message: "New parent not found" });
        await Task.findByIdAndUpdate(parentId, { $push: { children: task._id } }); // Add to new parent's children
        task.parent = parentId;
        task.level = newParentTask.level + 1; // Update task's hierarchy level
      }
    }
    
    const updatedTask = await task.save(); // Save all changes

    // If this task has a parent and its progress was updated, recalculate parent's progress
    if (updatedTask.parent && progress !== undefined) {
      await recalculateParentProgress(updatedTask.parent);
    }
    
    res.status(200).json({ status: true, message: "Task updated.", task: updatedTask });
  } catch (error) {
    console.error('Error updating task:', error);
    return res.status(400).json({ status: false, message: error.message });
  }
});
```
**Explanation:** The `updateTask` function first performs a quick check to see if the logged-in user has permission to make changes. It then updates the task's details with the new information. If you change its `parent`, it intelligently removes the task from its old parent's children list (if it had one) and adds it to the new parent's children list. This ensures the hierarchy is always correct.

#### Automatic Progress Calculation for Parent Tasks

When you update the `progress` of a child task (e.g., from 0% to 50%), `mytask` automatically updates the progress of its parent task! This helps you see the overall progress of a big project without manually calculating it.

```javascript
// server\controllers\taskController.js (Simplified recalculateParentProgress)
async function recalculateParentProgress(parentId) {
  try {
    // Find the parent task and load all its direct children
    const parentTask = await Task.findById(parentId).populate('children');
    
    if (!parentTask || !parentTask.children || parentTask.children.length === 0) {
      return; // No parent or no children to calculate from
    }
    
    // Sum up the progress of all children and calculate the average
    const totalProgress = parentTask.children.reduce((sum, child) => sum + (child.progress || 0), 0);
    const averageProgress = Math.round(totalProgress / parentTask.children.length);
    
    parentTask.progress = averageProgress; // Update the parent's progress
    await parentTask.save();
    
    // If this parent task itself has a parent, continue updating up the chain!
    if (parentTask.parent) {
      await recalculateParentProgress(parentTask.parent);
    }
  } catch (error) {
    console.error('Error recalculating parent progress:', error);
  }
}
```
**Explanation:** This `recalculateParentProgress` function is a great example of `mytask` doing smart work for you. When a child task's progress changes, this function finds its parent, calculates the *average progress* of all its direct children, and updates the parent's `progress` field. It then checks if *that* parent has a parent, and if so, it repeats the process, effectively updating progress all the way up the task hierarchy!

### Other Task Management Features

`mytask` offers many other ways to manage your tasks:

*   **Duplicating Tasks:** If you have similar tasks, you can quickly make a copy instead of creating a new one from scratch.
*   **Trashing/Restoring Tasks:** Tasks aren't immediately deleted; they go to a "trash" where you can restore them within a certain period (e.g., 30 days). This acts as a safety net against accidental deletions.
*   **Filtering and Searching:** You can easily find tasks by stage ("To Do," "In Progress"), priority, due date, or by searching for keywords.
*   **Assigning Team Members:** Each task can have multiple team members assigned to it, ensuring everyone knows their responsibilities.

These features are powered by different functions in the `taskController.js` (like `duplicateTask`, `trashTask`, `restoreTask`, `getTasks`) and corresponding queries/mutations in `taskApiSlice.js`, all interacting with the `taskModel.js` and following the same request-response pattern we've seen.

### Conclusion

In this chapter, we explored the core of `mytask`: the **Task Management System**. We learned how `mytask` allows you to create, assign, track, and organize your tasks like a digital project planner. We saw how tasks are defined with various properties, how new tasks are created, and how the powerful parent-child hierarchy helps break down large projects, even automatically calculating progress for parent tasks.

Now that your tasks are organized, how does `mytask` tell you about important updates, new assignments, or approaching deadlines? That's what we'll uncover in the next chapter: [Notifications & Communications](05_notifications___communications_.md).

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)