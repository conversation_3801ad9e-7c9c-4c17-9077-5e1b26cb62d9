"""
Admin-related API endpoints.
"""

from fastapi import APIRouter, HTTPException, Depends

from app.core.agentic_rag import AgenticRAG
from app.api.dependencies import get_agentic_rag
from app.core.logging_config import get_logger
from app.core.logging_helpers import get_simple_logger_instance

logger = get_logger(__name__)
simple_logger = get_simple_logger_instance()

router = APIRouter(prefix="/admin", tags=["Admin"])


@router.post("/reload")
async def reload_bots(rag: AgenticRAG = Depends(get_agentic_rag)):
    """Reload all bot configurations."""
    try:
        logger.info("Starting bot configuration reload...")

        # Reload configurations
        rag.config_loader.reload_configs()
        logger.info("Bot configurations reloaded successfully")

        # Reload bots
        rag._load_bots()
        logger.info("Bots reloaded successfully")

        # Simple log
        bot_count = len(rag.get_bot_names())
        simple_logger.config_reloaded(True, bot_count)

        return {"status": "ok", "message": "Bots reloaded successfully"}
    except Exception as e:
        error_msg = f"Error reloading bots: {str(e)}"
        logger.error(error_msg)
        import traceback

        logger.error(f"Reload traceback: {traceback.format_exc()}")
        simple_logger.config_reloaded(False)
        raise HTTPException(status_code=500, detail=f"Error reloading bots: {str(e)}")
