# Chapter 9: Credentials

Welcome back! Over the past chapters, we've explored the core building blocks of `agentic-workflow`: [Workflows](01_workflows_.md) built from [Tasks](02_tasks_.md) and [AI Agents](03_ai_agents_.md), how you design them in the [Flow Editor](04_flow_editor_.md), how the system plans ([Execution Plan](06_execution_plan_.md)) and runs ([Workflow Execution](05_workflow_execution_.md)) them, and how it tracks the details of each step ([Execution Phases](07_execution_phases_.md)) using the [Task & Executor Registry](08_task___executor_registry_.md) to find the right code to run.

Many powerful automation tasks involve interacting with external services. For example, you might need to:

*   Send data to a web service using an API.
*   Log in to a website that requires a username and password.
*   Use an AI model service that requires an API key.

These interactions often require **sensitive information** like API keys, passwords, or secret tokens. Where do you store this sensitive data so your workflow can use it, but keep it safe and out of sight? You certainly don't want to type your password directly into a task configuration field where anyone viewing the workflow might see it, or where it might be accidentally saved in a less-than-secure way.

This is where the concept of **Credentials** comes in.

### What are Credentials?

In `agentic-workflow`, **Credentials** provide a secure way to store sensitive pieces of information that your tasks might need during **[Workflow Execution](05_workflow_execution_.md)**.

Think of the Credentials feature like a **digital vault** for your secrets. You put your sensitive values (like your OpenAI API key, or a password for a service) into this vault, give each secret a name, and `agentic-workflow` locks it away securely.

The key security feature of Credentials is that the sensitive values are **encrypted** before they are saved to the database. This means even if someone were to gain access to the database where your workflow data is stored, they wouldn't be able to read your passwords or API keys because they would be stored as scrambled, unreadable text.

The values are only **decrypted** by the system *at the exact moment* a task needs to use them during a **[Workflow Execution](05_workflow_execution_.md)**. Once the task is finished, the decrypted value is not stored anywhere.

### Managing Your Credentials

You can manage your credentials in a dedicated section of the `agentic-workflow` application. This is typically found on a "Credentials" page.

The UI for this page (`app/(dashboard)/credentials/page.tsx`) lists your saved credentials by their name. Critically, it **does not display the sensitive value** after it's saved – only the name you gave it.

```typescript
// Inside app/(dashboard)/credentials/page.tsx (simplified)
async function UserCredentials() {
  // Fetches credentials from the database
  const credentials = await GetCredentialsForUser();

  if (credentials.length === 0) {
    // Show a message if no credentials exist
    return <div>No credentials created yet...</div>;
  }

  return (
    <div className="flex gap-2 flex-wrap">
      {credentials.map((credential) => {
        return (
          <Card key={credential.id} className="w-full p-4 flex justify-between">
            <div className="flex gap-2 items-center">
              {/* Icon and Name */}
              <LockKeyholeIcon size={18} className="stroke-primary" />
              <p className="font-bold">{credential.name}</p>
              {/* ... display creation date ... */}
            </div>
            {/* Delete button */}
            <DeleteCredentialDialog name={credential.name} />
          </Card>
        );
      })}
    </div>
  );
}
```

This code shows how the `GetCredentialsForUser` action fetches the list, and the UI displays each credential using its stored `name`, but not its `value`.

To add a new credential, you use the "Create Credential" dialog (`app/(dashboard)/credentials/_components/CreateCredentialDialog.tsx`). Here, you provide a unique name for your credential and the sensitive value you want to store securely.

```typescript
// Inside app/(dashboard)/credentials/_components/CreateCredentialDialog.tsx (simplified)
function CreateCredentialDialog({ triggerText }: { triggerText?: string }) {
  const form = useForm<createCredentialSchemaType>({ /* ... validation schema ... */ });

  const { mutate, isPending } = useMutation({
    mutationFn: CreateCredential, // This is the action that saves it
    onSuccess: () => { /* ... success handling ... */ },
    onError: () => { /* ... error handling ... */ },
  });

  const onSubmit = useCallback((values: createCredentialSchemaType) => {
      mutate(values); // Call the server action to create
  }, [mutate]);

  return (
    <Dialog /* ... */>
      <DialogTrigger asChild>
        <Button>{triggerText ?? "Create"}</Button>
      </DialogTrigger>
      <DialogContent /* ... */>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Form field for the Credential Name */}
            <FormField control={form.control} name="name" render={({ field }) => (/* ... Input for name ... */)}/>
            {/* Form field for the Cred Credential Value */}
            <FormField control={form.control} name="value" render={({ field }) => (/* ... Textarea for value ... */)}/>
            <Button type="submit" disabled={isPending}>Proceed</Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
```

This component uses a form to collect the `name` and `value` and then calls the `CreateCredential` server action.

### Storing Credentials Securely (Encryption)

When you click "Proceed" in the "Create Credential" dialog, the `CreateCredential` server action is called. This action is responsible for taking the sensitive value you entered and encrypting it *before* saving it to the database.

```typescript
// Inside actions/credentials/createCredential.ts (simplified)
import { symmetricEncrypt } from "@/lib/encryption";
import prisma from "@/lib/prisma";

export async function CreateCredential(form: createCredentialSchemaType) {
  // ... validation and user check ...

  // *** Encrypt the sensitive value! ***
  const encryptedValue = symmetricEncrypt(data.value);

  // Save the encrypted value to the database
  const result = await prisma.credential.create({
    data: {
      userId,
      name: data.name,
      value: encryptedValue, // This is the encrypted text
    },
  });

  // ... handle result and revalidate ...
}
```

The core of the security here is the `symmetricEncrypt` function from `lib/encryption.ts`. This function uses a **symmetric encryption key** (meaning the same key is used for both encryption and decryption) that is stored securely on the server where `agentic-workflow` is running (usually as an environment variable, `process.env.ENCRYPTION_KEY`).

```typescript
// Inside lib/encryption.ts (simplified)
import crypto from "crypto";
import "server-only"; // Ensures this code only runs on the server

const ALG = "aes-256-cbc"; // A standard, strong encryption algorithm

export const symmetricEncrypt = (data: string) => {
  const key = process.env.ENCRYPTION_KEY;
  if (!key) throw new Error("encryption key not found");

  const iv = crypto.randomBytes(16); // Initialization Vector (needed for security)
  const cipher = crypto.createCipheriv(ALG, Buffer.from(key, "hex"), iv);

  let encrypted = cipher.update(data);
  encrypted = Buffer.concat([encrypted, cipher.final()]);
  // Store the IV alongside the encrypted data, separated by a colon
  return iv.toString("hex") + ":" + encrypted.toString("hex");
};
```

This `symmetricEncrypt` function takes your plain text value, encrypts it using the secret key, and prepends a unique Initialization Vector (IV) to the result (which is necessary for decryption later). It saves this combined, hex-encoded string (e.g., `"somehexiv:somehexencrypteddata"`) into the `value` column in the database. The original sensitive value is never stored in plain text in the database.

### Using Credentials in a Workflow Task

Once you have saved a credential, you can use it in any task within your workflow that requires a sensitive input and has been configured to accept a credential reference. Tasks like connecting to APIs, logging into services, or using specific AI model providers might have inputs of a special type, like `TaskParamType.CREDENTIAL`.

In the [Flow Editor](04_flow_editor_.md), when you configure the parameters for a task node that has an input parameter defined with `type: TaskParamType.CREDENTIAL`, the UI will present a dropdown list populated with the names of the credentials you have saved. You simply select the name of the credential containing the value you need for that task.

The component that renders input fields for parameters in the [Flow Editor](04_flow_editor_.md) (`app/workflow/_components/nodes/param/NodeParam.tsx`, which then delegates to type-specific components) will use `CredentialsParam.tsx` when it encounters a parameter of type `TaskParamType.CREDENTIAL`.

```typescript
// Inside app/workflow/_components/nodes/param/CredentialsParam.tsx (simplified)
import { TaskParamType } from "@/types/task";
import { useQuery } from "@tanstack/react-query";
import { GetCredentialsForUser } from "@/actions/credentials/getCredentialsForUser"; // Fetch credential names

export default function CredentialsParam({
  param, // Definition of this specific parameter (name, type=CREDENTIAL)
  updateNodeParamValue, // Function to save the selected value to the node's data
  value, // The currently selected credential ID (if any)
}: ParamProps) {
  // Fetch the list of credentials (only name and ID) to populate the dropdown
  const query = useQuery({
    queryKey: ["credentials-for-user"],
    queryFn: () => GetCredentialsForUser(), // Gets list of { id, name }
  });

  return (
    <div className="flex flex-col gap-1 w-full">
      <Label /* ... displays param.name ... */ />
      <Select
        onValueChange={(selectedValue) => updateNodeParamValue(selectedValue)}
        defaultValue={value} // Set the initial selection
      >
        <SelectTrigger /* ... */>
          <SelectValue placeholder="Select a credential" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectLabel>Credentials</SelectLabel>
            {query.data?.map((credential) => ( // Loop through fetched credentials
              <SelectItem key={credential.id} value={credential.id}>
                {credential.name} {/* Display the credential name */}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
}
```

When you select a credential name from the dropdown, the system saves the selected credential's **ID** (not its name or value) into the configuration data for that specific task node in your workflow. This ID acts as a pointer to the secure value stored separately.

### Accessing Credentials During Execution (Decryption)

The magic happens during **[Workflow Execution](05_workflow_execution_.md)**. When the **[Workflow Execution](05_workflow_execution_.md)** engine reaches a task node that requires a credential:

1.  The `executeWorkflowPhase` function (from [Chapter 5](05_workflow_execution_.md)) prepares the inputs for the specific task's Executor.
2.  If an input parameter for this task is configured to use a credential (its value in the node's saved data is a credential ID):
    *   The Executor function (or a helper it calls) retrieves the `Credential` record from the database using the stored ID.
    *   It retrieves the **encrypted value** from the database record.
    *   It then calls the `symmetricDecrypt` function from `lib/encryption.ts` to decrypt the value using the same secure key available on the server.
    *   The `symmetricDecrypt` function uses the IV stored in the encrypted string to correctly decrypt the data.
    *   The Executor receives the *decrypted, plain text* sensitive value.
3.  The Executor function uses this decrypted value to perform its task (e.g., includes it in an API request header, uses it for authentication).
4.  After the Executor finishes, the decrypted value is **not retained** by the execution environment or saved anywhere.

Here's a simplified look at the `symmetricDecrypt` function:

```typescript
// Inside lib/encryption.ts (simplified)
import crypto from "crypto";
import "server-only"; // Ensures this code only runs on the server

const ALG = "aes-256-cbc"; // Same algorithm as encryption

export const symmetricDecrypt = (encrypted: string) => {
  const key = process.env.ENCRYPTION_KEY;
  if (!key) throw new Error("encryption key not found");

  // Split the stored string into the IV and the encrypted data
  const textParts = encrypted.split(":");
  const iv = Buffer.from(textParts.shift() as string, "hex");
  const encryptedText = Buffer.from(textParts.join(":"), "hex");

  // Create the decipher object using the key and the IV
  const decipher = crypto.createDecipheriv(ALG, Buffer.from(key, "hex"), iv);

  // Perform decryption
  let decrypted = decipher.update(encryptedText);
  decrypted = Buffer.concat([decrypted, decipher.final()]);

  return decrypted.toString(); // Return the original, plain text value
};
```

An Executor function needing a credential might look something like this conceptually:

```typescript
// Conceptual snippet inside an Executor function (e.g., WebhookExecutor)
import { symmetricDecrypt } from "@/lib/encryption";
import { getCredentialById } from "@/lib/db/credential"; // Helper to fetch from DB

async function DeliverViaWebhookExecutor(environment: ExecutionEnvironment<any>) {
  // ... get other inputs ...

  // Get the value that was selected in the Flow Editor (which is the credential ID)
  const credentialId = environment.getInput("api_key_credential"); // Assuming the input is named "api_key_credential"

  if (!credentialId) {
    environment.log.error("API key credential not specified.");
    return false;
  }

  try {
    // 1. Fetch the credential record from the database
    const credential = await getCredentialById(credentialId); // This gets the { id, name, value (encrypted) } record

    if (!credential) {
      environment.log.error(`Credential with ID ${credentialId} not found.`);
      return false;
    }

    // 2. Decrypt the sensitive value using the helper function
    const apiKey = symmetricDecrypt(credential.value); // *** Decrypt here! ***
    environment.log.info("Credential decrypted successfully (value NOT logged)"); // Log decryption, but NOT the value

    // 3. Use the decrypted value in the task logic (e.g., make an API call)
    // Example: await fetch(webhookUrl, { headers: { 'X-API-Key': apiKey }, body: data });
    environment.log.info("Using decrypted API key for webhook.");

    // ... rest of webhook logic ...
    environment.setOutput("success", true); // Example output
    return true; // Task succeeded
  } catch (error) {
    environment.log.error(`Error accessing or decrypting credential: ${error}`);
    return false; // Task failed
  }
}
```

This conceptual Executor code shows how it fetches the credential record (containing the encrypted value), uses `symmetricDecrypt` to get the plain text, and then uses that plain text value for the task's operation. The decrypted value is handled within the function's scope and not persisted.

### Execution Flow with Credentials

Here's a simplified sequence diagram showing the flow when a task needing a credential is encountered during **[Workflow Execution](05_workflow_execution_.md)**:

```mermaid
sequenceDiagram
    participant Executor as Workflow Executor (executeWorkflowPhase)
    participant SpecificExecutor as Task Executor (e.g., WebhookExecutor)
    participant DB as Database
    participant EncryptionLib as Encryption Library (lib/encryption.ts)
    participant TaskLogic as Task Code (using decrypted value)

    Executor->>SpecificExecutor: Start Task Execution (pass inputs, including credential ID)
    SpecificExecutor->>DB: Fetch Credential record by ID
    DB-->>SpecificExecutor: Return Credential record (contains ENCRYPTED value)
    SpecificExecutor->>EncryptionLib: Call symmetricDecrypt(encrypted value)
    EncryptionLib->>EncryptionLib: Use server-side ENCRYPTION_KEY
    EncryptionLib->>SpecificExecutor: Return DECRYPTED value
    SpecificExecutor->>TaskLogic: Use DECRYPTED value to perform task action
    TaskLogic-->>SpecificExecutor: Task action completes
    SpecificExecutor->>Executor: Return result (outputs, status)
    Executor->>Executor: DECRYPTED value is not stored
    Executor->>DB: Save Execution Phase status, outputs, logs
```

This diagram highlights that the decryption happens only within the secure server environment, directly before the specific task Executor needs the value, and the plain text value is not stored afterward.

### Conclusion

Credentials provide a vital layer of security in `agentic-workflow` by allowing you to store sensitive information separately from your workflow definitions, encrypted at rest in the database. You manage these secrets through a dedicated UI, and they are only ever decrypted temporarily in memory by the server when a task specifically requires them during a **[Workflow Execution](05_workflow_execution_.md)**. This digital vault ensures that your API keys, passwords, and other secrets remain protected, even while being used to power your automated workflows.

This concludes our foundational tutorial on the core concepts of `agentic-workflow`. You've learned how to build workflows, understand their components, design them visually, and see how the system plans and executes them securely. With this knowledge, you're ready to start building powerful automations!

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)