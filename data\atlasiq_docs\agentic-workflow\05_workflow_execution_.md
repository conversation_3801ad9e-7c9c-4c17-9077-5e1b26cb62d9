# Chapter 5: Workflow Execution

Welcome back to the `agentic-workflow` tutorial! In the [previous chapter](04_flow_editor_.md), you learned how to visually design your automation blueprints using the **Flow Editor**, connecting different [Tasks](02_tasks_.md) and [AI Agents](03_ai_agents_.md) with edges. You created a detailed recipe for your digital task.

But a recipe sitting in a book doesn't make dinner! To actually perform the automation you've designed, you need to *run* that saved blueprint. This process is called **Workflow Execution**.

### What is Workflow Execution?

Simply put, **Workflow Execution** is the process of taking a saved Workflow (the blueprint you made in the [Flow Editor](04_flow_editor_.md)) and bringing it to life. It's when the system actually follows the instructions you laid out, step by step, to perform the automated task.

Imagine the blueprint for building a house. The blueprint defines everything – the walls, windows, plumbing. Execution is the actual construction process, where builders follow the blueprint, pour concrete, put up walls, install windows, and so on.

In `agentic-workflow`, when you trigger an execution, the system:

1.  Retrieves the saved Workflow's definition (the nodes and edges from your visual design).
2.  Follows a specific plan derived from that definition (called the [Execution Plan](06_execution_plan_.md)).
3.  Runs the logic for each [Task](02_tasks_.md) or [AI Agent](03_ai_agents_.md) in the correct order.
4.  Manages necessary resources like browser instances if the workflow involves web automation.
5.  Passes outputs from one task as inputs to the next, just as you connected them in the [Flow Editor](04_flow_editor_.md).
6.  Records everything that happens (logging).
7.  Updates the status of the run (like "Running," "Completed," or "Failed").
8.  Tracks and deducts credits for tasks that consume them (like [AI Agents](03_ai_agents_.md)).

### How is an Execution Triggered?

Workflow Execution can be triggered in a few ways:

1.  **Manually:** You can click a "Run" button within the `agentic-workflow` application, usually on the workflow's page or in the editor.
2.  **Automatically (via Schedule/Cron):** You can configure a workflow to run automatically at specific times using a cron schedule.

When you trigger a run (either manually or via cron), the system first records that a new execution has started. This creates a unique "Workflow Execution" record in the database for this specific run.

Look at the `RunWorkflow` action, which is triggered when you click the "Run" button:

```typescript
// Inside actions/workflows/runWorkflow.ts (simplified)
export async function RunWorkflow(form: { workflowId: string; flowDefinition?: string }) {
  // ... authentication and workflow lookup ...

  // Determine the execution plan from the saved definition or draft
  let executionPlan: WorkflowExecutionPlan;
  // ... logic to load/generate executionPlan ...

  // Create a record in the database for this specific run
  const execution = await prisma.workflowExecution.create({
    data: {
      workflowId,
      userId, // Who started it
      status: WorkflowExecutionStatus.PENDING, // Starts as PENDING
      startedAt: new Date(), // Record start time
      trigger: WorkflowExecutionTrigger.MANUAL, // or CRON
      definition: workflowDefinition, // Store the blueprint snapshot
      // Prepare initial phases based on the execution plan
      phases: { create: executionPlan.flatMap(...) },
    },
    select: { id: true, phases: true }, // Get the new execution ID
  });

  if (!execution) { throw new Error("workflow execution not created"); }

  // Start the actual execution process in the background
  ExecuteWorkflow(execution.id); // This function does the work!

  // Redirect user to the execution viewing page
  redirect(`/workflow/runs/${workflowId}/${execution.id}`);
}
```

This code snippet shows that clicking "Run" initiates a server action that:
1.  Prepares the instructions (`executionPlan`) based on your workflow's definition.
2.  Creates a new `workflowExecution` entry in the database with a unique ID, setting its initial status to `PENDING`.
3.  Critically, it calls the `ExecuteWorkflow` function, passing the ID of the newly created execution record. This `ExecuteWorkflow` function is the core engine that will carry out the automation steps in the background.

The cron trigger works similarly, calling an API endpoint (`app/api/workflows/execute/route.ts`) which in turn uses `ExecuteWorkflow`.

### The Execution Engine (`ExecuteWorkflow`)

The `ExecuteWorkflow` function (found in `lib/workflow/executeWorkflow.ts`) is the central piece of code responsible for actually running your workflow. It doesn't run *in* the user's browser, but on the server in the background.

Here's a simplified look at its main steps:

```typescript
// Inside lib/workflow/executeWorkflow.ts (simplified)
export async function ExecuteWorkflow(executionId: string, nextRunAt?: Date) {
  // 1. Get the execution details and workflow definition from the database
  const execution = await prisma.workflowExecution.findUnique({ /* ... */ });
  if (!execution) { /* handle error */ }

  // 2. Initialize the state for this run (environment, browser, etc.)
  const environment: Environment = { phases: {} };
  // ... update status to RUNNING in DB ...

  let creditsConsumed = 0;
  let executionFailed = false;

  // 3. Loop through each phase (group of tasks that can run together)
  for (const phase of execution.phases) {
    // 4. Execute the tasks within the current phase
    const phaseExecution = await executeWorkflowPhase(
      phase, environment, edges, execution.userId
    );
    creditsConsumed += phaseExecution.creditsConsumed;

    // 5. If any task in the phase failed, stop the execution
    if (!phaseExecution.success) {
      executionFailed = true;
      break; // Stop the loop
    }
  }

  // 6. Finalize the execution (update final status, total credits)
  await finalizeWorkflowExecution(
    executionId, execution.workflowId, executionFailed, creditsConsumed
  );

  // 7. Clean up resources (like closing the browser)
  await cleanupEnvironment(environment);

  // 8. Revalidate UI data so the user sees the results
  revalidatePath("/workflows/runs");
}
```

This function orchestrates the entire process. It fetches the execution record, loops through the steps defined in the workflow's [Execution Plan](06_execution_plan_.md) (represented as "phases" and "nodes" within those phases), calls helper functions to run each individual task, tracks state, handles errors, and cleans up.

### Running Individual Tasks (`executeWorkflowPhase`)

The `executeWorkflowPhase` function is called by `ExecuteWorkflow` for each phase (or essentially, each task node) that needs to run. It's responsible for:

1.  Setting up the "environment" for the specific task node, providing its inputs based on prior tasks' outputs or manual values.
2.  Looking up the correct **Executor** function for the task's type (like a `NavigateUrlExecutor` for a "Navigate URL" task, or an `AgentExecutor` for an "AI Agent" task). We'll cover Executors more in a later chapter ([Task & Executor Registry](08_task___executor_registry_.md)).
3.  Calling the Executor function. This is where the actual work of the task happens (e.g., controlling a browser, calling an LLM).
4.  Collecting logs generated by the Executor.
5.  Handling credits deduction before running the task.
6.  Storing the outputs generated by the Executor.
7.  Updating the status of *that specific phase* in the database.

```typescript
// Inside lib/workflow/executeWorkflow.ts (simplified executeWorkflowPhase)
async function executeWorkflowPhase(
  phase: ExecutionPhase, environment: Environment, edges: Edge[], userId: string
) {
  const logCollector = createLogCollector();
  const node = JSON.parse(phase.node) as AppNode; // The task node from the definition

  // Prepare inputs for this specific node instance
  setupEnvironmentForPhase(node, environment, edges);

  // Update the phase status to RUNNING in the database
  await prisma.executionPhase.update({ /* ... set status=RUNNING, startedAt, inputs ... */ });

  // Calculate and decrement credits required for this task
  let creditsRequired = TaskRegistry[node.data.type as TaskType]?.credits || 0;
  if (node.data.type === AGENT_TYPE) creditsRequired = AgentTask.credits;

  let success = await decrementCredits(userId, creditsRequired, logCollector);
  const creditsConsumed = success ? creditsRequired : 0;

  if (success) { // Only run if credits are sufficient
    // Find and call the specific function (Executor) that knows how to run this task type
    let runFn = ExecutorRegistry[node.data.type as TaskType];
    if (node.data.type === AGENT_TYPE) runFn = ExecutorRegistry[AGENT_TYPE];

    if (!runFn) { logCollector.error(`not found executor for ${node.data.type}`); success = false; }
    else {
       // Create a specific environment object for the executor function
       const executionEnvironment: ExecutionEnvironment<any> =
         createExecutionEnvironment(node, environment, logCollector);
       success = await runFn(executionEnvironment); // *** Execute the task logic! ***
    }
  }


  // Finalize the phase (update status, save outputs and logs)
  const outputs = environment.phases[node.id].outputs;
  await finalizePhase(phase.id, success, outputs, logCollector, creditsConsumed);

  return { success, creditsConsumed };
}
```

This function bridges the gap between the generic execution loop (`ExecuteWorkflow`) and the specific logic for each task type (the Executors). It provides the Executor with the necessary inputs and collects its outputs and logs.

The `setupEnvironmentForPhase` function (also in `lib/workflow/executeWorkflow.ts`) is key to preparing the inputs. It looks at the edges connected to the current node's inputs and pulls the corresponding output values from the `environment` object (which holds results from previously executed tasks). If an input is not connected by an edge, it uses the manual value entered in the [Flow Editor](04_flow_editor_.md) (if any).

```typescript
// Inside lib/workflow/executeWorkflow.ts (simplified setupEnvironmentForPhase)
function setupEnvironmentForPhase(
  node: AppNode, environment: Environment, edges: Edge[]
) {
  // Initialize inputs and outputs for this phase's node
  environment.phases[node.id] = { inputs: {}, outputs: {} };

  // Get expected inputs from the Task definition (TaskRegistry or AgentTask)
  const taskDefinition = (node.data.type === AGENT_TYPE ? AgentTask : TaskRegistry[node.data.type as TaskType]);
  const expectedInputs = taskDefinition?.inputs || [];

  for (const input of expectedInputs) {
    // Check if a manual value was set in the editor
    const manualValue = node.data.inputs[input.name];
    if (manualValue) {
      environment.phases[node.id].inputs[input.name] = manualValue;
      continue; // Use manual value if present
    }

    // If no manual value, look for a connected edge
    const connectedEdge = edges.find(
      (edge) => edge.target === node.id && edge.targetHandle === input.name
    );

    if (!connectedEdge) {
      // Input is required but has no manual value or connection
      console.error(`Input "${input.name}" for node "${node.id}" is missing source.`);
      // You might add a validation check here before execution starts
      continue; // Or handle as an error
    }

    // Get the output value from the source node's phase in the environment
    const outputValue =
      environment.phases[connectedEdge.source].outputs[connectedEdge.sourceHandle!];

    environment.phases[node.id].inputs[input.name] = outputValue;
  }
}
```

This function demonstrates how the connections you drew in the [Flow Editor](04_flow_editor_.md) (`edges`) dictate where a task node gets its inputs during execution – either from a manual value saved with the node or from the `outputs` stored in the `environment` from a preceding task.

### Tracking Status and Credits

As the workflow runs, the system constantly updates the status of the overall execution and each individual phase (task instance). It also keeps a running tally of credits consumed.

*   The `workflowExecution` record starts as `PENDING`, changes to `RUNNING` when `ExecuteWorkflow` starts, and becomes `COMPLETED` or `FAILED` at the end.
*   Each `executionPhase` record starts as `CREATED`, changes to `PENDING` when the execution starts, `RUNNING` when `executeWorkflowPhase` starts for it, and `COMPLETED` or `FAILED` when `finalizePhase` is called.
*   Credits are deducted *before* a task runs (`decrementCredits`) and the amount is recorded in the `executionPhase` and summed up in the `workflowExecution` record (`finalizeWorkflowExecution`).

```typescript
// Inside lib/workflow/executeWorkflow.ts (simplified decrementCredits)
async function decrementCredits(
  userId: string, amount: number, logCollector: LogCollector
): Promise<boolean> {
  try {
    // Attempt to deduct the amount from the user's balance,
    // but ONLY if they have enough credits (gte: amount)
    await prisma.userBalance.update({
      where: { userId, credits: { gte: amount } }, // Condition: credits must be >= amount
      data: { credits: { decrement: amount } }, // Action: decrement credits
    });
    logCollector.info(`Credits deducted: ${amount}`);
    return true; // Deduction successful
  } catch (error) {
    // This catch block is hit if the update fails (e.g., insufficient balance)
    logCollector.error("Insufficient balance or other credit deduction error.");
    return false; // Deduction failed
  }
}
```

This `decrementCredits` function shows how the system checks if the user has enough credits *before* allowing a task to run and updates the user's balance directly in the database.

### Logging

Throughout the execution, tasks and the execution engine generate logs. These logs record what's happening, including informational messages, warnings, and errors.

The `logCollector` object, passed to the `ExecutionEnvironment` for each Executor, is used for this:

```typescript
// Inside types/executor.ts (simplified ExecutionEnvironment)
export type ExecutionEnvironment<T extends WorkflowTask> = {
  // ... other properties ...
  log: LogCollector; // Provides methods like log.info(), log.error()
};

// Inside lib/log.ts (simplified createLogCollector)
export function createLogCollector(): LogCollector {
  const logs: Log[] = []; // Store logs in memory during execution

  return {
    info: (message) => logs.push({ level: LogLevel.INFO, message, timestamp: new Date() }),
    error: (message) => logs.push({ level: LogLevel.ERROR, message, timestamp: new Date() }),
    // ... warn, debug methods ...
    getAll: () => logs, // Get all collected logs
  };
}
```

When a phase finishes (`finalizePhase`), all logs collected for that phase are saved to the database, linked to the `executionPhase` record.

### Viewing Execution Results

After triggering a workflow run, you're typically redirected to the "Workflow Runs" page (`app/workflow/runs/[workflowId]/page.tsx`), which lists all past and current executions for that workflow:

```typescript
// Inside app/workflow/runs/[workflowId]/page.tsx (simplified)
async function ExecutionsTableWrapper({ workflowId }: { workflowId: string }) {
  // Fetches all execution records for this workflow from the DB
  const executions = await GetWorkflowExecutions(workflowId);

  if (executions.length === 0) { /* Show "no runs yet" message */ }

  // Render a table showing key details for each execution
  return (
    <ExecutionsTable workflowId={workflowId} initialData={executions} />
  );
}
```

The `ExecutionsTable` component displays key information like the execution ID, status, duration, and credits consumed (from the `workflowExecution` record).

Clicking on a specific run in the table takes you to the "Execution Details" page (`app/workflow/runs/[workflowId]/[executionId]/page.tsx`), where you can see more detailed information:

```typescript
// Inside app/workflow/runs/[workflowId]/[executionId]/page.tsx (simplified)
async function ExecutionViewerWrapper({ executionId }: { executionId: string }) {
  // Fetches the specific execution and its phases from the DB
  const workflowExecution = await GetWorkflowExecutionWithPhases(executionId);
  if (!workflowExecution) { /* handle not found */ }

  // Renders the detailed viewer component
  return <ExecutionViewer initialData={workflowExecution} />;
}
```

The `ExecutionViewer` component (`app/workflow/runs/[workflowId]/[executionId]/_components/ExecutionViewer.tsx`) provides a sidebar listing each phase (task) of the execution with its status. When you click on a phase, it shows:

*   Credits consumed by that phase.
*   Duration of that phase.
*   The **inputs** the task received (based on the `inputs` saved in the `executionPhase` record).
*   The **outputs** the task generated (based on the `outputs` saved in the `executionPhase` record).
*   The **logs** generated by that task (fetched via `GetWorkflowPhaseDetails` and displayed using the `LogViewer` component).

This detailed view allows you to troubleshoot why a workflow might have failed or verify the data that was processed and generated at each step.

### Execution Flow Diagram

Here's a simplified sequence diagram showing the main steps of a Workflow Execution:

```mermaid
sequenceDiagram
    participant User
    participant Frontend as Web UI
    participant Backend as Server (Actions/API)
    participant Executor as Workflow Executor (lib/workflow/executeWorkflow.ts)
    participant Executors as Task-Specific Executors (lib/workflow/executor/*)
    participant DB as Database

    User->>Frontend: Click "Run Workflow"
    Frontend->>Backend: Call runWorkflow Action
    Backend->>DB: Create WorkflowExecution record (Status: PENDING)
    DB-->>Backend: New Execution ID
    Backend->>Executor: Start ExecuteWorkflow(Execution ID) in background
    loop For each phase/task in Execution Plan
        Executor->>DB: Update Phase Status (RUNNING)
        Executor->>DB: Check/Decrement Credits
        alt Credits Sufficient
            Executor->>Executors: Call specific Task Executor (e.g., NavigateUrlExecutor)
            Executors-->>Executor: Task Success/Failure, Output Data, Logs
            Executor->>DB: Update Phase Status (COMPLETED/FAILED), Save Outputs, Save Logs
        else Insufficient Credits
            Executor->>DB: Update Phase Status (FAILED), Save "Insufficient Balance" Log
            Executor->>Executor: Stop execution loop
        end
    end
    Executor->>DB: Update WorkflowExecution Status (COMPLETED/FAILED), total credits
    Executor->>Executor: Cleanup resources (e.g., close browser)
    Executor->>Backend: Notify completion (or status update)
    Backend->>Frontend: Trigger UI update
    Frontend->>User: Show updated status/results
    User->>Frontend: View Execution Details
    Frontend->>Backend: Call GetWorkflowExecutionWithPhases
    Backend->>DB: Fetch Execution, Phases, Logs
    DB-->>Backend: Execution Data
    Backend-->>Frontend: Execution Data
    Frontend->>User: Display details (Status, Logs, Inputs, Outputs)
```

This diagram illustrates how the different parts of the system work together to execute a workflow, from the initial trigger to viewing the final results and logs.

### Conclusion

In this chapter, you learned that **Workflow Execution** is the crucial step of running your saved automation blueprint. It involves the `ExecuteWorkflow` engine processing the [Execution Plan](06_execution_plan_.md) phase by phase, calling specific **Executors** for each [Task](02_tasks_.md), managing inputs and outputs, handling resources, tracking status, deducting credits, and collecting logs. You also saw how the system creates a record for each run and provides a user interface to view the status, details, and results of your workflow executions, allowing you to monitor and troubleshoot your automations.

The next chapter will dive deeper into the **[Execution Plan](06_execution_plan_.md)**, explaining how the visual design you create in the [Flow Editor](04_flow_editor_.md) is translated into the structured plan that the `ExecuteWorkflow` function follows.

[Execution Plan](06_execution_plan_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)