"""
Gradio UI for the Agentic RAG system.
Supports Turkish characters and multilingual interface.

Turkish Character Support Features:
- UTF-8 encoding configuration for Windows compatibility
- Proper charset headers in all API requests
- Bilingual UI labels (Turkish/English)
- Turkish character preservation in chat messages
- Error messages in both languages
- Proper encoding for API responses

Supported Turkish characters: ğ, Ğ, ı, İ, ö, Ö, ü, Ü, ş, Ş, ç, Ç
"""

import requests
import gradio as gr
import sys
import os

# Set up UTF-8 encoding for Windows compatibility
if sys.platform == "win32":
    import codecs

    # Ensure proper UTF-8 handling for console output
    if hasattr(sys.stdout, "buffer"):
        sys.stdout = codecs.getwriter("utf-8")(sys.stdout.buffer, "strict")
    if hasattr(sys.stderr, "buffer"):
        sys.stderr = codecs.getwriter("utf-8")(sys.stderr.buffer, "strict")

# Set environment variable for UTF-8 encoding
os.environ["PYTHONIOENCODING"] = "utf-8"

# API URL
API_URL = "http://localhost:8000"


def ensure_utf8_response(response):
    """Ensure proper UTF-8 encoding for API responses."""
    if response.encoding != "utf-8":
        response.encoding = "utf-8"
    return response


def get_bots():
    """Get the list of available bots from the API."""
    try:
        response = requests.get(
            f"{API_URL}/bots", headers={"Accept": "application/json; charset=utf-8"}
        )
        response = ensure_utf8_response(response)
        if response.status_code == 200:
            data = response.json()
            return data.get("bots", [])
        else:
            return []
    except Exception as e:
        print(f"Error getting bots: {str(e)}")
        return []


def get_bot_info(bot_name):
    """Get information about a specific bot."""
    try:
        response = requests.get(
            f"{API_URL}/bots/{bot_name}",
            headers={"Accept": "application/json; charset=utf-8"},
        )
        response = ensure_utf8_response(response)
        if response.status_code == 200:
            return response.json()
        else:
            return None
    except Exception as e:
        print(f"Error getting bot info: {str(e)}")
        return None


def format_bot_info(bot_info):
    """Format bot information for display with Turkish support."""
    if not bot_info:
        return "Hiçbir bot seçilmedi. / No bot selected."

    info = f"## {bot_info['name']}\n\n"
    info += f"**Description**: {bot_info['description']}\n\n"

    if bot_info.get("tools"):
        info += "**Tools**:\n"
        for tool in bot_info["tools"]:
            info += f"- {tool}\n"

    if bot_info.get("metadata"):
        info += "\n**Metadata**:\n"
        for key, value in bot_info["metadata"].items():
            if isinstance(value, list):
                info += f"- **{key}**: {', '.join(map(str, value))}\n"
            else:
                info += f"- **{key}**: {value}\n"

    return info


def query_bot(bot_name, message, chat_history):
    """Send a query to the bot and get the response."""
    if not bot_name:
        return "Lütfen önce bir bot seçin. / Please select a bot first.", chat_history

    try:
        payload = {"query": message, "session_id": f"gradio-session-{bot_name}"}

        response = requests.post(
            f"{API_URL}/bots/{bot_name}/query",
            json=payload,
            headers={"Content-Type": "application/json; charset=utf-8"},
        )
        response = ensure_utf8_response(response)

        if response.status_code == 200:
            data = response.json()
            bot_response = data.get(
                "response", "Bot'tan yanıt alınamadı. / No response from bot."
            )
            chat_history.append({"role": "user", "content": message})
            chat_history.append({"role": "assistant", "content": bot_response})
            return "", chat_history
        else:
            error_msg = f"Hata / Error: {response.status_code} - {response.text}"
            chat_history.append({"role": "user", "content": message})
            chat_history.append({"role": "assistant", "content": error_msg})
            return "", chat_history
    except Exception as e:
        error_msg = f"Bot sorgulanırken hata oluştu / Error querying bot: {str(e)}"
        chat_history.append({"role": "user", "content": message})
        chat_history.append({"role": "assistant", "content": error_msg})
        return "", chat_history


def on_bot_change(bot_name):
    """Handle bot selection change."""
    if not bot_name:
        return "Hiçbir bot seçilmedi. / No bot selected.", []

    bot_info = get_bot_info(bot_name)
    info_text = format_bot_info(bot_info)

    # When changing bots, we start a new conversation
    # The memory is tied to the session ID, so a new conversation will have a fresh memory

    return info_text, []


def process_documents(directory_path, collection_name, recursive):
    """Process documents in a directory."""
    if not directory_path or not collection_name:
        return (
            "Lütfen dizin yolu ve koleksiyon adını girin. / Please enter directory path and collection name.",
            "",
        )

    try:
        payload = {
            "directory_path": directory_path,
            "collection_name": collection_name,
            "recursive": recursive,
        }

        response = requests.post(
            f"{API_URL}/process-documents",
            json=payload,
            headers={"Content-Type": "application/json; charset=utf-8"},
        )
        response = ensure_utf8_response(response)

        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                result_msg = f"✅ Başarılı! / Success!\n\n"
                result_msg += (
                    f"📁 Dizin / Directory: {data.get('directory_path', 'N/A')}\n"
                )
                result_msg += f"📚 Koleksiyon / Collection: {data.get('collection_name', 'N/A')}\n"
                result_msg += (
                    f"📄 Toplam dosya / Total files: {data.get('total_files', 0)}\n"
                )
                result_msg += (
                    f"✅ Başarılı / Successful: {data.get('successful_count', 0)}\n"
                )
                result_msg += (
                    f"❌ Başarısız / Failed: {data.get('failed_count', 0)}\n\n"
                )
                result_msg += f"Mesaj / Message: {data.get('message', '')}"

                # Clear the input fields on success
                return result_msg, "", ""
            else:
                error_msg = f"❌ Hata / Error: {data.get('error', 'Unknown error')}"
                return error_msg, directory_path, collection_name
        else:
            error_msg = (
                f"❌ API Hatası / API Error: {response.status_code} - {response.text}"
            )
            return error_msg, directory_path, collection_name

    except Exception as e:
        error_msg = (
            f"❌ Döküman işlenirken hata oluştu / Error processing documents: {str(e)}"
        )
        return error_msg, directory_path, collection_name


def process_custom_path(file_path, collection_name, recursive):
    """Process documents from a custom user-specified path."""
    if not file_path or not collection_name:
        return (
            "Lütfen dosya/dizin yolu ve koleksiyon adını girin. / Please enter file/directory path and collection name.",
            "",
        )

    try:
        payload = {
            "file_path": file_path,
            "collection_name": collection_name,
            "recursive": recursive,
            "custom_metadata": {
                "source_type": "custom_user_upload",
                "uploaded_by": "user_interface",
            },
        }

        response = requests.post(
            f"{API_URL}/process-custom-path",
            json=payload,
            headers={"Content-Type": "application/json; charset=utf-8"},
        )
        response = ensure_utf8_response(response)

        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                result_msg = f"✅ Başarılı! / Success!\n\n"

                # Check if it was a file or directory
                total_files = data.get("total_files", 0)
                if total_files == 1:
                    result_msg += f"📄 Dosya / File: {file_path}\n"
                else:
                    result_msg += (
                        f"📁 Dizin / Directory: {data.get('directory_path', 'N/A')}\n"
                    )

                result_msg += f"📚 Koleksiyon / Collection: {data.get('collection_name', 'N/A')}\n"
                result_msg += f"📄 Toplam dosya / Total files: {total_files}\n"
                result_msg += (
                    f"✅ Başarılı / Successful: {data.get('successful_count', 0)}\n"
                )
                result_msg += (
                    f"❌ Başarısız / Failed: {data.get('failed_count', 0)}\n\n"
                )
                result_msg += f"Mesaj / Message: {data.get('message', '')}"

                # Clear the input fields on success
                return result_msg, "", ""
            else:
                error_msg = f"❌ Hata / Error: {data.get('error', 'Unknown error')}"
                return error_msg, file_path, collection_name
        else:
            error_msg = (
                f"❌ API Hatası / API Error: {response.status_code} - {response.text}"
            )
            return error_msg, file_path, collection_name

    except Exception as e:
        error_msg = f"❌ Dosya işlenirken hata oluştu / Error processing file: {str(e)}"
        return error_msg, file_path, collection_name


def get_source_directories():
    """Get available source directories."""
    try:
        response = requests.get(f"{API_URL}/source-directories")
        response = ensure_utf8_response(response)

        if response.status_code == 200:
            data = response.json()
            return data.get("source_directories", {})
        else:
            return {}
    except Exception as e:
        print(f"Error getting source directories: {e}")
        return {}


def process_source_directory(source_type, collection_name):
    """Process documents from a specific source directory."""
    if not source_type or not collection_name:
        return "Lütfen kaynak türü ve koleksiyon adını seçin. / Please select source type and collection name."

    try:
        payload = {
            "source_type": source_type,
            "collection_name": collection_name,
        }

        response = requests.post(
            f"{API_URL}/process-source-directory",
            json=payload,
            headers={"Content-Type": "application/json; charset=utf-8"},
        )
        response = ensure_utf8_response(response)

        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                result_msg = f"✅ Başarılı! / Success!\n\n"
                result_msg += f"📁 Kaynak türü / Source type: {source_type}\n"
                result_msg += f"📚 Koleksiyon / Collection: {data.get('collection_name', 'N/A')}\n"
                result_msg += (
                    f"📄 Toplam dosya / Total files: {data.get('total_files', 0)}\n"
                )
                result_msg += (
                    f"✅ Başarılı / Successful: {data.get('successful_count', 0)}\n"
                )
                result_msg += (
                    f"❌ Başarısız / Failed: {data.get('failed_count', 0)}\n\n"
                )
                result_msg += f"Mesaj / Message: {data.get('message', '')}"
                return result_msg
            else:
                return f"❌ Hata / Error: {data.get('error', 'Unknown error')}"
        else:
            return (
                f"❌ API Hatası / API Error: {response.status_code} - {response.text}"
            )
    except Exception as e:
        return f"❌ Hata / Error: {str(e)}"


def process_default_source(collection_name):
    """Process documents from the default source directory."""
    if not collection_name:
        return "Lütfen koleksiyon adını girin. / Please enter collection name."

    try:
        response = requests.post(
            f"{API_URL}/process-default-source",
            params={"collection_name": collection_name},
            headers={"Content-Type": "application/json; charset=utf-8"},
        )
        response = ensure_utf8_response(response)

        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                result_msg = f"✅ Başarılı! / Success!\n\n"
                result_msg += f"📁 Kaynak / Source: Default (data/all_docs)\n"
                result_msg += f"📚 Koleksiyon / Collection: {data.get('collection_name', 'N/A')}\n"
                result_msg += (
                    f"📄 Toplam dosya / Total files: {data.get('total_files', 0)}\n"
                )
                result_msg += (
                    f"✅ Başarılı / Successful: {data.get('successful_count', 0)}\n"
                )
                result_msg += (
                    f"❌ Başarısız / Failed: {data.get('failed_count', 0)}\n\n"
                )
                result_msg += f"Mesaj / Message: {data.get('message', '')}"
                return result_msg
            else:
                return f"❌ Hata / Error: {data.get('error', 'Unknown error')}"
        else:
            return (
                f"❌ API Hatası / API Error: {response.status_code} - {response.text}"
            )
    except Exception as e:
        return f"❌ Hata / Error: {str(e)}"


def create_ui():
    """Create the Gradio UI."""
    # Get the list of bots
    bots = get_bots()
    bot_names = [bot["name"] for bot in bots]

    # Create the UI with Turkish support
    with gr.Blocks(
        title="Atlas Üniversitesi Chatbotları / Atlas University Chatbots",
        theme=gr.themes.Soft(),
    ) as demo:
        gr.Markdown("# Atlas Üniversitesi Chatbotları / Atlas University Chatbots")
        gr.Markdown(
            "Bir chatbot seçin ve konuşmaya başlayın. Chatbotlar hem Türkçe hem de İngilizce destekler.\n\n"
            "Select a chatbot and start a conversation. The chatbots support both English and Turkish."
        )

        with gr.Tabs():
            with gr.TabItem("💬 Chatbot / Sohbet"):
                create_chat_interface(bot_names)

            with gr.TabItem("📚 Döküman Embedding / Document Embedding"):
                create_document_interface()

            with gr.TabItem("📁 Kaynak Dizin İşleme / Source Directory Processing"):
                create_source_directory_interface()

            with gr.TabItem("📂 Özel Yol İşleme / Custom Path Processing"):
                create_custom_path_interface()

    return demo


def create_source_directory_interface():
    """Create the source directory processing interface."""
    # Get available source directories
    source_dirs = get_source_directories()
    source_types = (
        list(source_dirs.keys())
        if source_dirs
        else ["default", "academic", "student", "admin", "atlasiq"]
    )

    gr.Markdown(
        "### 📁 Kaynak Dizin İşleme / Source Directory Processing\n\n"
        "Bu bölümde önceden tanımlanmış kaynak dizinlerden dökümanları işleyebilirsiniz.\n\n"
        "Process documents from predefined source directories."
    )

    with gr.Row():
        with gr.Column():
            gr.Markdown("#### 🎯 Hızlı İşleme / Quick Processing")

            # Default source processing
            with gr.Group():
                gr.Markdown("**Default Kaynak Dizini / Default Source Directory**")
                gr.Markdown(
                    "Tüm dökümanları data/all_docs dizininden işler / Processes all documents from data/all_docs"
                )

                default_collection_input = gr.Textbox(
                    label="Koleksiyon Adı / Collection Name",
                    placeholder="Örnek: all_documents",
                    value="",
                )

                default_process_btn = gr.Button(
                    "🚀 Default Kaynak İşle / Process Default Source",
                    variant="primary",
                )

            # Specific source type processing
            with gr.Group():
                gr.Markdown("**Belirli Kaynak Türü / Specific Source Type**")

                source_type_dropdown = gr.Dropdown(
                    choices=source_types,
                    label="Kaynak Türü / Source Type",
                    value=source_types[0] if source_types else None,
                )

                source_collection_input = gr.Textbox(
                    label="Koleksiyon Adı / Collection Name",
                    placeholder="Örnek: academic_papers",
                    value="",
                )

                source_process_btn = gr.Button(
                    "📁 Kaynak Türü İşle / Process Source Type",
                    variant="secondary",
                )

        with gr.Column():
            gr.Markdown("#### 📊 Sonuçlar / Results")

            source_result_output = gr.Textbox(
                label="İşlem Sonucu / Processing Result",
                lines=15,
                max_lines=20,
                value="İşlem sonucu burada görünecek. / Processing result will appear here.",
                interactive=False,
            )

            # Show available source directories
            if source_dirs:
                gr.Markdown(
                    "#### 📂 Mevcut Kaynak Dizinleri / Available Source Directories"
                )
                source_info = ""
                for source_type, path in source_dirs.items():
                    source_info += f"**{source_type}**: `{path}`\n\n"
                gr.Markdown(source_info)

    # Set up event handlers
    default_process_btn.click(
        process_default_source,
        inputs=[default_collection_input],
        outputs=[source_result_output],
    )

    source_process_btn.click(
        process_source_directory,
        inputs=[source_type_dropdown, source_collection_input],
        outputs=[source_result_output],
    )


def create_chat_interface(bot_names):
    """Create the chat interface."""
    with gr.Row():
        with gr.Column(scale=1):
            bot_dropdown = gr.Dropdown(
                choices=bot_names,
                label="Chatbot Seçin / Select a Chatbot",
                info="Konuşmak istediğiniz chatbotu seçin / Choose which chatbot you want to talk to",
            )
            bot_info = gr.Markdown("Hiçbir bot seçilmedi. / No bot selected.")

        with gr.Column(scale=2):
            chatbot = gr.Chatbot(height=500, type="messages")
            msg = gr.Textbox(
                show_label=False,
                placeholder="Mesajınızı buraya yazın... / Type your message here...",
                container=False,
            )
            clear = gr.Button("Temizle / Clear")

    # Set up event handlers
    bot_dropdown.change(
        on_bot_change, inputs=[bot_dropdown], outputs=[bot_info, chatbot]
    )

    msg.submit(query_bot, inputs=[bot_dropdown, msg, chatbot], outputs=[msg, chatbot])

    # Clear chat history and reset memory
    def clear_chat(bot_name):
        if bot_name:
            # Clear memory for the current session
            try:
                session_id = f"gradio-session-{bot_name}"
                # Call the clear-memory endpoint
                response = requests.post(
                    f"{API_URL}/bots/{bot_name}/clear-memory?session_id={session_id}",
                    headers={"Content-Type": "application/json; charset=utf-8"},
                )
                response = ensure_utf8_response(response)
                if response.status_code != 200:
                    print(f"Error clearing memory: {response.text}")
            except Exception as e:
                print(f"Error clearing memory: {str(e)}")
        return []

    clear.click(clear_chat, inputs=[bot_dropdown], outputs=[chatbot])

    # Initialize with the first bot if available
    if bot_names:
        bot_info_text = format_bot_info(get_bot_info(bot_names[0]))
        bot_info.value = bot_info_text
        bot_dropdown.value = bot_names[0]


def create_document_interface():
    """Create the document processing interface."""
    gr.Markdown("## 📚 Döküman Embedding İşlemi / Document Embedding Process")
    gr.Markdown(
        "Bu bölümde dizinlerdeki dökümanları işleyerek chatbot'larda kullanılabilir hale getirebilirsiniz.\n\n"
        "In this section, you can process documents in directories to make them available for use in chatbots.\n\n"
        "**Desteklenen formatlar / Supported formats:** PDF, DOCX, DOC, TXT, MD"
    )

    with gr.Row():
        with gr.Column():
            directory_input = gr.Textbox(
                label="📁 Dizin Yolu / Directory Path",
                placeholder="Örnek / Example: data/all_docs/myform",
                info="İşlenecek dökümanların bulunduğu dizin yolu / Path to directory containing documents to process",
            )

            collection_input = gr.Textbox(
                label="📚 Koleksiyon Adı / Collection Name",
                placeholder="Örnek / Example: myform_documents",
                info="Dökümanların saklanacağı koleksiyon adı / Name of collection to store documents",
            )

            recursive_checkbox = gr.Checkbox(
                label="🔄 Alt dizinleri de işle / Process subdirectories",
                value=True,
                info="Alt dizinlerdeki dosyaları da işlemek için işaretleyin / Check to also process files in subdirectories",
            )

            process_btn = gr.Button(
                "🚀 Dökümanları İşle / Process Documents", variant="primary"
            )

        with gr.Column():
            result_output = gr.Markdown(
                "İşlem sonucu burada görünecek. / Processing result will appear here."
            )

    # Set up event handler
    process_btn.click(
        process_documents,
        inputs=[directory_input, collection_input, recursive_checkbox],
        outputs=[result_output, directory_input, collection_input],
    )


def create_custom_path_interface():
    """Create the custom path processing interface."""
    gr.Markdown("### 📂 Özel Yol İşleme / Custom Path Processing")
    gr.Markdown(
        "Bu bölümde kendi belirlediğiniz dosya veya dizin yolundan dökümanları işleyebilirsiniz.\n\n"
        "In this section, you can process documents from your own specified file or directory path.\n\n"
        "**Desteklenen formatlar / Supported formats:** PDF, DOCX, DOC, TXT, MD\n\n"
        "**Örnekler / Examples:**\n"
        "- Tek dosya / Single file: `C:\\Users\\<USER>\\mydoc.pdf`\n"
        "- Dizin / Directory: `C:\\Users\\<USER>\\MyProject\\docs`\n"
        "- Relatif yol / Relative path: `data/my_custom_docs`"
    )

    with gr.Row():
        with gr.Column():
            gr.Markdown("#### 📝 İşlem Parametreleri / Processing Parameters")

            custom_path_input = gr.Textbox(
                label="📂 Dosya/Dizin Yolu / File/Directory Path",
                placeholder="Örnek / Example: C:\\Users\\<USER>\\MyDocs",
                info="İşlenecek dosya veya dizin yolunu girin / Enter the path to file or directory to process",
                lines=1,
            )

            custom_collection_input = gr.Textbox(
                label="📚 Koleksiyon Adı / Collection Name",
                placeholder="Örnek / Example: my_custom_collection",
                info="Dökümanların saklanacağı koleksiyon adı / Name of collection to store documents",
            )

            custom_recursive_checkbox = gr.Checkbox(
                label="🔄 Alt dizinleri de işle / Process subdirectories",
                value=True,
                info="Dizin seçtiyseniz, alt dizinlerdeki dosyaları da işlemek için işaretleyin / If directory selected, check to also process files in subdirectories",
            )

            custom_process_btn = gr.Button(
                "🚀 Özel Yolu İşle / Process Custom Path", variant="primary"
            )

        with gr.Column():
            gr.Markdown("#### 📊 Sonuçlar / Results")

            custom_result_output = gr.Textbox(
                label="İşlem Sonucu / Processing Result",
                lines=15,
                max_lines=20,
                value="İşlem sonucu burada görünecek. / Processing result will appear here.",
                interactive=False,
            )

            gr.Markdown(
                "#### 💡 İpuçları / Tips\n\n"
                "- **Windows yolları / Windows paths:** `C:\\Users\\<USER>\\Documents\\folder`\n"
                "- **Linux/Mac yolları / Linux/Mac paths:** `/home/<USER>/documents/folder`\n"
                "- **Relatif yollar / Relative paths:** `data/docs` (proje dizinine göre)\n"
                "- **Tek dosya / Single file:** Sadece o dosyayı işler / Only processes that file\n"
                "- **Dizin / Directory:** İçindeki tüm desteklenen dosyaları işler / Processes all supported files inside"
            )

    # Set up event handler
    custom_process_btn.click(
        process_custom_path,
        inputs=[custom_path_input, custom_collection_input, custom_recursive_checkbox],
        outputs=[custom_result_output, custom_path_input, custom_collection_input],
    )


if __name__ == "__main__":
    # Create and launch the UI
    import argparse

    parser = argparse.ArgumentParser(description="Agentic RAG UI")
    parser.add_argument("--port", type=int, default=7860, help="Port to run the UI on")
    args = parser.parse_args()

    demo = create_ui()
    demo.launch(server_name="0.0.0.0", server_port=args.port)
