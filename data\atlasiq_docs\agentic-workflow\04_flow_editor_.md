# Chapter 4: Flow Editor

Welcome back! In the [previous chapter](03_ai_agents_.md), we learned about **[Tasks](02_tasks_.md)**, the individual steps in a workflow, and a special type of task called **[AI Agents](03_ai_agents_.md)** that leverage large language models. We know workflows are made up of these tasks linked together, but how do you actually build one? How do you tell the system which task follows which, and how data flows between them?

Instead of writing code or complex configuration files, `agentic-workflow` provides a visual interface – a drawing board – where you can design your automation recipe. This is called the **Flow Editor**.

### What is the Flow Editor?

Imagine you want to create a workflow that:
1.  Goes to a website.
2.   Extracts some specific information (like a phone number or a product list).
3.   Uses an AI Agent to summarize the extracted information.
4.  Saves the summary to a database or sends it via a webhook.

Describing this with just text or code can be a bit tricky to visualize. The Flow Editor makes it easy! It's a **visual interface** where you can:

*   See all your available [Tasks](02_tasks_.md) (including your defined [AI Agents](03_ai_agents_.md)) in a menu.
*   **Drag and drop** these [Tasks](02_tasks_.md) onto a canvas (the main editor area).
*   Each dragged task appears as a **node**.
*   **Draw connections (edges)** between the output of one node and the input of another node to show how data and execution flow.
*   Configure the settings for each node (like the URL for a "Navigate URL" task or which AI Agent to use).

It's like drawing a flowchart for your automation, but interactive!

You can see the Flow Editor when you open or create a workflow. The main component is located at `app/workflow/_components/FlowEditor.tsx`.

```typescript
// Inside app/workflow/editor/[workflowId]/page.tsx (simplified)
import Editor from "@/app/workflow/_components/Editor";
// ... other imports ...

async function page({ params }: { params: { workflowId: string } }) {
  // ... fetch workflow from DB ...

  if (!workflow) {
    return <div>Workflow not found</div>;
  }
  // The Editor component contains the FlowEditor
  return <Editor workflow={workflow} />;
}

export default page;
```

The `Editor.tsx` component then wraps the `FlowEditor.tsx` component, passing it the workflow data fetched from the database.

```typescript
// Inside app/workflow/_components/Editor.tsx (simplified)
import FlowEditor from "@/app/workflow/_components/FlowEditor";
import TaskMenu from "@/app/workflow/_components/TaskMenu";
// ... other imports ...

function Editor({ workflow }: { workflow: Workflow }) {
  return (
    // ... ReactFlowProvider and other context providers ...
    <section className="flex h-full overflow-auto">
      {/* The Task Menu is on the left */}
      <TaskMenu />
      {/* The Flow Editor is the main canvas */}
      <FlowEditor workflow={workflow} />
    </section>
    // ...
  );
}
```

This shows that the main editor screen is composed of the `TaskMenu` (the list of draggable tasks) and the `FlowEditor` itself (the interactive canvas).

### Building Blocks of the Flow Editor

The Flow Editor is built using a library called **React Flow**, which is excellent for creating diagram-like interfaces. The key concepts you interact with are:

1.  **Nodes:** As we saw in [Chapter 2](02_tasks_.md), tasks are represented as nodes. In the editor, these nodes are draggable blocks. Each node represents one step in your workflow. They show the Task's name, icon, and crucially, its **inputs** and **outputs** as small connection points (called "handles").

    ```mermaid
    graph LR
        A[Navigate URL Task] -- "output handle" ---
        --- "input handle" --> B[Extract Data Task]
    ```

    The handles on the left side of a node are its inputs, and the handles on the right side are its outputs.

2.  **Edges:** Edges are the lines you draw to connect the output handle of one node to the input handle of another node. An edge represents the flow of data or control. It shows that the result of the source node will be used as the input for the target node.

    ```mermaid
    graph LR
        A[Navigate URL Task] --> B[Extract Data Task]
        B --> C[AI Agent Task]
        C --> D[Deliver Result Task]
    ```

    This diagram visually represents the workflow we described earlier using nodes (tasks) and edges (connections).

### Using the Flow Editor: Dragging and Dropping Tasks

On the left side of the editor, you'll find the **Task Menu** (`TaskMenu.tsx`). This menu lists all the available [Task types](02_tasks_.md) you can use, often categorized for easier browsing. It also includes your custom [AI Agents](03_ai_agents_.md).

To add a task to your workflow, you simply click and drag an item from the Task Menu onto the main canvas area of the Flow Editor.

The `FlowEditor.tsx` component listens for the `onDrop` event to handle when you drag something from the menu and drop it onto the canvas.

```typescript
// Inside app/workflow/_components/FlowEditor.tsx (simplified onDrop function)
const onDrop = useCallback(
  (event: React.DragEvent) => {
    event.preventDefault();

    const dataStr = event.dataTransfer.getData("application/reactflow");
    if (!dataStr) return;

    // Calculate where to place the new node based on mouse position
    const position = screenToFlowPosition({
      x: event.clientX,
      y: event.clientY,
    });

    let newNode;
    try {
      // Try parsing as JSON first (used for Agents)
      const jsonData = JSON.parse(dataStr);
      if (jsonData && jsonData.type === AGENT_TYPE) {
        // If it's an AGENT, create a specific node
        newNode = CreateFlowNode(jsonData.type as TaskType, position);
        // Set the agent_id input when the node is created
        newNode.data.inputs = {
          agent_id: jsonData.agentId,
          input_text: "", // input_text will likely be connected later
        };
      } else {
        // If JSON parse failed or wasn't an agent, treat as normal task type string
        const taskType = dataStr; // The string is the task type
        if (!taskType) return;
        newNode = CreateFlowNode(taskType as TaskType, position);
      }
    } catch (error) {
       // Handle potential JSON parse errors, assume it's a simple task type string
       const taskType = dataStr;
       if (!taskType) return;
       newNode = CreateFlowNode(taskType as TaskType, position);
    }

    // Add the newly created node to the list of nodes in the editor
    setNodes((nds) => nds.concat(newNode));
  },
  [screenToFlowPosition, setNodes] // These are dependencies for useCallback
);
```

This code shows that when you drop an item:
1.  It prevents the default browser behavior.
2.  It reads the data attached to the drag event. This data is either the simple string name of a `TaskType` (like `"NAVIGATE_URL"`) or, in the case of [AI Agents](03_ai_agents_.md), a JSON string containing the agent's ID and the special `AGENT_TYPE`.
3.  It calculates the position on the canvas where you dropped the item.
4.  It creates a new "node object" using a helper function `CreateFlowNode`. If it's an AI Agent drop, it also pre-fills the `agent_id` input of the new node with the ID of the specific agent you dragged.
5.  Finally, it updates the state (`setNodes`) to add the new node to the editor, making it appear on the canvas.

The `CreateFlowNode` function (defined in `lib/workflow/createFlowNode.ts`, not shown here but mentioned in Chapter 1) is responsible for creating the initial structure of a node based on its `TaskType` and position.

### Using the Flow Editor: Connecting Tasks with Edges

Once you have multiple nodes on the canvas, you need to connect them to define the workflow sequence and data flow. You do this by clicking on an output handle (right side) of one node and dragging a line to an input handle (left side) of another node.

The `FlowEditor.tsx` component uses the `onConnect` prop of `ReactFlow` to handle when you finish drawing a connection.

```typescript
// Inside app/workflow/_components/FlowEditor.tsx (simplified onConnect function)
const onConnect = useCallback(
  (connection: Connection) => {
    // Adds a new edge object to the list of edges
    setEdges((eds) => addEdge({ ...connection, animated: true }, eds));

    // Optional: If the target handle had a manually entered value, clear it
    // because a connection means its value will now come from the source node
    if (!connection.targetHandle) return;
    const node = nodes.find((nd) => nd.id === connection.target);
    if (!node) return;
    const nodeInputs = node.data.inputs;
    updateNodeData(node.id, {
      inputs: {
        ...nodeInputs,
        [connection.targetHandle]: "", // Clear the manual input value
      },
    });
  },
  [setEdges, updateNodeData, nodes] // Dependencies
);
```

This code does two main things:
1.  It takes the information about the connection you just made (which source node output connected to which target node input) and adds a new "edge object" to the list of edges managed by the editor (`setEdges`). React Flow then draws the line.
2.  It performs a helpful cleanup: if the input handle you just connected was previously set with a *manual value* (like typing a URL directly into the input field instead of connecting it from a previous task's output), it clears that manual value. This ensures the input will correctly receive data from the connected source node.

### Configuring Task Inputs

While edges connect outputs to inputs, some inputs need specific values that don't come from another task (like the URL for the very first "Navigate URL" task).

Each node in the editor has a section where you can manually enter values for its inputs if they are not connected. This is handled within the `NodeComponent.tsx` that renders each individual node:

```typescript
// Inside app/workflow/_components/nodes/NodeComponent.tsx (simplified)
import NodeInputs from "./NodeInputs";
// ... other imports ...

const NodeComponent = memo((props: NodeProps) => {
  const nodeData = props.data as AppNodeData;
  // ... lookup task definition ...

  return (
    <NodeCard nodeId={props.id} isSelected={!!props.selected}>
      {/* ... Node Header ... */}
      <NodeInputs>
        {/* Loop through each expected input from the Task Definition */}
        {task.inputs.map((input) => (
          // NodeInput component renders the handle and potential input field
          <NodeInput key={input.name} input={input} nodeId={props.id} />
        ))}
      </NodeInputs>
      {/* ... Node Outputs ... */}
    </NodeCard>
  );
});
```

The `NodeInputs` and `NodeInput` components (part of `app/workflow/_components/nodes/`) are responsible for:
*   Drawing the input handle on the left side of the node.
*   Checking if this input is connected to an edge.
*   If it's *not* connected and is `required` by the task definition, it renders an input field (like a text box or dropdown) where you can manually enter the value.
*   When you type a value, it updates the `inputs` property within that specific node's data (`nodeData.inputs`).

This data (`nodeData.inputs` and `nodeData.outputs`, plus the connections defined by the edges) is crucial. When you save the workflow and later run it, the system uses this saved configuration to execute the steps.

### Saving Your Workflow Design

The visual design you create in the Flow Editor isn't automatically saved as you drag and drop. You need to explicitly save it. While the code for the "Save" button isn't shown in the provided snippets (it's typically in the `Topbar` component), here's what happens when you save:

1.  The editor uses React Flow's `toObject()` function to get the current state of the canvas – a list of all nodes and all edges, including their positions, connections, and the manual input values you've set.
2.  This data (nodes and edges) is formatted into a JSON object.
3.  This JSON object is sent back to the server and saved in the database as the `definition` field of your Workflow record (as discussed in [Chapter 1](01_workflows_.md)).

```typescript
// Conceptually what happens when saving (simplified)
const { nodes, edges, viewport } = useReactFlow().toObject(); // Get current state
const flowDefinition = JSON.stringify({ nodes, edges, viewport }); // Convert to JSON

// Send flowDefinition to backend action to save in DB...
// Example: await updateWorkflowAction(workflowId, flowDefinition);
```

This is how your visual design in the editor is persisted and becomes the blueprint for future [Workflow Executions](05_workflow_execution_.md).

### Flow Validation

The Flow Editor also helps you build valid workflows. For example, it prevents you from drawing connections that don't make sense (like connecting an output that provides HTML to an input that expects a number). It also checks for potential issues like required inputs that haven't been set (either by a connection or a manual value).

```typescript
// Inside app/workflow/_components/FlowEditor.tsx (simplified isValidConnection function)
const isValidConnection = useCallback(
  (connection: Edge | Connection) => {
    // 1. Prevent connecting a node to itself
    if (connection.source === connection.target) {
      return false;
    }

    // 2. Check if source/target nodes exist (sanity check)
    const source = nodes.find((node) => node.id === connection.source);
    const target = nodes.find((node) => node.id === connection.target);
    if (!source || !target) return false;

    // 3. Look up the task definitions for source and target nodes
    // Special handling for AGENT type to use AgentTask definition
    const sourceTask = source.data.type === AGENT_TYPE ? AgentTask : TaskRegistry[source.data.type as TaskType];
    const targetTask = target.data.type === AGENT_TYPE ? AgentTask : TaskRegistry[target.data.type as TaskType];

    if (!sourceTask || !targetTask) return false;

    // 4. Find the specific output and input handles being connected
    const output = sourceTask.outputs.find((o) => o.name === connection.sourceHandle);
    const input = targetTask.inputs.find((i) => i.name === connection.targetHandle);

    // 5. Check if the data types of the output and input match
    if (input?.type !== output?.type) {
      console.error("invalid connection: type mismatch"); // Log error for debugging
      return false; // Don't allow connection
    }

    // 6. (More advanced) Check for cycles (loops) that would prevent execution
    // The hasCycle function checks if connecting these nodes would create a loop
    const detectedCycle = hasCycle(target); // Simplified: hasCycle checks from target back to source
    if (detectedCycle) {
       console.error("invalid connection: cycle detected");
    }
    return !detectedCycle; // Only allow if no cycle detected
  },
  [nodes, edges] // Dependencies
);
// ... later in the ReactFlow component ...
<ReactFlow /* ... props ... */ isValidConnection={isValidConnection}>
  {/* ... */}
</ReactFlow>
```

The `isValidConnection` function is called by `React Flow` every time you try to draw a connection. It checks various rules (like type matching using the `TaskParamType` from the task definitions, and preventing cycles) and returns `true` if the connection is allowed, or `false` otherwise. This immediate feedback helps you build valid workflows.

Validation for required inputs is typically checked when you try to *run* or *publish* the workflow, rather than constantly in the editor, although the editor can visually highlight nodes with missing inputs (using the `FlowValidationContext`). The `useExecutionPlan` hook (shown conceptually in the provided snippets) performs these checks before generating the plan to run the workflow.

### Conclusion

The **Flow Editor** is the primary tool you use to design and build your automation workflows in `agentic-workflow`. It provides a user-friendly visual interface based on React Flow, allowing you to drag and drop [Task](02_tasks_.md) nodes (including [AI Agents](03_ai_agents_.md)) from a menu and connect them with edges to define the step-by-step logic and data flow of your automation. This visual blueprint is then saved and used to execute the workflow.

Now that you know how to build a workflow visually, the next chapter will explain what happens when you press the "Run" button: **[Workflow Execution](05_workflow_execution_.md)**.

[Workflow Execution](05_workflow_execution_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)