# Document Upload API Endpoint

## Yeni Endpoint: `/process-document-upload`

Bu endpoint, uzaktan dosya yükleme ve işleme özelliği sağlar. İstemci tarafından gönderilen dosyalar sunucuda geçici bir klasöre kayd<PERSON>r, i<PERSON><PERSON><PERSON> ve embedding'leri o<PERSON>ak belirtilen koleksiyona eklenir.

### Endpoint Detayları

- **URL**: `POST /process-document-upload`
- **Content-Type**: `multipart/form-data`
- **Tags**: `["Documents"]`

### Request Parameters

#### Form Data
- `collection_name` (string, required): Dökümanların saklanacağı koleksiyon adı
- `files` (file[], required): Yüklenecek dosyalar (çoklu dosya desteklenir)
- `recursive` (boolean, optional, default: true): Alt klasörlerin de işlenip işlenmeyeceği

### Desteklenen Dosya Formatları

- PDF (`.pdf`)
- Word D<PERSON> (`.docx`, `.doc`)
- <PERSON><PERSON> (`.txt`)
- Markdown Dosyaları (`.md`)

### Response Model: `DocumentUploadResponse`

```json
{
  "success": true,
  "message": "Successfully uploaded and processed 3 files",
  "collection_name": "my_collection",
  "uploaded_files": ["document1.pdf", "document2.docx", "document3.txt"],
  "processed_files": 3,
  "failed_files": 0,
  "temp_directory": "/tmp/document_upload_xyz123",
  "error": null,
  "details": {
    "total_files": 3,
    "processing_results": [...]
  }
}
```

### Kullanım Örnekleri

#### 1. Python ile Requests Kullanarak

```python
import requests

# Tek dosya yükleme
files = {'files': ('document.pdf', open('document.pdf', 'rb'), 'application/pdf')}
data = {'collection_name': 'my_docs', 'recursive': 'true'}

response = requests.post(
    'http://localhost:8000/process-document-upload',
    files=files,
    data=data
)

print(response.json())
```

```python
# Çoklu dosya yükleme
files = [
    ('files', ('doc1.pdf', open('doc1.pdf', 'rb'), 'application/pdf')),
    ('files', ('doc2.docx', open('doc2.docx', 'rb'), 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')),
    ('files', ('doc3.txt', open('doc3.txt', 'rb'), 'text/plain'))
]
data = {'collection_name': 'my_collection', 'recursive': 'true'}

response = requests.post(
    'http://localhost:8000/process-document-upload',
    files=files,
    data=data
)
```

#### 2. cURL ile Kullanım

```bash
# Tek dosya yükleme
curl -X POST "http://localhost:8000/process-document-upload" \
  -F "collection_name=my_collection" \
  -F "recursive=true" \
  -F "files=@/path/to/document.pdf" \
  -H "accept: application/json"
```

```bash
# Çoklu dosya yükleme
curl -X POST "http://localhost:8000/process-document-upload" \
  -F "collection_name=my_collection" \
  -F "recursive=true" \
  -F "files=@/path/to/document1.pdf" \
  -F "files=@/path/to/document2.docx" \
  -F "files=@/path/to/document3.txt" \
  -H "accept: application/json"
```

#### 3. Production Server Örneği

```bash
# AWS üzerindeki production server'a yükleme
curl -X POST "http://*************:3820/process-document-upload" \
  -F "collection_name=production_docs" \
  -F "recursive=true" \
  -F "files=@./my_document.pdf" \
  -H "accept: application/json"
```

### İşlem Akışı

1. **Dosya Yükleme**: İstemci dosyaları multipart/form-data ile gönderir
2. **Geçici Klasör Oluşturma**: Sunucuda geçici bir klasör oluşturulur
3. **Dosya Kaydetme**: Yüklenen dosyalar geçici klasöre güvenli isimlerle kaydedilir
4. **Döküman İşleme**: Mevcut `DocumentProcessor` sistemi kullanılarak dosyalar işlenir
5. **Embedding Oluşturma**: Her döküman için embedding'ler oluşturulur
6. **Veritabanına Kaydetme**: Embedding'ler belirtilen koleksiyona kaydedilir
7. **Temizlik**: Geçici klasör ve dosyalar silinir

### Güvenlik Özellikleri

- **Dosya Adı Sanitizasyonu**: Yüklenen dosya adları güvenli karakterlere dönüştürülür
- **Geçici Klasör**: Dosyalar geçici klasörde işlenir ve sonra silinir
- **Hata Yönetimi**: İşlem sırasında hata oluşursa geçici dosyalar temizlenir
- **Dosya Boyutu**: FastAPI'nin varsayılan dosya boyutu limitleri geçerlidir

### Hata Durumları

#### Başarısız Yükleme
```json
{
  "success": false,
  "message": "No files were uploaded",
  "error": "No valid files found in the upload"
}
```

#### İşleme Hatası
```json
{
  "success": false,
  "message": "File upload successful but processing failed",
  "collection_name": "my_collection",
  "uploaded_files": ["document.pdf"],
  "processed_files": 0,
  "failed_files": 1,
  "error": "Unsupported file type: .xyz"
}
```

### Test Scripti

Proje kök dizininde `test_document_upload.py` dosyası bulunmaktadır. Bu script ile endpoint'i test edebilirsiniz:

```bash
python test_document_upload.py
```

### Mevcut Endpoint'lerle Karşılaştırma

| Endpoint | Amaç | Dosya Kaynağı | Kullanım |
|----------|------|---------------|----------|
| `/process-documents` | Sunucudaki klasörü işle | Sunucu dosya sistemi | Sunucuda bulunan dosyalar |
| `/process-custom-path` | Belirtilen yolu işle | Sunucu dosya sistemi | Sunucuda bulunan dosyalar |
| `/process-document-upload` | Yüklenen dosyaları işle | İstemci yüklemesi | Uzaktan dosya yükleme |

### Avantajlar

1. **Uzaktan Erişim**: İstemci kendi dosyalarını sunucuya yükleyebilir
2. **Çoklu Dosya**: Aynı anda birden fazla dosya yüklenebilir
3. **Güvenli İşlem**: Geçici klasör kullanımı ile güvenli işleme
4. **Otomatik Temizlik**: İşlem sonrası geçici dosyalar otomatik silinir
5. **Mevcut Sistem Entegrasyonu**: Var olan `DocumentProcessor` sistemini kullanır

Bu endpoint sayesinde AWS üzerinde çalışan Ubuntu server'ınıza local dosyalarınızı uzaktan yükleyip işleyebilirsiniz.
