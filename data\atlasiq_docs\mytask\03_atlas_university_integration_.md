# Chapter 3: Atlas University Integration

Welcome back to the `mytask` journey! In [Chapter 1: User Interface Components & Pages](01_user_interface_components___pages_.md), we learned how `mytask` builds all the visual parts you see. Then, in [Chapter 2: User & Authentication System](02_user___authentication_system_.md), we explored how `mytask` identifies you and keeps your tasks secure when you log in with your regular `mytask` account.

But what if you're a student or staff member at Istanbul Atlas University? You already have a university account. It would be a hassle to create and remember *another* separate login for `mytask`. Also, wouldn't it be great if `mytask` automatically knew your department or job title from your university account? This is exactly the problem that **Atlas University Integration** solves!

### The Problem: Too Many Logins & Missing Info

Imagine our `mytask` application is still that private club. We've set up a way for regular members to enter with their club ID. But now, we're partnering with Atlas University! University members should be able to walk right in using their *university ID*. We also want to know their university role (like "Professor" or "Student") as soon as they enter, without asking them to fill out extra forms.

Without this special integration, every university member would need a brand new `mytask` account, and we'd have no easy way to get their university details. This creates extra work for users and leads to inconsistent information.

### Solution: A Special Bridge to Atlas University

`mytask` solves this by building a "special bridge" directly to Istanbul Atlas University's existing user system. This bridge allows:

1.  **Easy Login:** University members log into `mytask` using their usual Atlas University username and password.
2.  **Automatic Sync:** Once logged in, `mytask` automatically pulls in important details like their department, job title, and full name from their Atlas account.
3.  **Consistent Info:** This ensures that user information in `mytask` is always up-to-date with the university's main system.

Let's walk through how a university member logs into `mytask` using their Atlas account.

#### Central Use Case: Logging in with your Atlas University Account

Here's how it works from your perspective:

1.  **You go to the `Login` page:** Just like before, you see the login form.
2.  **You choose "Log in with Atlas University":** `mytask` offers a specific option or button for Atlas login.
3.  **You enter your Atlas credentials:** You type your Atlas University username and password into the `mytask` login form.
4.  **You click "Log In":** You click the login button.

Behind the scenes, `mytask` does a lot of work to securely talk to Atlas University and get you logged in!

### How `mytask` Uses the Atlas Bridge (Client-Side)

When you enter your Atlas details and click "Login", your `mytask` application (the one in your web browser) doesn't directly talk to the Atlas University system. Instead, it sends your credentials *securely to our `mytask` server*. Our server then handles all the complex communication with Atlas.

This is a smart design because it keeps sensitive actions hidden and secure on our server, rather than exposed in your web browser.

Here's how the `mytask` application prepares this request:

```javascript
// client\src\redux\slices\api\atlasServerApiSlice.js
import { apiSlice } from "../apiSlice";

export const atlasServerApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    processAtlasLogin: builder.mutation({ // Prepares an Atlas login request
      query: (data) => ({
        url: `/atlas-auth/process-login`, // Our server's special Atlas login path
        method: "POST",                  // Sending data securely
        body: data,                      // Your Atlas username and password
      }),
    }),
    // ... other Atlas related actions
  }),
});

export const { useProcessAtlasLoginMutation } = atlasServerApiSlice;
```
**Explanation:** This code snippet shows that `mytask` has a special function `processAtlasLogin`. When you use it, `mytask` knows to send your Atlas username and password to a specific address on *our own server*: `/atlas-auth/process-login`. It's like telling our club's bouncer, "Here's my university ID, please check it with the university directly."

### How it All Works Together (Under the Hood)

Let's see the full journey when you log in with your Atlas University account. It's a bit like a relay race!

```mermaid
sequenceDiagram
    participant You
    participant MyTaskApp as MyTask App (Browser)
    participant MyTaskServer as MyTask Server
    participant AtlasAPIServer as Atlas University System
    participant Database as MyTask Database

    You->>MyTaskApp: Enter Atlas Username/Password
    MyTaskApp->>MyTaskServer: Send Atlas Login Request
    MyTaskServer->>AtlasAPIServer: Request Login & User Data
    AtlasAPIServer-->>MyTaskServer: Send Atlas Token & User Details
    MyTaskServer->>Database: Find/Create User & Sync Details
    MyTaskServer->>MyTaskServer: Generate MyTask "ID Card" (JWT)
    MyTaskServer-->>MyTaskApp: Send MyTask "ID Card" + User Info
    MyTaskApp->>You: Display Dashboard (Logged In!)
```

**Step-by-step Explanation:**

1.  **You provide credentials:** You type your Atlas username and password into the `mytask` login page.
2.  **`mytask` app sends to its server:** Your `mytask` app (running in your browser) takes these credentials and sends them to our `mytask` server.
3.  **Our server talks to Atlas:** The `mytask` server receives your request. It then acts on your behalf, sending a login request to the official Atlas University system.
4.  **Atlas confirms and sends data:** The Atlas University system checks your credentials. If they are correct, it sends back a special "Atlas token" (like a temporary pass) and your official university details (name, department, job title) to our `mytask` server.
5.  **Our server updates its database:** Our `mytask` server now has your Atlas University details. It checks if you already have a `mytask` account linked to this Atlas ID.
    *   If you *do* have an account, it updates your `mytask` profile with any new information from Atlas (like a changed department).
    *   If you *don't* have an account, `mytask` automatically creates a brand new account for you, pre-filling your name, email, department, and job title from the Atlas data.
6.  **Our server gives you a `mytask` "ID Card":** After updating or creating your user profile, our `mytask` server generates its own "ID card" for you (a JWT token, just like in [Chapter 2: User & Authentication System](02_user___authentication_system_.md)). This ID card proves you're logged into *mytask*.
7.  **You're logged in!** The `mytask` server sends this `mytask` ID card and your user information back to your `mytask` app, which then shows you the Dashboard!

### Diving Deeper: The Server-Side Magic (Code Walkthrough)

Let's look at the pieces of code on the `mytask` server that make this integration work.

#### 1. The Atlas User Blueprint in `mytask`

First, `mytask` needs to know what kind of information it expects to get from Atlas and store for its users. In our `User` model, we have special fields for Atlas users:

```javascript
// server\models\userModel.js
import mongoose, { Schema } from "mongoose";

const userSchema = new Schema(
  {
    name: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    password: { type: String, required: false },
    isAdmin: { type: Boolean, default: false },
    // Is this user from Atlas?
    isExternal: { type: Boolean, default: true }, 
    // The unique ID from Atlas University system
    atlasUserId: { type: String, default: null, unique: true }, 
    // ... other fields like department, company, photoReference
    department: { type: String, default: null },
    company: { type: String, default: null },
    photoReference: { type: String, default: null },
    title: { type: String, default: null },
  },
  { timestamps: true }
);

const User = mongoose.model("User", userSchema);
export default User;
```
**Explanation:** We added fields like `atlasUserId`, `isExternal`, `department`, `company`, and `title` to the `User` blueprint. This tells `mytask` to expect and store these specific details when someone logs in from Atlas University. The `atlasUserId` is especially important as it's the unique identifier `mytask` uses to recognize university members.

#### 2. The Atlas Login Process on Our Server

This is the core "bridge" function. It takes your Atlas credentials, contacts the Atlas API, gets the data, and then decides whether to create a new user or update an existing one in `mytask`'s database.

```javascript
// server\controllers\atlasAuthController.js
import axios from "axios"; // Tool to make web requests
import User from "../models/userModel.js";
import createJWT from "../utils/index.js";
import { fetchAtlasUserData } from "./syncAtlasUser.js"; // Helper to get user details

const processAtlasLogin = asyncHandler(async (req, res) => {
  const { username, password } = req.body;
  
  // Step 1: Send login request to Atlas University API
  const loginResponse = await axios.post(
    ATLAS_API_ENDPOINTS.LOGIN, // Atlas University's login address
    { username, password, aplicationName: "MYATLAS" } // Your credentials
  );
  const token = loginResponse.data.webToken; // Get the special Atlas token

  // Step 2: Use the Atlas token to get detailed user data from Atlas
  const atlasUserData = await fetchAtlasUserData(token); 
  
  // Step 3: Find or Create user in our `mytask` database
  let user = await User.findOne({ atlasUserId: atlasUserData.userId }); // Try to find by Atlas ID

  if (user) {
    // If user exists, update their details from Atlas
    user.name = atlasUserData.nameSurname;
    user.department = atlasUserData.department;
    // ... update other fields
    await user.save();
  } else {
    // If user doesn't exist, create a new one in `mytask`
    user = await User.create({
      name: atlasUserData.nameSurname,
      email: atlasUserData.email,
      atlasUserId: atlasUserData.userId,
      department: atlasUserData.department,
      // ... set other fields
      isExternal: true // Mark as an external (Atlas) user
    });
  }

  // Step 4: Generate `mytask`'s own "ID card" (JWT token)
  createJWT(res, user._id); 
  
  // Send back the `mytask` user info to the browser
  res.status(200).json({ status: true, user: user.toObject() });
});

export { processAtlasLogin };
```
**Explanation:** This simplified function `processAtlasLogin` first sends your Atlas username and password to the real Atlas University system using `axios` (a tool for making web requests). If Atlas says "yes, this is a valid user!", it gives us an Atlas `token`. We then use this token to ask Atlas for more detailed user information (like department, job title) using `fetchAtlasUserData`. Finally, we check our `mytask` database: if a user with that `atlasUserId` already exists, we update their information. If not, we create a new `mytask` user profile using the data from Atlas and mark them as `isExternal: true`. We then give them a `mytask` JWT token so they can access their tasks.

#### 3. Getting Detailed Atlas User Data (Helper Function)

This is a separate function used by `processAtlasLogin` to get more detailed user information from Atlas after a successful initial login.

```javascript
// server\controllers\syncAtlasUser.js
import axios from "axios";
import { ATLAS_API_ENDPOINTS } from "../config/apiConfig.js";

const fetchAtlasUserData = async (token) => {
  // Ensure the token is in the correct "Bearer" format for Atlas API
  let formattedToken = token;
  if (!formattedToken.startsWith('Bearer ')) {
    formattedToken = `Bearer ${formattedToken}`;
  }
  
  // Make a GET request to Atlas University's data endpoint
  const response = await axios.get(
    ATLAS_API_ENDPOINTS.LOGIN_DATA, // Atlas University's data address
    { headers: { 'Authorization': formattedToken } } // Send the Atlas token
  );
  
  return response.data; // Return the user data received from Atlas
};

export { fetchAtlasUserData };
```
**Explanation:** This `fetchAtlasUserData` function is like a special messenger. After our server gets the initial "Atlas token" (the temporary pass), this messenger uses that pass to go back to the Atlas University system and ask for *more* details about the user, such as their full name, email, department, and job title. It's crucial for getting all the information we need to populate the `mytask` user profile.

#### 4. Mapping URLs to Functions (Routes)

Finally, our `mytask` server needs to know which incoming requests should be handled by which function.

```javascript
// server\routes\atlasAuthRoutes.js
import express from 'express';
import { processAtlasLogin } from '../controllers/atlasAuthController.js';

const router = express.Router();

// When a POST request comes to /atlas-auth/process-login, call processAtlasLogin
router.post('/process-login', processAtlasLogin);

export default router;
```
**Explanation:** This snippet tells the `mytask` server: "If someone sends a POST request to `/atlas-auth/process-login`, use the `processAtlasLogin` function (the bridge function we just discussed) to handle it." This is how the `mytask` application knows where to send your Atlas login attempt.

### Conclusion

In this chapter, we explored the "Atlas University Integration" in `mytask`. We learned how this specialized bridge allows students and staff to log into `mytask` using their familiar Atlas University accounts, automatically syncing their details like department and job title. This simplifies the login process and keeps user information consistent.

You've now learned how `mytask` manages its visual appearance and securely handles different types of user logins. What's next? Well, a task manager isn't much use without tasks! In the next chapter, we'll dive into the core of the application: [Task Management System](04_task_management_system_.md).

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)