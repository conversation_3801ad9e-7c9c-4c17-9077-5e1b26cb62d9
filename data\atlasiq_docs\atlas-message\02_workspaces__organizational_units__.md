# Chapter 2: Workspaces (Organizational Units)

Welcome back! In [Chapter 1: User & Authentication System](01_user___authentication_system_.md), we learned how `atlas-message` knows *who* you are by managing your user account and ensuring only authenticated users can access the application. Think of it like getting your ID checked at the entrance of a big building.

Now, imagine that building isn't just one giant open room where everyone shouts their messages. That would be chaotic, right? Different teams, projects, or even whole companies need their own dedicated spaces to communicate without getting mixed up. This is where **Workspaces** come in!

Workspaces are the primary way `atlas-message` helps you organize your conversations. They are like **separate digital offices or project rooms** within the application. Each workspace is a self-contained environment where a specific group of people can collaborate.

## What are Workspaces?

At its core, a workspace in `atlas-message` is:

*   **A Container:** It holds its own members, its own unique settings, and specific communication channels (which we'll explore in the next chapter!).
*   **Collaborative Spaces:** You can be a part of multiple workspaces. For example, you might have one for your main company team, another for a side project, and a third for a community group.
*   **Role-Based:** Each member in a workspace has a specific role (like 'owner', 'admin', or 'member'), which determines what they are allowed to do within that workspace.

Let's imagine you've just signed up for `atlas-message` and want to create a space for your "Marketing Team" project. This is a perfect use case for a workspace!

## Creating Your First Workspace

When you log into `atlas-message` for the first time, you might see a page where you can create or join a workspace. Let's focus on creating one.

Here's a simplified look at the screen where you'd create a new workspace:

```tsx
// app/workspace/create/page.tsx (simplified)
'use client'
import { useState } from 'react'
import { useSession } from 'next-auth/react' // To get current user info
import { useRouter } from 'next/navigation'

export default function CreateWorkspacePage() {
  const { data: session } = useSession() // Get current logged-in user's session
  const router = useRouter()
  const [workspaceName, setWorkspaceName] = useState('')
  const [isCreating, setIsCreating] = useState(false)
  const [error, setError] = useState('')

  const handleCreateWorkspace = async () => {
    if (!workspaceName.trim()) return // Don't allow empty names

    setIsCreating(true)
    setError('')

    try {
      // 1. Send a request to our server to create the workspace
      const response = await fetch('/api/workspaces', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: workspaceName.trim() }),
      })

      const data = await response.json()

      if (response.ok) {
        // 2. If successful, go to the new workspace's page
        router.push(`/workspace/${data._id}`)
      } else {
        // 3. Show any error messages
        setError(data.error || 'Failed to create workspace')
      }
    } catch (error) {
      setError('Network error. Please try again.')
    } finally {
      setIsCreating(false)
    }
  }

  if (!session) return null // Wait for session or redirect

  return (
    <div>
      <h1>Create Your Workspace</h1>
      <input
        type="text"
        value={workspaceName}
        onChange={(e) => setWorkspaceName(e.target.value)}
        placeholder="Enter workspace name..."
        disabled={isCreating}
      />
      <button onClick={handleCreateWorkspace} disabled={isCreating}>
        {isCreating ? 'Creating...' : 'Create Workspace'}
      </button>
      {error && <p style={{ color: 'red' }}>{error}</p>}
    </div>
  )
  // ... more UI elements
}
```

In this simplified `CreateWorkspacePage`, you type a name for your workspace (e.g., "Marketing Team"), and when you click "Create Workspace," the code sends that name to our server. If everything goes well, the application will then take you directly into your newly created "Marketing Team" workspace.

## How Workspace Creation Works: Under the Hood

When you click that "Create Workspace" button, a lot happens behind the scenes to make sure your new digital office is set up correctly.

```mermaid
sequenceDiagram
    participant User
    participant Frontend as Your Browser (Next.js App)
    participant BackendAPI as Our Server (Next.js API Routes)
    participant Database as MongoDB

    User->>Frontend: Enters Workspace Name, Clicks Create
    Frontend->>BackendAPI: Sends POST request to /api/workspaces with {name: "Marketing Team"}
    BackendAPI->>BackendAPI: 1. Verify User's Identity & Permissions (from session)
    BackendAPI->>BackendAPI: 2. Validate Workspace Name (e.g., length, characters)
    BackendAPI->>Database: 3. Check User Creation Limits (e.g., max 5 workspaces per user)
    Database-->>BackendAPI: User limits confirmed
    BackendAPI->>Database: 4. Create new Workspace record
    Database-->>BackendAPI: Workspace ID returned
    BackendAPI->>Database: 5. Create a default "general" Channel linked to Workspace
    Database-->>BackendAPI: Channel ID returned
    BackendAPI->>Database: 6. Update User record to link to the new Workspace
    Database-->>BackendAPI: User updated
    BackendAPI-->>Frontend: Success Response (with new workspace details)
    Frontend->>User: Redirects to new Workspace page
```

Let's dive into the server-side code that makes this happen, specifically in `app/api/workspaces/route.ts`. This file handles requests related to workspaces.

### The Workspace Blueprint: `models/Workspace.ts`

First, let's look at how `atlas-message` structures the data for a workspace. Just like your `User` account has a blueprint, so does a `Workspace`.

```typescript
// models/Workspace.ts (simplified)
import mongoose, { Document, Schema } from 'mongoose'

export interface IWorkspace extends Document {
  _id: string;             // Unique ID for the workspace
  name: string;            // E.g., "Marketing Team"
  description?: string;    // Optional, like "Our amazing marketing efforts"
  owner: mongoose.Types.ObjectId; // The user who created it (their ID)
  members: {               // List of users in this workspace
    user: mongoose.Types.ObjectId;
    role: 'owner' | 'admin' | 'member' | 'guest'; // What they can do
    joinedAt: Date;
  }[];
  channels: mongoose.Types.ObjectId[]; // List of channels within this workspace (Chapter 3!)
  // ... other settings like invite codes, public status, etc.
  createdAt: Date;
  updatedAt: Date;
}

const WorkspaceSchema = new Schema<IWorkspace>(
  {
    name: { type: String, required: true, maxlength: 50 },
    description: { type: String, maxlength: 200 },
    owner: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    members: [{
      user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
      role: { type: String, enum: ['owner', 'admin', 'member', 'guest'], default: 'member' },
      joinedAt: { type: Date, default: Date.now },
    }],
    channels: [{ type: Schema.Types.ObjectId, ref: 'Channel' }],
    // ... other schema fields
  },
  { timestamps: true }
)

export const Workspace = mongoose.models.Workspace || mongoose.model<IWorkspace>('Workspace', WorkspaceSchema)
```

This `models/Workspace.ts` file defines what information each workspace will store in our database. It includes its `name`, a `description`, who the `owner` is, a list of `members` (including their role), and a list of `channels` that belong to it.

### The Server-Side Logic for Creating a Workspace (`POST /api/workspaces`)

Now let's look at the actual code that runs on the server when `atlas-message` receives a request to create a new workspace.

```typescript
// app/api/workspaces/route.ts (simplified POST)
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { connectToDatabase } from '@/lib/mongodb'
import { Workspace } from '@/models/Workspace'
import { User } from '@/models/User'
import { Channel } from '@/models/Channel' // We'll talk about this in Chapter 3!
import { isAdmin, WORKSPACE_CONFIG } from '@/lib/workspace-config' // Configuration for limits

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions) // Get info about the logged-in user
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const { name, description } = await request.json()
  if (!name?.trim()) { // Check if name is provided
    return NextResponse.json({ error: 'Name is required' }, { status: 400 })
  }

  await connectToDatabase() // Connect to our database

  // 1. Basic validation (e.g., name length, allowed characters)
  // (Simplified for brevity, actual code has more checks based on WORKSPACE_CONFIG)
  if (name.trim().length < 2 || name.trim().length > 50) {
    return NextResponse.json({ error: 'Name must be between 2 and 50 characters.' }, { status: 400 })
  }

  // 2. Check user creation limits (e.g., how many workspaces a user can own)
  // (Simplified: In actual code, checks `WORKSPACE_CONFIG.ADMIN_ONLY_WORKSPACE_CREATION`
  // and `MAX_WORKSPACES_PER_USER` if not admin)
  const userIsAdmin = isAdmin(session.user.email || '')
  const userWorkspaceCount = await Workspace.countDocuments({
    'members.user': session.user.id,
    'members.role': 'owner'
  })
  if (!userIsAdmin && userWorkspaceCount >= WORKSPACE_CONFIG.MAX_WORKSPACES_PER_USER) {
    return NextResponse.json({ error: 'You have reached your workspace creation limit.' }, { status: 429 })
  }

  // 3. Create the new workspace in the database
  const workspace = await Workspace.create({
    name: name.trim(),
    description: description?.trim() || '',
    owner: session.user.id, // The logged-in user is the owner
    members: [{
      user: session.user.id,
      role: 'owner', // Assign owner role to the creator
      joinedAt: new Date(),
    }],
    inviteCode: Math.random().toString(36).substring(2, 10).toUpperCase(), // Generate an invite code
    isPublic: false,
    settings: WORKSPACE_CONFIG.DEFAULT_SETTINGS,
  })

  // 4. Create a default 'general' channel for the new workspace
  const generalChannel = await Channel.create({
    name: 'general',
    description: 'General discussion for the workspace',
    type: 'public',
    workspace: workspace._id, // Link to the new workspace
    members: [session.user.id], // Owner is automatically a member
    creator: session.user.id,
  })

  // 5. Add the new channel's ID to the workspace's channels list
  workspace.channels.push(generalChannel._id)
  await workspace.save()

  // 6. Add the new workspace's ID to the user's list of workspaces
  await User.findByIdAndUpdate(session.user.id, {
    $push: { workspaces: workspace._id }
  })

  return NextResponse.json(workspace, { status: 201 }) // Send back the new workspace details
}
```

This simplified code from `app\api\workspaces\route.ts` outlines the server's actions:

1.  **Authorization:** It first checks if the user is logged in using their `session` (just like we saw in [Chapter 1: User & Authentication System](01_user___authentication_system_.md)).
2.  **Input & Validation:** It receives the workspace `name` and `description` and performs basic checks to ensure they are valid (e.g., not too short or too long).
3.  **Limits Check:** It verifies if the user is allowed to create another workspace based on predefined rules (e.g., maximum number of workspaces per user, or if only admins can create them). This prevents abuse.
4.  **Create Workspace:** If all checks pass, it creates a new `Workspace` record in the database. The logged-in user is automatically set as the `owner` and added to the `members` list.
5.  **Create Default Channel:** It immediately creates a default "general" [Channel](03_channels__communication_hubs_.md) for this new workspace, so there's a place to start chatting right away.
6.  **Update Relationships:** It links the new channel back to the workspace and adds the workspace to the user's list of owned/joined workspaces.

Finally, it sends the details of the newly created workspace back to your browser, which then redirects you into that workspace.

## Viewing Your Workspaces

Once you've created a workspace (or joined others), you'll want to see them all. `atlas-message` typically has a dashboard or a sidebar where you can switch between your different digital offices.

Here's how `atlas-message` fetches and displays the workspaces you belong to:

```tsx
// components/dashboard/workspace-dashboard.tsx (simplified)
'use client'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

interface Workspace {
  _id: string
  name: string
  description?: string
  userRole: string // Role of the current user in this workspace
  memberCount: number
}

export default function WorkspaceDashboard({ user }: { user: any }) {
  const [workspaces, setWorkspaces] = useState<Workspace[]>([])
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const fetchWorkspaces = async () => {
      try {
        setLoading(true)
        // Send a GET request to our API to fetch all workspaces the user is a member of
        const response = await fetch('/api/workspaces')
        
        if (response.ok) {
          const userWorkspaces = await response.json()
          setWorkspaces(userWorkspaces) // Store the fetched workspaces
        }
      } catch (error) {
        console.error('Error fetching workspaces:', error)
      } finally {
        setLoading(false)
      }
    }
    fetchWorkspaces()
  }, []) // Empty array means this runs once when the component loads

  const handleWorkspaceClick = (workspaceId: string) => {
    // When a workspace is clicked, navigate to its page
    router.push(`/workspace/${workspaceId}`)
  }

  if (loading) return <div>Loading your workspaces...</div>
  if (workspaces.length === 0) return <div>No workspaces yet. Create one!</div>

  return (
    <div>
      <h2>Your Workspaces</h2>
      {workspaces.map((workspace) => (
        <button key={workspace._id} onClick={() => handleWorkspaceClick(workspace._id)}>
          <h3>{workspace.name} ({workspace.userRole})</h3>
          <p>{workspace.memberCount} members</p>
          {workspace.description && <p>{workspace.description}</p>}
        </button>
      ))}
    </div>
  )
}
```

This `WorkspaceDashboard` component makes a request to `/api/workspaces` to get a list of all workspaces the currently logged-in user is part of. It then displays them, allowing you to click on one to enter it.

### The Server-Side Logic for Fetching Workspaces (`GET /api/workspaces`)

The `GET` method in `app/api/workspaces/route.ts` is responsible for sending you your list of workspaces:

```typescript
// app/api/workspaces/route.ts (simplified GET)
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { connectToDatabase } from '@/lib/mongodb'
import { Workspace } from '@/models/Workspace'

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  await connectToDatabase()

  // Find all workspaces where the current user is a member
  const workspaces = await Workspace.find({
    'members.user': session.user.id // Find workspaces where the user's ID is in the 'members' array
  }).select('_id name description owner members createdAt') // Select only necessary fields

  // Process the results to add the user's role and member count
  const workspacesWithRole = workspaces.map(workspace => {
    const memberInfo = workspace.members.find(
      (member: any) => member.user.toString() === session.user.id
    )
    
    return {
      _id: workspace._id,
      name: workspace.name,
      description: workspace.description,
      // ... other fields
      userRole: memberInfo?.role || 'member', // Determine current user's role in this workspace
      isOwner: workspace.owner.toString() === session.user.id,
      memberCount: workspace.members.length // Total members in this workspace
    }
  })

  // Sort workspaces (e.g., owner workspaces first)
  workspacesWithRole.sort((a, b) => {
    const roleOrder: { [key: string]: number } = { owner: 0, admin: 1, member: 2 }
    return (roleOrder[a.userRole] || 3) - (roleOrder[b.userRole] || 3)
  })

  return NextResponse.json(workspacesWithRole) // Send back the list of workspaces
}
```

This `GET` function handles:

1.  **Authorization:** Again, it verifies the user's identity.
2.  **Database Query:** It looks into the `Workspace` collection in our database and finds every workspace where the current user's ID is listed in the `members` array.
3.  **Data Enrichment:** For each workspace found, it figures out what the *current user's* role is within *that specific workspace* (owner, admin, member) and counts how many total members are in that workspace.
4.  **Response:** It sends this prepared list of workspaces back to your browser.

## Conclusion

Workspaces are crucial for keeping communication organized in `atlas-message`. They provide dedicated spaces for teams and projects, allowing focused collaboration. You've seen how `atlas-message` handles the creation of these "digital offices," including managing who owns them, who belongs to them, and how their data is stored. You've also seen how the application fetches and displays all the workspaces you're a part of.

Now that we understand how users organize themselves into workspaces, the next logical step is to explore how communication happens *within* these workspaces. Get ready to dive into **Channels**!

[Next Chapter: Channels (Communication Hubs)](03_channels__communication_hubs_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)