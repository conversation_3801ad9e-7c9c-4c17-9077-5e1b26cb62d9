pipeline {
    agent any
    environment {
        // --- AWS & ECR Configuration ---
        AWS_ACCOUNT_ID = "************"
        AWS_DEFAULT_REGION = "eu-central-1"

        // --- Image Configuration ---
        IMAGE_REPO_NAME = "hipo-test" // Ortak ECR deposu
        APP_NAME = "atlas-q-a-rag" // İmaj etiketlemesi için uygulama adı
        DOCKERFILE_PATH = "docker/Dockerfile.backend-only"

        // --- Deployment Configuration ---
        // Bu değerleri kendi sunucu bilgilerinize göre güncelleyin
        DEPLOY_SERVER = "*************" // Sadece IP adresi veya FQDN. Kullanıcı adı Jenkins kimlik bilgisinden alınacak.
        DEPLOY_PATH = "/opt/atlas-rag" // Sunucudaki proje yolu
        SSH_CREDENTIALS_ID = "*************" // <PERSON>'teki "Username with password" credential ID'si
    }

    stages {

        stage('Checkout Code') {
            steps {
                echo "Cloning the atlas-q-a-rag repository..."
                checkout([
                    $class: 'GitSCM',
                    branches: [[name: '*/main']],
                    userRemoteConfigs: [[
                        credentialsId: 'tradeport-bitbucket-jenkins',
                        url: 'https://<EMAIL>/netaxtech-team/atlas-q-a-rag.git'
                    ]]
                ])
            }
        }

        stage('Login to AWS ECR') {
            steps {
                echo "Logging into AWS ECR..."
                sh "aws ecr get-login-password --region ${AWS_DEFAULT_REGION} | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_DEFAULT_REGION}.amazonaws.com"
            }
        }

        stage('Build & Push to ECR') {
            steps {
                script {
                    echo "Building and pushing Docker image..."
                    def commitHash = sh(script: 'git rev-parse --short HEAD', returnStdout: true).trim()
                    def imageTag = "${APP_NAME}-${commitHash}"
                    env.IMAGE_URI = "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_DEFAULT_REGION}.amazonaws.com/${IMAGE_REPO_NAME}:${imageTag}"

                    docker.withRegistry("https://${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_DEFAULT_REGION}.amazonaws.com") {
                        def customImage = docker.build(env.IMAGE_URI, "-f ${DOCKERFILE_PATH} .")
                        customImage.push()
                    }
                }
            }
        }
        stage('Deploy to Production') {
            environment {
                OPENAI_API_KEY_SECRET = credentials('OPENAI_API_KEY')
                TAVILY_API_KEY_SECRET = credentials('TAVILY_API_KEY')
                LANGCHAIN_API_KEY_SECRET = credentials('LANGCHAIN_API_KEY')
                // SSH_CREDENTIALS_ID'deki kimlik bilgisini (kullanıcı adı ve şifre) yükle
                SERVER_CREDENTIALS = credentials('*************')
            }
            steps {
                echo "Deploying to production server with password..."
                script {
                    // withEnv bloğu, şifrenin sadece bu blok içinde geçerli olmasını sağlar
                    withEnv(["SSHPASS=${SERVER_CREDENTIALS_PSW}"]) {
                        // Güvenlik seçeneklerini ve uzak sunucu adresini tanımla
                        def ssh_opts = "-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"
                        def remote_address = "${SERVER_CREDENTIALS_USR}@${DEPLOY_SERVER}"
                        
                        // sshpass komutlarını tanımla
                        def sshpass_ssh = "sshpass -e ssh ${ssh_opts}"
                        def sshpass_scp = "sshpass -e scp ${ssh_opts}"

                        // 1. Sunucuda gerekli klasörlerin var olduğundan emin ol
                        sh "${sshpass_ssh} ${remote_address} 'mkdir -p ${DEPLOY_PATH}/data ${DEPLOY_PATH}/logs'"

                        // 2. Gerekli dosyaları sunucuya kopyala
                        sh "${sshpass_scp} docker-compose.deploy.yml ${remote_address}:${DEPLOY_PATH}/"

                        // 3. Sunucudaki .env dosyasını oluştur/güncelle
                        sh """
                        ${sshpass_ssh} ${remote_address} "cat > ${DEPLOY_PATH}/.env <<EOF
OPENAI_API_KEY=${OPENAI_API_KEY_SECRET}
TAVILY_API_KEY=${TAVILY_API_KEY_SECRET}
LANGCHAIN_API_KEY=${LANGCHAIN_API_KEY_SECRET}
IMAGE_URI=${env.IMAGE_URI}
EOF"
                        """

                        // 3.5. Deploy sunucusunda AWS ECR'ye tekrar login ol
                        sh """
                        ${sshpass_ssh} ${remote_address} "aws ecr get-login-password --region ${AWS_DEFAULT_REGION} | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_DEFAULT_REGION}.amazonaws.com"
                        """

                        // 4. Yeni imajı çek, servisleri yeniden başlat ve eski imajları temizle
                        sh """
                        ${sshpass_ssh} ${remote_address} "cd ${DEPLOY_PATH} && \
                            docker compose -f docker-compose.deploy.yml pull && \
                            docker compose -f docker-compose.deploy.yml up -d --remove-orphans && \
                            docker image prune -af"
                        """
                    }
                }
            }
        }
    }

    post {
        always {
            echo 'Pipeline finished.'
            // Jenkins agent üzerinde yer kaplamaması için build sonrası imajı temizle
            script {
                if (env.IMAGE_URI) {
                    sh "docker rmi ${env.IMAGE_URI} || true"
                }
            }
        }
    }
}
