# Chapter 4: Credit & Billing System

Welcome back! In [Chapter 3: AI Content Generation](03_ai_content_generation_.md), we explored how our `ai-course` platform uses powerful AI models to generate detailed course content, quizzes, and even images. This is fantastic for creating courses quickly! However, these amazing AI capabilities aren't free. Using advanced AI models consumes resources, and these resources come with a cost.

This is where the **Credit & Billing System** comes in. Imagine our `ai-course` platform is like a super-smart vending machine. You need to put in "tokens" (which we call **credits**) to get the AI to generate content for you. This chapter is all about how we manage these credits: how users get them (by paying for them or being on a plan), how they spend them, and how we keep track of everything. It's the financial engine behind our AI features.

### The Big Problem: Managing AI Costs & User Payments

How do we let users use powerful AI tools while also covering the costs of running those AIs? How do we allow users to pay for more access, track their usage, and manage different subscription levels? This is the central problem the "Credit & Billing System" solves.

Let's focus on two core use cases:
1.  **A user consumes credits**: They click "Generate Content" for a chapter, and the system needs to deduct the cost.
2.  **A user purchases more credits/upgrades their plan**: They want to buy more AI generation power.

Our goal for this chapter is to understand how the system securely handles these operations, ensuring users have enough credits for their AI tasks and can easily purchase more when needed.

### Key Concepts: Your Financial Tools

To understand this system, let's break down its main components:

1.  **Credits (Our Digital Currency)**: These are like tokens. Each time you use a premium AI feature (like generating a chapter's full content), a certain number of credits are deducted from your account. Free users get a small amount, while paid plans offer more.
    *   **Analogy**: Coins you put into the vending machine to get your favorite snack.

2.  **Subscription Plans (Credit Bundles)**: We offer different plans (e.g., Free, Standard, Enterprise). Each plan comes with a specific amount of credits or even unlimited access. Upgrading means getting more credits!
    *   **Analogy**: Buying a larger bag of coins at a discount for the vending machine, or even getting a special "unlimited snacks" pass.

3.  **Stripe (Our Payment Processor)**: Stripe is a widely used online payment service. Our platform integrates with Stripe to securely handle all payments, subscriptions, and billing information without ever touching your credit card details directly.
    *   **Analogy**: The secure bank that handles all the money transactions for the vending machine company.

4.  **Credit Tracking (The Ledger)**: Our database keeps a detailed record of how many credits each user has, how many they've used, and what plan they are on. It also logs every time credits are used or added.
    *   **Analogy**: A meticulously kept notebook (or a digital spreadsheet) that tracks every coin put in and every snack taken out.

### Solving the Use Case: Consuming and Purchasing Credits

Let's see how these concepts work in action for our two use cases.

#### Use Case 1: Consuming Credits (Frontend Perspective)

Recall from [Chapter 3: AI Content Generation](03_ai_content_generation_.md) that when you click "Generate Content," the frontend sends requests to the AI backend. Before or after the AI generates content, our system *must* check and deduct credits.

The `app\create-course\page.jsx` (for course outline generation) or `app\course\[courseId]\start\page.jsx` (for chapter content generation) will make a call to a special API endpoint to handle credit deduction.

```javascript
// From: app\create-course\page.jsx (simplified snippet)
// This is called AFTER AI has successfully generated content
// For [Credit & Billing System](04_credit___billing_system_.md): A credit is used here.
const useCredit = async (courseId, chapterId = null, action = 'course_creation') => {
    try {
        const response = await fetch('/api/credits', { // <-- IMPORTANT: Calling the Credits API!
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                amount: 1, // Example: 1 credit for course creation, or 5 for chapter content
                action: action,
                courseId: courseId,
                chapterId: chapterId
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to use credits');
        }
        console.log('Credits deducted successfully!');
        // Optionally, update UI to reflect new credit balance
    } catch (error) {
        console.error('Error deducting credits:', error.message);
        // Inform user about credit issue
        alert('Not enough credits or error deducting credits. Please upgrade your plan.');
    }
};

// ... inside GenerateCourseLayout or handleGenerateContent function ...
// After AI response is received and content is parsed/saved:
// await SaveCourseLayoutInDb(parsedResponse); // Saves the course and chapters
// For [Credit & Billing System](04_credit___billing_system_.md): 
await useCredit(newCourseId, null, 'course_creation'); // Deduct credits for course creation
```

**Explanation**: After the AI successfully generates the course outline or chapter content, the `useCredit` function is called. This function sends a `POST` request to our `/api/credits` endpoint. It tells the server *how many* credits to deduct (`amount`), *what* action caused the deduction (`action`), and *which* course/chapter it relates to. The server then processes this request, checking if you have enough credits and updating your balance.

**Input**: The amount of credits to deduct, the action, and related course/chapter IDs.
**Output**: Credits are deducted, and the backend confirms success or sends an error if credits are insufficient.

#### Use Case 2: Purchasing More Credits / Upgrading Plan (Frontend Perspective)

When a user wants more credits or to upgrade their plan, they visit the "Upgrade" page (`app\dashboard\upgrade\page.jsx`). This page typically displays different subscription plans with their benefits and prices.

```javascript
// From: app\dashboard\_components\ProductsPage.jsx (simplified)
import { Button } from '@/components/ui/button';

function ProductsPage() {
  const plans = [
    { name: "Free", credits: 5, price: "Free", description: "Basic access" },
    { name: "Standard", credits: 50, price: "$9.99/month", priceId: "price_123abc", billingPeriod: "monthly" },
    // ... other plans
  ];

  const handleSubscribe = async (priceId, planName, billingPeriod) => {
    try {
      const response = await fetch('/api/create-checkout-session', { // <-- Calling Stripe checkout API!
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ priceId, planName, billingPeriod })
      });
      const data = await response.json();
      if (data.url) {
        window.location.href = data.url; // Redirect to Stripe's secure checkout page
      } else {
        alert('Error creating checkout session: ' + data.error);
      }
    } catch (error) {
      console.error('Frontend error during subscription:', error);
      alert('An unexpected error occurred. Please try again.');
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {plans.map((plan) => (
        <div key={plan.name} className="border p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold">{plan.name}</h2>
          <p className="text-lg font-bold mt-2">{plan.price}</p>
          <p className="text-sm text-gray-500">{plan.credits} Credits</p>
          <Button 
            className="mt-4"
            onClick={() => handleSubscribe(plan.priceId, plan.name, plan.billingPeriod)}
            disabled={plan.price === 'Free'}
          >
            {plan.price === 'Free' ? 'Current Plan' : 'Choose Plan'}
          </Button>
        </div>
      ))}
    </div>
  );
}
```

**Explanation**: When you click the "Choose Plan" button for a paid plan, the `handleSubscribe` function is called. This function sends a `POST` request to our `/api/create-checkout-session` endpoint, providing the chosen plan's `priceId` (a unique ID from Stripe), `planName`, and `billingPeriod`. Our backend then talks to Stripe, which generates a secure checkout URL. The frontend then redirects your browser to this Stripe URL, where you can safely enter your payment information.

**Input**: Chosen plan's ID, name, and billing period.
**Output**: A redirect to Stripe's secure checkout page. After payment, Stripe redirects back to our `payment-success` page.

### Under the Hood: How Credit & Billing Works (Internal Implementation)

Let's peek behind the curtain to see how our backend manages credits and integrates with Stripe.

#### High-Level Walkthrough: Credit Management & Payments

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant BackendAPI as Server (Our APIs)
    participant Database
    participant StripeAPI as Stripe (Payment Processor)

    User->>Frontend: Clicks "Generate Content"
    Frontend->>BackendAPI: Request to use credits (POST /api/credits)
    BackendAPI->>Database: Check user's total & used credits
    Database-->>BackendAPI: Current credit balance
    alt Sufficient Credits
        BackendAPI->>Database: Deduct credits & Record usage (CreditUsage table)
        Database-->>BackendAPI: Credits updated
        BackendAPI-->>Frontend: Success (Continue AI generation)
    else Insufficient Credits
        BackendAPI-->>Frontend: Error (Prompt to upgrade)
    end

    User->>Frontend: Clicks "Upgrade Plan"
    Frontend->>BackendAPI: Request Stripe checkout session (POST /api/create-checkout-session)
    BackendAPI->>StripeAPI: Create Checkout Session with plan details
    StripeAPI-->>BackendAPI: Session URL
    BackendAPI-->>Frontend: Redirect URL
    Frontend->>User: Redirects to Stripe for payment

    User->>StripeAPI: Completes payment
    StripeAPI->>BackendAPI: Webhook: Payment Success! (POST /api/webhooks/stripe)
    BackendAPI->>Database: Update User's plan & total credits (Users & UserCredits tables)
    Database-->>BackendAPI: Plan/credits updated
    BackendAPI-->>StripeAPI: Acknowledge webhook
    StripeAPI-->>User: Redirects back to our site (Success Page)
```

**Explanation**:
1.  **Credit Consumption**: When you use an AI feature, the **Frontend** asks our **Backend API** to deduct credits. The **Backend API** checks your balance in the **Database**. If you have enough, it updates your `used_credits` and logs the activity. Otherwise, it tells the **Frontend** you need more.
2.  **Credit Purchase**: When you choose a plan, the **Frontend** asks our **Backend API** to start a payment process. The **Backend API** talks to **StripeAPI** to create a secure checkout session.
3.  **Payment & Webhook**: You complete the payment on **Stripe's** site. After a successful payment, **Stripe** sends a special "webhook" message back to our **Backend API**. This webhook contains information about the successful payment. Our **Backend API** then updates your user record and credit balance in our **Database** to reflect your new plan and credits. Finally, you are redirected back to our site's success page.

#### Code Deep Dive: The Server's Financial Brain

Our project uses several backend API routes to manage credits and integrate with Stripe.

**1. Managing User Credits (`app\api\credits\route.js` - GET & POST)**

This single API route handles both fetching (GET) and consuming (POST) user credits.

**Fetching Credits (GET)**: When the dashboard or a credit display component loads, it asks this endpoint for the user's current credit balance.

```javascript
// From: app\api\credits\route.js (simplified GET handler)
import { db } from "@/lib/db";
import { sql } from "drizzle-orm"; // For raw SQL queries
import jwt from 'jsonwebtoken'; // To verify user's identity

export const runtime = 'nodejs'; // IMPORTANT: JWT requires Node.js runtime

// Helper function to get user's email securely (covered in Chapter 1)
function getEmailFromRequest(request) { /* ... implementation ... */ }

export async function GET(request) {
    try {
        const email = getEmailFromRequest(request);
        if (!email) {
            return NextResponse.json({ error: "Email not found" }, { status: 400 });
        }
        
        // Query the 'user_credits' table for the user's balance
        const userCreditsResult = await db.execute(
            sql`SELECT total_credits as "totalCredits", used_credits as "usedCredits", plan_type as "planType" 
                FROM user_credits 
                WHERE email = ${email}`
        );

        const userCredits = userCreditsResult.rows?.[0] || userCreditsResult?.[0]; // Get the first row
        
        // If no record exists, create a default 'free' plan entry (e.g., 5 credits)
        if (!userCredits) {
            const newCreditsResult = await db.execute(
                sql`INSERT INTO user_credits (email, total_credits, used_credits, plan_type) 
                    VALUES (${email}, 5, 0, 'free') 
                    RETURNING total_credits as "totalCredits", used_credits as "usedCredits", plan_type as "planType"`
            );
            return NextResponse.json({ success: true, credits: newCreditsResult.rows?.[0] || newCreditsResult[0] });
        }
        
        return NextResponse.json({ success: true, credits: userCredits });
    } catch (error) {
        console.error('Credits GET API error:', error);
        return NextResponse.json({ error: "Database error", details: error.message }, { status: 500 });
    }
}
```

**Explanation**: This `GET` endpoint first gets the authenticated user's email. Then, it queries the `user_credits` table in the database to fetch their `total_credits`, `used_credits`, and `plan_type`. If no record is found for the user (e.g., they just registered), it automatically creates a new entry, typically assigning them a default "free" plan with a small number of credits (e.g., 5).

**Consuming Credits (POST)**: This is the endpoint called by the frontend to deduct credits.

```javascript
// From: app\api\credits\route.js (simplified POST handler)
// ... (imports and getEmailFromRequest helper as above) ...

export async function POST(request) {
    try {
        const email = getEmailFromRequest(request);
        if (!email) {
            return NextResponse.json({ error: "Email not found" }, { status: 400 });
        }

        const { amount, action, courseId, chapterId } = await request.json(); // Get deduction details

        // 1. Get current credits
        const userCreditsResult = await db.execute(
            sql`SELECT total_credits as "totalCredits", used_credits as "usedCredits" 
                FROM user_credits 
                WHERE email = ${email}`
        );
        const currentCredits = userCreditsResult.rows?.[0] || userCreditsResult?.[0];

        if (!currentCredits) {
            return NextResponse.json({ error: "User has no credit record" }, { status: 404 });
        }

        // 2. Check if user has enough credits
        if (currentCredits.totalCredits - currentCredits.usedCredits < amount) {
            return NextResponse.json({ error: "Insufficient credits" }, { status: 400 });
        }

        // 3. Update used credits
        const newUsedCredits = currentCredits.usedCredits + amount;
        await db.execute(
            sql`UPDATE user_credits 
                SET used_credits = ${newUsedCredits}, last_updated = CURRENT_TIMESTAMP 
                WHERE email = ${email}`
        );

        // 4. Record credit usage (for audit trail)
        await db.execute(
            sql`INSERT INTO credit_usage (email, amount, action, course_id, chapter_id) 
                VALUES (${email}, ${amount}, ${action}, ${courseId || null}, ${chapterId || null})`
        );
        
        // 5. Return updated credits
        const updatedCreditsResult = await db.execute(
            sql`SELECT total_credits as "totalCredits", used_credits as "usedCredits", plan_type as "planType" 
                FROM user_credits 
                WHERE email = ${email}`
        );
        return NextResponse.json(updatedCreditsResult.rows?.[0] || updatedCreditsResult[0]);

    } catch (error) {
        console.error('Credits POST API error:', error);
        return NextResponse.json({ error: "Database error", details: error.message }, { status: 500 });
    }
}
```

**Explanation**: This `POST` endpoint receives the `amount` to deduct and details about the `action`. It first fetches the user's current credit balance. If the user doesn't have enough credits, it returns an "Insufficient credits" error. Otherwise, it updates the `used_credits` in the `user_credits` table and then inserts a new record into the `credit_usage` table, logging the transaction for historical tracking and debugging.

**2. Creating a Stripe Checkout Session (`app\api\create-checkout-session\route.js` - POST)**

This API is called when a user selects a paid plan on the "Upgrade" page. It securely interacts with Stripe to set up a payment.

```javascript
// From: app\api\create-checkout-session\route.js (simplified POST handler)
import { NextResponse } from 'next/server';
import Stripe from 'stripe'; // Stripe library

export const runtime = 'edge'; // Edge runtime is fine as it doesn't use JWT directly

const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
let stripe = new Stripe(stripeSecretKey, { apiVersion: '2023-10-16' }); // Initialize Stripe

export async function POST(req) {
  try {
    const { priceId, planName, billingPeriod } = await req.json(); // Get details from frontend

    // IMPORTANT: In a real app, you'd associate this session with the *logged-in user's ID*
    // For this example, we assume `userId` is somehow passed or derived.
    const mockUserId = 'user_abc123'; // Replace with actual user ID from auth

    // Create a checkout session with Stripe
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [{ price: priceId, quantity: 1 }], // The Stripe Price ID for the plan
      mode: 'subscription', // This indicates it's a recurring payment
      success_url: `http://localhost:3255/dashboard/payment-success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `http://localhost:3255/dashboard/upgrade`,
      metadata: { // Custom data that Stripe sends back with the webhook
        userId: mockUserId,
        planName,
        billingPeriod
      },
    });

    return NextResponse.json({ sessionId: session.id, url: session.url });
  } catch (error) {
    console.error('Error creating Stripe checkout session:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
```

**Explanation**: This endpoint receives the `priceId` of the chosen plan. It then uses the `stripe` library to call Stripe's API and create a `checkout.session`. This session is configured for a `subscription` and includes `success_url` and `cancel_url` for redirects. Crucially, it embeds `metadata` like `userId`, `planName`, and `billingPeriod`. This `metadata` is very important because Stripe will send it back to us via a webhook, allowing us to identify the user and plan once the payment is successful. The `session.url` (Stripe's payment page) is returned to the frontend.

**3. Handling Stripe Webhooks (`app\api\webhooks\stripe\route.js` - POST)**

This is the most critical part of the billing system. Stripe sends messages (webhooks) to this endpoint whenever an important event happens (like a successful payment, a subscription update, or a cancellation).

```javascript
// From: app\api\webhooks\stripe\route.js (simplified POST handler)
import { NextResponse } from 'next/server';
import Stripe from 'stripe';
import { headers } from 'next/headers';
import { db } from '@/configs/db.jsx';
import { Users, UserCredits, CreditUsage } from '@/configs/schema.jsx'; // Our database schemas
import { eq } from 'drizzle-orm';

export const runtime = 'edge';

const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET; // Special secret for webhook verification
let stripe = new Stripe(stripeSecretKey, { apiVersion: '2023-10-16' });

export async function POST(req) {
  const body = await req.text();
  const signature = headers().get('stripe-signature'); // Stripe's unique signature

  let event;
  try {
    // Verify the webhook signature to ensure it's truly from Stripe
    event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
  } catch (err) {
    console.error(`Webhook signature verification failed: ${err.message}`);
    return NextResponse.json({ error: 'Webhook signature verification failed' }, { status: 400 });
  }

  // Handle different event types
  switch (event.type) {
    case 'checkout.session.completed':
      const session = event.data.object;
      await handleCheckoutSessionCompleted(session); // Our custom function below
      break;
    case 'customer.subscription.updated':
    case 'customer.subscription.deleted':
      const subscription = event.data.object;
      await handleSubscriptionChange(subscription); // Our custom function below
      break;
    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  return NextResponse.json({ received: true });
}

// Function to handle successful checkout sessions
async function handleCheckoutSessionCompleted(session) {
  // Extract custom metadata we sent earlier
  const { userId, planName, billingPeriod } = session.metadata; 
  
  if (!userId) { // Safety check
    console.error('No userId found in session metadata'); return;
  }

  try {
    // 1. Get user's email from our 'Users' table using the userId
    const userRecord = await db.select({ email: Users.email })
      .from(Users).where(eq(Users.id, userId)).limit(1);
    const userEmail = userRecord[0].email;

    // 2. Determine credits based on the purchased plan
    let totalCredits = 5; // Default for free
    if (planName === 'Standard') totalCredits = 50;
    else if (planName === 'Enterprise') totalCredits = 999999; // Unlimited

    // 3. Update user's subscription and credits in our database
    await db.transaction(async (tx) => { // Use a database transaction for atomicity
      // Update 'Users' table with Stripe customer/subscription IDs and plan type
      await tx.update(Users)
        .set({ stripeCustomerId: session.customer, stripeSubscriptionId: session.subscription, planType: planName })
        .where(eq(Users.id, userId));
      
      // Update or create 'UserCredits' record
      const existingCredits = await tx.select().from(UserCredits).where(eq(UserCredits.email, userEmail));
      if (existingCredits.length > 0) {
        await tx.update(UserCredits).set({ totalCredits, planType: planName, lastUpdated: new Date() })
          .where(eq(UserCredits.email, userEmail));
      } else {
        await tx.insert(UserCredits).values({ email: userEmail, totalCredits, usedCredits: 0, planType: planName, createdAt: new Date() });
      }
      
      // Record credit addition in 'CreditUsage' table
      await tx.insert(CreditUsage)
        .values({ email: userEmail, amount: totalCredits, action: `Subscription to ${planName} plan`, createdAt: new Date() });
    });
    console.log(`User ${userId} successfully subscribed to ${planName}. Credits updated.`);
  } catch (error) {
    console.error('Error processing checkout.session.completed:', error);
  }
}

// Function to handle subscription status changes (e.g., cancellation)
async function handleSubscriptionChange(subscription) {
  if (subscription.status === 'canceled' || subscription.status === 'unpaid') {
    console.log(`Subscription ${subscription.id} is ${subscription.status}, downgrading to Free plan`);
    const userRecord = await db.select().from(Users).where(eq(Users.stripeSubscriptionId, subscription.id));
    if (userRecord.length > 0) {
      const user = userRecord[0];
      await db.transaction(async (tx) => {
        await tx.update(Users).set({ planType: 'Free' }).where(eq(Users.id, user.id));
        await tx.update(UserCredits).set({ totalCredits: 5, planType: 'Free', lastUpdated: new Date() })
          .where(eq(UserCredits.email, user.email));
        await tx.insert(CreditUsage)
          .values({ email: user.email, amount: -45, action: 'Subscription downgraded to Free plan', createdAt: new Date() }); // Example deduction
      });
      console.log(`User ${user.id} downgraded to Free plan.`);
    }
  }
}
```

**Explanation**:
1.  **Signature Verification**: The first crucial step is `stripe.webhooks.constructEvent`. This function verifies that the incoming webhook is genuinely from Stripe and hasn't been tampered with, using `stripe-signature` header and `webhookSecret`. This is vital for security.
2.  **Event Handling**: Based on the `event.type` (e.g., `checkout.session.completed`, `customer.subscription.updated`), the server calls specific helper functions.
3.  **`handleCheckoutSessionCompleted`**: This function is called when a user successfully completes a payment. It retrieves the `userId` and `planName` from the `session.metadata`. It then performs a **database transaction** (a group of database operations that either all succeed or all fail, ensuring data consistency). Inside the transaction:
    *   It updates the `Users` table with the `stripeCustomerId`, `stripeSubscriptionId`, and `planType`.
    *   It then updates or creates the user's entry in the `UserCredits` table, assigning the correct `totalCredits` based on the new plan.
    *   Finally, it logs this credit addition in the `CreditUsage` table.
4.  **`handleSubscriptionChange`**: This function handles updates or cancellations. For example, if a subscription is `canceled`, it finds the associated user and downgrades their `planType` and `totalCredits` back to the "Free" plan, and records this change.

**4. Debug Endpoints (for Developers)**

For development and testing, we have special endpoints that let us inspect and manipulate credit data:
*   `app\api\debug\credits\route.js` (GET): Fetches all records from `UserCredits` and `CreditUsage` tables, useful for seeing everyone's balances and history.
*   `app\api\debug\test-credits\route.js` (POST): Allows you to easily add or update credits for a test user, simulating a subscription upgrade without going through the entire Stripe payment flow. This is super handy during development!

### Conclusion

In this chapter, we've unravelled the workings of the **Credit & Billing System**. We learned how credits act as our digital currency for AI features, how different subscription plans provide varying credit amounts, and how we leverage Stripe for secure payment processing. We explored the journey of a credit, from its deduction after an AI generation to its acquisition through a Stripe-powered payment, meticulously tracked in our database. This robust system ensures fair usage and sustainable operation of our powerful AI features.

Now that we understand how users can pay for and use our platform's features, let's explore how users can collaborate and share their learning content with others!

**Next Chapter**: [Workspace Collaboration](05_workspace_collaboration_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)