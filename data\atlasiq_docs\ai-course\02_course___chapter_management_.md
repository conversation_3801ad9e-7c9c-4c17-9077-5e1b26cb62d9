# Chapter 2: Course & Chapter Management

Welcome back! In [Chapter 1: User Authentication & Authorization](01_user_authentication___authorization_.md), we learned how our `ai-course` platform acts as a secure club, ensuring only authorized users can enter and access specific features. Now that we know *who* is allowed in, the big question is: **What kind of content can they create and manage inside this club?**

This is where **Course & Chapter Management** comes in. Imagine our `ai-course` platform as a vast digital library. This chapter is about understanding how we organize all the "books" (courses) and their "sections" (chapters) within this library. It’s the core system that allows you to design, build, and manage your digital textbooks and all their individual lessons.

### The Big Problem: Organizing Learning Content

How do we let users create a brand-new educational course from scratch? How do we break it down into logical parts, store all the learning materials, and then let students browse them or allow creators to edit them? This is the central problem "Course & Chapter Management" solves.

Let's use a very common task: **creating a new online course on the ai-course platform.**

Our goal for this chapter is to understand how, when you decide to build a new course, the system helps you define its structure, populate it with content, and then make it available for learning.

### Key Concepts: Courses and Chapters

These are the two main building blocks of our educational content:

1.  **Course**: Think of a course as a complete "digital textbook" or a comprehensive subject area (e.g., "Introduction to Python," "Advanced Data Science").
    *   **Analogy**: It's like a complete book in our digital library.
    *   **In our project**: A course has a title, description, category, level, and, most importantly, a list of chapters.

2.  **Chapter**: A chapter is a smaller, focused section within a course (e.g., "Variables in Python," "Data Structures").
    *   **Analogy**: It's like a specific chapter or section within that digital book.
    *   **In our project**: Each chapter has a name, a unique ID within its course, and holds the actual lesson content (text, code examples, videos, etc.).

### Solving the Use Case: Creating a New Course

Let's walk through the process of creating a course. It often starts with just an idea, and our platform helps structure that idea into a full course outline.

#### How the Frontend Handles Course Creation (Simplified)

When you navigate to the "Create Course" section (which uses the component `app\create-course\page.jsx`), you'll see a series of steps (a "stepper") guiding you to define your course. You'll input details like:

*   **Category**: What type of course is it? (e.g., Technology, Business)
*   **Topic & Description**: The main subject and a brief overview.
*   **Level**: Is it for beginners, intermediate, or advanced learners?
*   **Outputs**: What kind of content should be generated (e.g., code examples, exercises).
*   **Language**: The language of the course.
*   **AI Model**: Which AI model should be used for generation.

Once you've filled in these details, you click the "Generate outline" button.

```javascript
// From: app\create-course\page.jsx (simplified)
import { db } from '@/configs/db.jsx'; // Our database connection
import { CourseList, Chapters } from '@/configs/schema'; // Database schemas
import uuid4 from 'uuid4'; // For generating unique IDs

function CreateCourse() {
    // ... state declarations for user input (userCourseInput), loading, etc. ...
    const [user, setUser] = useState(null); // Authenticated user
    const { userCourseInput } = useContext(UserInputContext); // User input from form

    const GenerateCourseLayout = async () => {
        setLoading(true);
        try {
            // 1. Call AI to generate the course outline (details in Chapter 3)
            const responseText = await GenerateCourseLayout_AI(userCourseInput, selectedConfig, maxTokens);
            
            // 2. Parse the AI's response (which should be JSON)
            let parsedResponse = JSON.parse(responseText); 
            
            // 3. Save the generated outline and create chapters in the database
            await SaveCourseLayoutInDb(parsedResponse);
            
            toast.success('Course outline generated successfully!');
        } catch (error) {
            // ... error handling ...
        } finally {
            setLoading(false);
        }
    };

    const SaveCourseLayoutInDb = async (courseLayout) => {
        const newCourseId = uuid4(); // Generate a unique ID for the new course
        try {
            // Insert the main course details into the 'CourseList' table
            await db.insert(CourseList).values({
                courseId: newCourseId,
                name: userCourseInput?.topic, // Course title from user input
                courseOutput: courseLayout, // The AI-generated outline (chapters, etc.)
                createdBy: user?.email,     // Link to the creating user
                // ... other fields like level, category, userName, etc. ...
            });

            // Create placeholder entries for each chapter in the 'Chapters' table
            const totalChapters = parseInt(userCourseInput?.noOfChapter);
            const chapterInsertPromises = Array.from({ length: totalChapters }, (_, idx) => {
                const chapterData = courseLayout.course.chapters[idx] || {};
                return db.insert(Chapters).values({
                    courseId: newCourseId, // Link chapter to the new course
                    chapterId: idx,        // Chapter number (0, 1, 2...)
                    chapterName: chapterData.name || `Chapter ${idx + 1}`,
                    content: { title: chapterData.name, description: chapterData.about || "" }, // Initial empty content
                    // ... other default chapter fields like videoId, outcomes, files ...
                });
            });

            await Promise.all(chapterInsertPromises); // Insert all chapters concurrently

            // For [Credit & Billing System](04_credit___billing_system_.md): A credit is used here.
            // await useCredit(newCourseId); 

            router.push('/create-course/' + newCourseId); // Redirect to the course's page
        } catch (error) {
            console.error("Error saving course:", error);
            throw error; 
        }
    };
    
    return (
        // ... JSX for the stepper form and buttons ...
        <button onClick={() => GenerateCourseLayout()}>
            Generate outline
        </button>
    );
}
```

**Explanation**: When you click "Generate outline", the frontend gathers all your inputs (`userCourseInput`). It then calls an AI function (details for this are in [Chapter 3: AI Content Generation](03_ai_content_generation_.md)) to generate a course structure (like a table of contents with chapter names and descriptions). Once the AI provides this `courseLayout`, the `SaveCourseLayoutInDb` function kicks in. This function creates a unique `courseId`, saves the main course details (including the AI-generated outline) into the `CourseList` table, and then creates empty "placeholder" entries for each chapter in the `Chapters` table. Finally, you are redirected to the new course's main page.

**Input**: Your course preferences (topic, level, category, number of chapters, etc.).
**Output**: A new course outline is created in the database, and you are redirected to a page displaying it.

#### How to View and Edit Courses/Chapters (Frontend Perspective)

After generation, you're taken to a page like `app\create-course\[courseId]\page.jsx` (which shares functionality with `app\course\[courseId]\page.jsx`). Here, you can see your course's basic information and the list of chapters.

You can then click a button like "Generate Course Content" to fill each chapter with detailed lessons, or click on an individual chapter to view and edit its content.

```javascript
// From: app\course\[courseId]\page.jsx (simplified)
import { useEffect, useState } from 'react';
import { db } from '@/configs/db';
import { CourseList, Chapters } from '@/configs/schema';
import { eq } from 'drizzle-orm';

function Course({ params }) {
    const [course, setCourse] = useState();
    const [chapters, setChapters] = useState([]); // This will hold the actual chapter content from DB
    // ... other state for loading, etc. ...

    const refreshData = async () => {
        try {
            // Fetch the main course details from the 'CourseList' table
            const courseResult = await db.select()
                .from(CourseList)
                .where(eq(CourseList.courseId, params?.courseId));

            if (courseResult && courseResult[0]) {
                setCourse(courseResult[0]);
                
                // Fetch the actual chapters' detailed content from the 'Chapters' table
                const chaptersResult = await db.select()
                    .from(Chapters)
                    .where(eq(Chapters.courseId, params?.courseId));
                setChapters(chaptersResult || []); // Store the fetched chapters
            }
        } catch (error) {
            console.error('Error refreshing data:', error);
        }
    };

    useEffect(() => {
        if (params?.courseId) {
            refreshData(); // Fetch course and chapters when component loads
        }
    }, [params?.courseId]);

    // ... JSX rendering the course details and chapters list ...
    return (
        <div>
            {course && (
                <div>
                    <h1>{course.name}</h1>
                    {/* Components like CourseBasicInfo, CourseDetail, ChapterList */}
                    {/* A button to "Generate Course Content" which triggers AI generation */}
                </div>
            )}
        </div>
    );
}
```

**Explanation**: When this page loads, it fetches the main `course` object and all associated `chapters` from our database using their `courseId`. The `courseOutput` field of the `CourseList` object contains the AI-generated *outline* of chapters (names, descriptions), while the `Chapters` table contains the detailed *content* of each chapter (text, video IDs, etc.). This separation allows for incremental content generation.

Clicking on a chapter in the `ChapterList` (which is typically part of `app\course\[courseId]\start\page.jsx`) allows you to view its content or edit it.

### Under the Hood: How Course & Chapter Management Works (Internal Implementation)

Let's peek behind the curtain to see how our backend APIs handle these operations.

#### High-Level Walkthrough

Here’s a simplified step-by-step process of how courses and chapters are managed:

```mermaid
sequenceDiagram
    participant User
    participant Frontend as Website (Your Browser)
    participant CourseAPI as Server (Course API)
    participant ChaptersAPI as Server (Chapters API)
    participant Database

    User->>Frontend: Requests "Create Course"
    Frontend->>CourseAPI: Sends Course Details (POST /api/courses)
    CourseAPI->>Database: Inserts Course Entry + Initial Chapter Entries
    Database-->>CourseAPI: Course/Chapters Saved
    CourseAPI-->>Frontend: Confirmation + New Course ID
    Frontend->>User: Shows Course Outline Page

    User->>Frontend: Clicks "Generate Course Content"
    Frontend->>ChaptersAPI: For each Chapter: Request AI Generation (PUT /api/chapters/[id]/content)
    ChaptersAPI->>Database: Updates Chapter Content
    Database-->>ChaptersAPI: Content Saved
    ChaptersAPI-->>Frontend: Overall Progress Update
    Frontend->>User: Shows detailed chapter content on page

    User->>Frontend: Views a Specific Chapter
    Frontend->>ChaptersAPI: Requests Chapter Content (GET /api/chapters?courseId=X)
    ChaptersAPI->>Database: Fetches Chapter Content
    Database-->>ChaptersAPI: Chapter Content
    ChaptersAPI-->>Frontend: Chapter Content
    Frontend->>User: Displays Chapter Content
```

**Explanation**:
1.  **Course Creation**: When you submit the "Create Course" form, your browser sends the details to the **Course API**. This API creates a record for your new course and placeholder entries for its chapters in the **Database**.
2.  **Content Generation**: After the outline is created, you can trigger content generation. This is a bigger process where the frontend orchestrates the filling of actual lesson material for *each* chapter, often by calling the **AI Content Generation** system (which we'll cover in [Chapter 3: AI Content Generation](03_ai_content_generation_.md)). Each chapter's content is then saved by the **Chapters API** back into the **Database**.
3.  **Viewing/Editing**: When you view a course or a specific chapter, the **Frontend** requests the data from the **Chapters API** (or Course API), which retrieves it from the **Database** and sends it back to your browser for display.

#### Code Deep Dive: Server's Role in Management

Let's look at the key backend APIs responsible for managing courses and chapters.

**1. Creating a Course and its Initial Chapters (`app\api\courses\route.js` - POST)**

This API endpoint is called when you click "Generate outline". It's responsible for creating the main course entry and basic chapter placeholders.

```javascript
// From: app\api\courses\route.js (simplified POST handler)
import { NextResponse } from 'next/server';
import { db } from '@/configs/db';
import { CourseList, Chapters } from '@/configs/schema';
import { verifyJWT, getTokenFromRequest } from '@/lib/auth'; // Auth check

export async function POST(request) {
  try {
    // Authenticate user using token from request (see Chapter 1)
    const token = getTokenFromRequest(request);
    const user = token ? await verifyJWT(token) : null;
    if (!user || !user.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { title, courseOutput } = await request.json(); // Get data from frontend
    const courseId = `course_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`; // Unique ID

    // Insert main course data into the 'CourseList' table
    await db.insert(CourseList).values({
      courseId,
      name: title,
      courseOutput: courseOutput, // Store the AI-generated outline here
      createdBy: user.email,     // Link course to creator's email
      createdAt: new Date(),
      // ... other course properties ...
    });

    // Insert placeholder chapters based on the AI-generated outline
    if (courseOutput?.course?.chapters) {
        for (let idx = 0; idx < courseOutput.course.chapters.length; idx++) {
            const chapterData = courseOutput.course.chapters[idx];
            await db.insert(Chapters).values({
                courseId: courseId,     // Link chapter to its course
                chapterId: idx,         // Unique ID for chapter within this course (e.g., 0, 1, 2)
                chapterName: chapterData.name || `Chapter ${idx + 1}`,
                content: { title: chapterData.name, description: chapterData.about || "" }, // Initial empty content
                // ... other default values like videoId, outcomes, files ...
            });
        }
    }
    
    return NextResponse.json({ success: true, courseId });
  } catch (error) {
    console.error('Error creating course:', error);
    return NextResponse.json({ error: 'Failed to create course' }, { status: 500 });
  }
}
```

**Explanation**: This code snippet shows how the server receives your course details, creates a unique `courseId`, and then inserts the primary course information into the `CourseList` table. Crucially, it then loops through the chapters suggested by the AI in `courseOutput` and creates a separate, *placeholder* entry for each in the `Chapters` table. These chapter entries initially have very little content; they're ready to be filled in later.

**2. Fetching Course Information (`app\api\courselist\[courseId]\route.js` - GET & `app\api\courses\route.js` - GET)**

There are multiple API endpoints for fetching courses. `app\api\courselist\[courseId]\route.js` is for getting a *single* course by its ID, and `app\api\courses\route.js` is for fetching *lists* of courses (e.g., all courses created by a user, or courses within a workspace).

```javascript
// From: app\api\courselist\[courseId]\route.js (simplified GET handler)
import { db } from '@/configs/db';
import { CourseList } from '@/configs/schema';
import { eq } from 'drizzle-orm';

export async function GET(req, { params }) {
  const { courseId } = params; // Get course ID from URL parameters
  // Select the course from the 'courseList' table where courseId matches
  const course = await db.select().from(CourseList).where(eq(CourseList.courseId, courseId)).then(rows => rows[0]);
  if (!course) {
    return new Response(JSON.stringify({ error: 'Course not found' }), { status: 404 });
  }
  return new Response(JSON.stringify(course), { status: 200 });
} 
```

**Explanation**: This `GET` endpoint simply retrieves a single course record from the `CourseList` table based on the `courseId` provided in the URL. This is used when you navigate directly to a course page.

The `app\api\courses\route.js` GET handler is more complex, handling filtering, searching, and pagination for lists of courses. It authenticates the user and then fetches courses primarily `WHERE "createdBy" = user.email`.

**3. Fetching Chapters for a Course (`app\api\chapters\route.js` - GET)**

When a course page loads, or when you switch chapters, the frontend needs to fetch the details of the chapters.

```javascript
// From: app\api\chapters\route.js (simplified GET handler)
import { NextResponse } from 'next/server';
import { db } from '@/configs/db';
import { sql } from 'drizzle-orm'; // Using raw SQL for simplicity here

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const courseId = searchParams.get('courseId'); // Get courseId from query parameters
    
    if (!courseId) {
      return NextResponse.json({ error: 'Course ID is required' }, { status: 400 });
    }
    
    // Query the 'chapters' table for all chapters belonging to this courseId
    const chaptersResult = await db.execute(sql`
      SELECT * FROM "chapters" 
      WHERE "courseId" = ${courseId}
      ORDER BY "chapterId" ASC
    `);
    
    // Parse JSON fields from the database result strings into JavaScript objects
    const chapters = chaptersResult.rows.map(chapter => {
      return {
        ...chapter,
        content: typeof chapter.content === 'string' ? JSON.parse(chapter.content) : chapter.content,
        outcomes: typeof chapter.outcomes === 'string' ? JSON.parse(chapter.outcomes) : chapter.outcomes,
      };
    });
    
    return NextResponse.json({ success: true, chapters });
  } catch (error) {
    console.error('Error fetching chapters:', error);
    return NextResponse.json({ error: 'Failed to fetch chapters' }, { status: 500 });
  }
}
```

**Explanation**: This `GET` endpoint retrieves all chapter records from the `chapters` table that belong to a specific `courseId`. It's important to note that fields like `content` and `outcomes` are stored as JSON strings in the database, so the server parses them back into JavaScript objects before sending them to the frontend.

**4. Updating Chapter Content (`app\api\chapters\[chapterId]\content\route.js` - PUT)**

When you click "Generate Course Content" or edit a chapter directly, this API is responsible for saving the detailed content.

```javascript
// From: app\api\chapters\[chapterId]\content\route.js (simplified PUT handler)
import { NextResponse } from 'next/server';
import { db } from '@/configs/db';
import { Chapters } from '@/configs/schema';
import { eq, and } from 'drizzle-orm';

export async function PUT(request, { params, searchParams }) {
    try {
        const { chapterId } = params; // Get chapter ID from URL path
        const courseId = searchParams?.get('courseId'); // Get course ID from query parameters
        const updatedContent = await request.json(); // The new content for the chapter

        // Check if chapter exists for this course and update its content
        const existingChapter = await db.select().from(Chapters)
            .where(and(eq(Chapters.chapterId, parseInt(chapterId)), eq(Chapters.courseId, courseId)));

        if (existingChapter.length > 0) {
            await db.update(Chapters)
                .set({ 
                    content: updatedContent.content, // Save the new content (JSON object)
                    updated_at: new Date()
                })
                .where(and(eq(Chapters.chapterId, parseInt(chapterId)), eq(Chapters.courseId, courseId)));
        } else {
            // Create new chapter if it doesn't exist (robustness)
            await db.insert(Chapters).values({
                courseId: courseId,
                chapterId: parseInt(chapterId),
                content: updatedContent.content,
                // ... other default values ...
            });
        }
        
        return NextResponse.json({ success: true, message: 'Chapter content updated' });
    } catch (error) {
        console.error('Error updating chapter content:', error);
        return NextResponse.json({ error: 'Failed to update chapter content' }, { status: 500 });
    }
}
```

**Explanation**: This `PUT` endpoint receives the `chapterId` and `courseId` from the URL, and the new `content` from the request body. It then uses Drizzle ORM to update the `content` field of the specific chapter in the `Chapters` table. It also includes logic to *create* a chapter if it somehow doesn't exist, making it robust.

**5. Updating Chapter Name (`app\api\chapters\[chapterId]\name\route.js` - PUT)**

Besides content, you can also update the chapter's name.

```javascript
// From: app\api\chapters\[chapterId]\name\route.js (simplified PUT handler)
import { NextResponse } from "next/server";
import { db } from "@/configs/db";
import { Chapters, CourseList } from "@/configs/schema";
import { eq, and } from "drizzle-orm";

export async function PUT(request, { params }) {
    try {
        const { chapterId } = params;
        const { chapterName, courseId } = await request.json(); // New name and courseId

        // Update the chapter name in the 'chapters' table
        await db.update(Chapters)
            .set({ chapterName: chapterName, updated_at: new Date() })
            .where(and(eq(Chapters.courseId, courseId), eq(Chapters.chapterId, parseInt(chapterId))));

        // Also update the chapter name within the 'courseOutput' JSON of the CourseList
        const courseData = await db.select().from(CourseList).where(eq(CourseList.courseId, courseId));
        if (courseData && courseData.length > 0) {
            const course = courseData[0];
            if (course.courseOutput?.course?.chapters) {
                const chapterIndex = course.courseOutput.course.chapters.findIndex(ch => ch.chapterId === parseInt(chapterId));
                if (chapterIndex !== -1) {
                    course.courseOutput.course.chapters[chapterIndex].name = chapterName;
                    await db.update(CourseList).set({ courseOutput: course.courseOutput }).where(eq(CourseList.courseId, courseId));
                }
            }
        }

        return NextResponse.json({ success: true, chapter: { chapterId, chapterName } });
    } catch (error) {
        console.error('Error updating chapter name:', error);
        return NextResponse.json({ success: false, error: 'Failed to update chapter name' }, { status: 500 });
    }
}
```

**Explanation**: This API updates the `chapterName` in the `Chapters` table. More interestingly, it also updates the `name` of the corresponding chapter within the `courseOutput` JSON stored in the `CourseList` table. This ensures that the course's overall outline (which is part of `courseOutput`) stays consistent with individual chapter details.

**6. Deleting a Course (`app\api\courses\delete\route.js` or `app\api\course-delete\route.js` - POST/DELETE)**

When a user decides to delete a course, it's a cascading operation that removes all related data.

```javascript
// From: app\api\courses\delete\route.js (simplified POST/DELETE handler)
import { NextResponse } from 'next/server';
import { db } from '@/configs/db';
import { CourseList } from '@/configs/schema';
import { eq, sql } from 'drizzle-orm';
import { verifyJWT, cookies } from '@/lib/auth'; // Auth & Cookies

export async function POST(request) { // Can also be a DELETE method for direct calls
  try {
    // Authenticate user
    const token = cookies().get('atlas_token')?.value;
    const user = token ? await verifyJWT(token) : null;
    if (!user || !user.email) {
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    const { courseId, userEmail } = await request.json(); 
    const effectiveEmail = userEmail || user.email; 

    // 1. Verify user owns the course
    const course = await db.select({ createdBy: CourseList.createdBy })
      .from(CourseList).where(eq(CourseList.courseId, courseId));
    
    if (!course || course.length === 0 || course[0].createdBy !== effectiveEmail) {
      return NextResponse.json({ success: false, error: 'Unauthorized or Course not found' }, { status: 403 });
    }

    // 2. Delete associated data first (important for database consistency!)
    await db.execute(sql`DELETE FROM "chapters" WHERE "courseId" = ${courseId}`); // Delete all chapters
    await db.execute(sql`DELETE FROM "chapter_surveys" WHERE "courseId" = ${courseId}`); // Delete surveys (if any)
    await db.execute(sql`DELETE FROM "workspace_courses" WHERE "course_id" = ${courseId}`); // Delete workspace links

    // 3. Finally, delete the main course entry
    await db.execute(sql`DELETE FROM "courseList" WHERE "courseId" = ${courseId}`);

    return NextResponse.json({ success: true, message: 'Course and related data deleted' });
  } catch (error) {
    console.error('Failed to delete course:', error);
    return NextResponse.json({ success: false, error: 'Failed to delete course' }, { status: 500 });
  }
}
```

**Explanation**: This API handles course deletion. After verifying the user's authorization (making sure only the course creator can delete it), it performs a crucial step: it deletes all related data (chapters, chapter surveys, workspace associations) *before* deleting the main course entry from `CourseList`. This is essential to prevent database errors due to "foreign key constraints" (where one table refers to data in another, so the referred data must be deleted first).

### Conclusion

In this chapter, we've explored the heart of the `ai-course` platform: **Course & Chapter Management**. We learned how courses are like digital textbooks, broken down into individual chapters. We walked through the process of creating a course outline with AI assistance, how these outlines and chapter placeholders are saved in our database, and how content is generated and managed within each chapter. This system provides the structure for all the valuable learning content our platform will offer.

Now that we know how to organize and manage our educational content, let's dive into how AI actually helps us generate that content!

**Next Chapter**: [AI Content Generation](03_ai_content_generation_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)