# Atlas University Agentic RAG System

A modular Retrieval-Augmented Generation (RAG) system developed for Atlas University in Istanbul, Turkey. This system supports multiple chatbots with different document repositories, databases, and prompt templates to assist students, administrators, and researchers.

## Features

- **Multiple Chatbots**: StudentBot, AdminBot, AcademicBot with specialized tools and knowledge bases
- **Modular Tools**: Document search, MongoDB queries, SQL queries, and web search
- **Self-RAG Implementation**: Intelligent tool selection based on query analysis
- **Conversation Memory**: Context-aware conversations with session management
- **FastAPI Backend**: RESTful API with automatic documentation
- **Gradio UI**: Optional web interface for easy testing
- **Docker Support**: Containerized deployment with health checks

## Quick Start

### Prerequisites

- **OpenAI API Key**: Get from [OpenAI Platform](https://platform.openai.com/api-keys)
- **Tavily API Key**: Get from [Tavily](https://tavily.com/)
- Docker & Docker Compose (recommended) OR Python 3.11+

### 1. Docker Installation (Recommended)

```bash
# Clone repository
git clone https://github.com/berkincetin/agentic-rag.git
cd agentic-rag

# Set up environment variables
cp .env.example .env
# Edit .env with your API keys

# Start backend services
docker-compose -f docker/docker-compose.backend-only.yml up --build -d

# Test the API
curl http://localhost:3820/
```

### 2. Manual Installation

```bash
# Clone and setup
git clone https://github.com/berkincetin/agentic-rag.git
cd agentic-rag

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment
cp .env.example .env
# Edit .env with your API keys
```

## Running the Application

### Backend API

**With Python:**
```bash
# Direct execution
python -m app.main

# Or with uvicorn
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

**With Docker:**
```bash
# Start services
docker-compose -f docker/docker-compose.backend-only.yml up -d

# View logs
docker-compose -f docker/docker-compose.backend-only.yml logs -f

# Stop services
docker-compose -f docker/docker-compose.backend-only.yml down
```

### Gradio UI (Optional)

After starting the backend, run the UI for easy testing:

```bash
python app/ui.py
```

Access at: http://localhost:7860

**UI Features:**
- Bot selection dropdown
- Chat interface with Turkish character support
- Session management
- Real-time API communication

## API Testing

### API Endpoints

- `GET /`: Health check
- `GET /bots`: List all available bots
- `GET /bots/{bot_name}`: Get bot information
- `POST /bots/{bot_name}/query`: Query a bot
- `POST /bots/{bot_name}/clear-memory`: Clear conversation memory
- `POST /reload`: Reload bot configurations

### Testing with PowerShell

```powershell
# Health check
Invoke-WebRequest -Uri "http://localhost:3820/" -Method GET

# List bots
Invoke-WebRequest -Uri "http://localhost:3820/bots" -Method GET

# Query a bot (Turkish support)
$body = @{
    query = "Merhaba, nasılsın?"
    session_id = "test-session-1"
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:3820/bots/StudentBot/query" -Method POST -Body $body -ContentType "application/json; charset=utf-8"
```

### Testing with curl

```bash
# Health check
curl http://localhost:3820/

# Query a bot
curl -X POST http://localhost:3820/bots/StudentBot/query \
  -H "Content-Type: application/json; charset=utf-8" \
  -d '{"query": "What courses are available?", "session_id": "test-1"}'
```

## Project Structure

```
atlas-q-a-rag/
├── app/
│   ├── core/                    # Core system components
│   ├── tools/                   # Tool implementations (Document, MongoDB, SQL, Web search)
│   ├── agents/                  # LangGraph agent implementations
│   ├── api/                     # FastAPI endpoints and middleware
│   ├── document_processing/     # Document processing pipeline
│   ├── main.py                  # FastAPI application entry point
│   └── ui.py                    # Gradio web interface
├── configs/                     # Bot configuration files (YAML)
├── prompts/                     # Prompt templates for each bot
├── data/                        # Document storage and processing
│   ├── raw/                     # Upload documents here
│   ├── processed/               # Processing metadata
│   └── chroma_stores/           # Vector store collections
├── docker/                      # Docker configuration files
├── scripts/                     # Utility scripts
└── requirements.txt             # Project dependencies
```

## Available Bots

### StudentBot
- **Purpose**: Assist students with academic queries
- **Tools**: Document search, course information, academic resources
- **Knowledge Base**: Student documents, course materials

### AdminBot
- **Purpose**: Support administrative staff
- **Tools**: SQL queries, administrative documents, facility management
- **Knowledge Base**: Administrative documents, staff information

### AcademicBot
- **Purpose**: Help researchers and faculty
- **Tools**: Academic document search, research databases
- **Knowledge Base**: Academic papers, research materials

### AtlasIQBot
- **Purpose**: General university information
- **Tools**: Web search, general documents, university policies
- **Knowledge Base**: University-wide information

## Available Tools

### DocumentSearchTool
- **Purpose**: Search through processed documents using vector embeddings
- **Supported Formats**: PDF, Word (.doc/.docx), Text (.txt), Markdown (.md)
- **Features**: Semantic search, Turkish character support, metadata filtering

### MongoDBQueryTool
- **Purpose**: Convert natural language to MongoDB queries
- **Features**: LLM-powered query generation, complex operators support
- **Languages**: English and Turkish query support

### SQLQueryTool
- **Purpose**: Query SQL databases with natural language
- **Supported DBs**: SQLite, PostgreSQL, MySQL, SQL Server
- **Security**: Table access restrictions, parameterized queries

### TavilySearchTool
- **Purpose**: Web search for real-time information
- **Features**: Current events, external knowledge, fact verification

## Document Processing

### Quick Setup

```bash
# Upload documents to data/raw/
cp your_document.pdf data/raw/academic/

# Process documents
python scripts/process_documents.py process-dir "data/raw/academic" "academic_docs"

# Update bot config to use processed documents
# Edit configs/academic_bot.yaml to include DocumentSearchTool
```

### Supported Formats
- **PDF**: Research papers, reports
- **Word**: Course materials, documentation
- **Text**: Plain text documents
- **Markdown**: Technical documentation

## Configuration

### Bot Configuration (YAML)

```yaml
name: StudentBot
description: Assistant for students

tools:
  - type: DocumentSearchTool
    enabled: true
    config:
      collection_name: student_documents
      top_k: 5

  - type: MongoDBQueryTool
    enabled: true
    config:
      database_name: student_db
      default_collection: courses

metadata:
  audience: students
  languages: [English, Turkish]
```

## System Limitations

### Technical Constraints
- **API Rate Limits**: OpenAI and Tavily API usage limits apply
- **Memory**: Conversation memory is session-based (not persistent)
- **Document Size**: Large documents may require chunking for processing
- **Concurrent Users**: Performance depends on server resources

### Security Considerations
- **Database Access**: SQL tool restricted to configured tables only
- **API Keys**: Store securely, never commit to version control
- **Network**: Use HTTPS in production environments
- **Input Validation**: All user inputs are validated and sanitized

### Language Support
- **Primary**: English and Turkish
- **Character Encoding**: Full UTF-8 support for Turkish characters (ğ, ı, ş, ç, ö, ü)
- **UI**: Bilingual interface with proper encoding

## Adding New Features

### Adding a New Bot
1. Create YAML config in `configs/` directory
2. Add prompt templates in `prompts/` directory
3. Restart application or call `/reload` endpoint

### Adding a New Tool
1. Create tool class inheriting from `BaseTool`
2. Implement the `execute` method
3. Register in `TOOL_CLASSES` dictionary

### Customizing Prompts
- Modify templates in `prompts/` directory
- Support for both English and Turkish prompts
- No code changes required

## Testing

### Unit Tests
```bash
# Run all tests
python -m pytest tests/

# Test specific component
python tests/test_api_endpoints.py
```

### Integration Tests
```bash
# Test Self-RAG implementation
python tests/test_self_rag.py

# Test document processing
python tests/document_processing/
```

## Troubleshooting

### Common Issues
1. **Port conflicts**: Change port in environment variables
2. **API key errors**: Verify keys in `.env` file
3. **Docker issues**: Check logs with `docker-compose logs`
4. **Turkish characters**: Ensure UTF-8 encoding in requests

### Logs
- **Application logs**: `logs/app.log`
- **API logs**: `logs/api.log`
- **Error logs**: `logs/errors.log`

## Support

- **Documentation**: Check `docs/` directory
- **Examples**: Review `examples/` directory
- **Issues**: Create GitHub issue for bugs
- **API Documentation**: Visit `/docs` endpoint when running

## License

MIT License - see LICENSE file for details.
