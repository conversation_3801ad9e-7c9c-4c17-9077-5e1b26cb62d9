# Chapter 3: Study Material & Course Management

Welcome back to the Atlas University LMS AI project! In our last chapter, [Data Persistence (Drizzle ORM)](02_data_persistence__drizzle_orm__.md), we learned how to securely save all your valuable learning content—like your personalized courses, notes, flashcards, and quizzes—into our digital filing cabinet (the database) using Drizzle ORM.

Saving data is fantastic, but what's the point if you can't easily access and use it? Imagine having a beautifully organized library where all the books are perfectly cataloged, but there's no way to read them! This is where **Study Material & Course Management** comes in.

This part of Atlas LMS AI is like your personal curriculum designer and presentation tool. It takes all the raw ingredients (your saved courses and study materials) and arranges them into a clear, easy-to-use learning experience. It handles everything you see when you view a course:
*   Showing you the course overview (its title, summary, and chapters).
*   Letting you navigate through chapters and notes.
*   Presenting interactive flashcards.
*   Allowing you to take quizzes.

Our main goal in this chapter is to understand how, once a course is created and saved, you can effortlessly **view your course details and interact with different study materials** like notes, flashcards, and quizzes.

## How Your Learning Content Comes to Life

Let's imagine you've just generated a new course on "The Basics of JavaScript." You want to:
1.  See an overview of the course.
2.  Browse through its chapters.
3.  Then, dive into the detailed notes for a chapter.
4.  Maybe later, practice with some flashcards or take a quiz!

This is exactly what the "Study Material & Course Management" system helps you do.

### 1. The Course Overview

When you click on a course from your dashboard, the first thing you see is its main page. This page acts as the central hub for that specific course. It's like the cover and table of contents of a book.

The core information displayed includes:
*   The course title and a brief summary.
*   The number of chapters.
*   A list of all the chapters, often with a short summary for each.
*   Buttons to access different types of study materials (Notes, Flashcards, Quiz, etc.).

This main course page is managed by the `app\course\[courseId]\page.jsx` file. Let's see how it fetches and displays the information:

```javascript
// File: app\course\[courseId]\page.jsx (Simplified)
"use client"
import axios from 'axios';
import { useParams } from 'next/navigation'
import React, { useEffect, useState } from 'react'
import CourseIntroCard from './_components/CourseIntroCard';
import StudyMaterialSection from './_components/StudyMaterialSection';
import ChapterList from './_components/ChapterList';
import { Loader2 } from 'lucide-react';

function Course() {
    const {courseId}=useParams(); // Gets the unique ID of the course from the web address
    const [course,setCourse]=useState(); // Stores the course data
    const [isLoading, setIsLoading] = useState(true);

    useEffect(()=>{
        GetCourse(); // Fetch course data when the page loads
    },[courseId]) // Re-run if courseId changes

    const GetCourse=async()=>{
        // Asks our backend API for the course details
        const result=await axios.get('/api/courses?courseId='+courseId);
        setCourse(result.data.result); // Save the fetched course data
        setIsLoading(false); // Stop showing loading spinner
    }

    if(isLoading) { /* Shows a spinning loader while data is fetched */
        return (<div className="flex flex-col justify-center items-center h-2/3 mt-10">
                    <Loader2 className="h-12 w-12 animate-spin text-primary" />
                    <h2 className=' mt-3 text-lg font-medium text-primary'>Loading...</h2>
                </div>);
    }
    if(!course) { return <div className="text-center mt-10">No course found</div>; }

    return (
        <div className='container mx-auto'>
            {/* Displays course title, summary, total chapters */}
            <CourseIntroCard course={course} />
            {/* Section with buttons for Notes, Flashcards, Quiz */}
            <StudyMaterialSection courseId={courseId} course={course} />
            {/* Lists all the chapters of the course */}
            <ChapterList course={course} />
        </div>
    )
}
export default Course
```
**What's happening here?**
This `Course` component is the orchestrator. When you visit a specific course page (like `/course/123`), it uses `useParams()` to grab the `courseId`. Then, it makes a request to our backend (`/api/courses`) to get all the details for that course. Once it has the `course` data, it passes it down to smaller, specialized components like `CourseIntroCard` (for the header), `StudyMaterialSection` (for the study options), and `ChapterList` (for the chapter breakdown).

### 2. Exploring Chapters

The `ChapterList` component takes the `courseLayout` (which contains the `chapters` information, saved as flexible JSON from [Data Persistence (Drizzle ORM)](02_data_persistence__drizzle_orm__.md)) and simply displays them. It's your course's Table of Contents!

```javascript
// File: app\course\[courseId]\_components\ChapterList.jsx (Simplified)
import React from 'react'

function ChapterList({course}) {
    // We get the chapters directly from the course's layout
    const CHAPTERS = course?.courseLayout?.chapters
  return (
    <div className='mt-5'>
        <h2 className='font-medium text-xl'>Chapters</h2>
        <div className='mt-3'>
            {CHAPTERS?.map((chapter,index)=>(
                <div key={index} className='flex gap-5 items-center p-4 border shadow-md mb-2 rounded-lg'>
                    <h2 className='text-2xl'>{chapter?.emoji}</h2> {/* Fun icon for the chapter */}
                    <div>
                        <h2 className='font-medium'>{chapter?.chapter_title||chapter?.chapterTitle}</h2>
                        <p className='text-gray-400 text-sm'>{chapter?.summary}</p>
                    </div>
                </div>
            ))}
        </div>
    </div>
  )
}
export default ChapterList
```
**How it works:** This component iterates through the `chapters` array found within the `course.courseLayout` object. Each chapter's title, summary, and even an emoji are neatly displayed, giving you a quick glance at what each part of the course covers.

### 3. Accessing Different Study Materials

This is where you choose how you want to learn! The `StudyMaterialSection` displays cards for different study types: Notes, Flashcards, Quiz, and Question/Answer. Each card tells you if the content is "Ready" (already generated) or if you need to "Generate" it.

```javascript
// File: app\course\[courseId]\_components\StudyMaterialSection.jsx (Simplified)
import React, { useEffect, useState } from 'react'
import MaterialCardItem from './MaterialCardItem' // Component for each material type card
import axios from 'axios'
import { Loader2 } from 'lucide-react';

function StudyMaterialSection({courseId,course}) {
    const [studyTypeContent,setStudyTypeContent]=useState(); // Tracks which content is ready
    const [isLoading, setIsLoading] = useState(true);

    // List of all available study material types
    const MaterialList=[
        { name:'Notes/Chapters', desc:'Read notes to prepare it', icon:'/notes.png', path:'/notes', type:'notes' },
        { name:'Flashcard', desc:'Flashcard to remember the concepts', icon:'/flashcard.png', path:'/flashcards', type:'flashcard' },
        { name:'Quiz', desc:'Great way to test your knowledge', icon:'/quiz.png', path:'/quiz', type:'quiz' },
        // ... more types
    ]

    useEffect(()=>{
        GetStudyMaterial(); // Check which materials are already generated
    },[courseId])

    const GetStudyMaterial=async()=>{
        // Asks our backend API to tell us what study materials exist for this course
        const result=await axios.post('/api/study-type',{
            courseId:courseId,
            studyType:'ALL' // Request information for ALL study types
        })
        setStudyTypeContent(result.data) // Store the status of materials
        setIsLoading(false);
    }

    if(isLoading) { /* ... loading spinner ... */ }

    return (
        <div className='mt-5'>
            <h2 className='font-medium text-xl'>Study Material</h2>
            <div className='grid grid-cols-2 md:grid-cols-4 gap-5 mt-3'>
                {MaterialList.map((item,index)=>(
                    <MaterialCardItem item={item} key={index}
                        studyTypeContent={studyTypeContent} // Pass readiness status
                        course={course}
                        refreshData={GetStudyMaterial} // Function to re-check after generation
                    />
                ))}
            </div>
        </div>
    )
}
export default StudyMaterialSection
```
**Explanation:** This component dynamically renders cards for each study type. Crucially, it fetches the status of existing study materials from the `/api/study-type` endpoint. If a certain type of content (like flashcards) hasn't been generated yet (from [AI Content Generation Engine](04_ai_content_generation_engine_.md)), the `MaterialCardItem` will show a "Generate" button. Otherwise, it shows a "View" button.

### 4. Retrieving Specific Study Materials (Backend API)

When you click "View Notes" or "View Flashcards," your browser sends a request to our backend. The `app\api\study-type\route.jsx` file is the heart of fetching specific study content from our database.

```javascript
// File: app\api\study-type\route.jsx (Simplified)
import { db } from "@/configs/db"; // Our database connection from Chapter 2
import { CHAPTER_NOTES_TABLE, STUDY_TYPE_CONTENT_TABLE } from "@/drizzle/schema"; // Our database blueprints
import { and, eq } from "drizzle-orm"; // Drizzle ORM tools
import { NextResponse } from "next/server";

export async function POST(req) {
    const {courseId,studyType}=await req.json(); // Get course ID and desired study type

    if(studyType=='notes') {
        // If 'notes' are requested, get them from CHAPTER_NOTES_TABLE
        const notes=await db.select().from(CHAPTER_NOTES_TABLE)
            .where(eq(CHAPTER_NOTES_TABLE.courseId,courseId));
        return NextResponse.json(notes);
    } else {
        // For 'Flashcard', 'Quiz', or 'QA', get from STUDY_TYPE_CONTENT_TABLE
        const result=await db.select().from(STUDY_TYPE_CONTENT_TABLE)
            .where(and( eq(STUDY_TYPE_CONTENT_TABLE.courseId,courseId),
            eq(STUDY_TYPE_CONTENT_TABLE.type,studyType)))
        return NextResponse.json(result[0]??[]); // Return the first matching content
    }
}
```
**How it works:** This API endpoint acts like a librarian. You tell it the `courseId` and `studyType` you need (e.g., "notes" or "Flashcard"). It then uses Drizzle ORM (our super librarian from [Data Persistence (Drizzle ORM)](02_data_persistence__drizzle_orm__.md)) to find the correct data in the `CHAPTER_NOTES_TABLE` or `STUDY_TYPE_CONTENT_TABLE` and sends it back to your browser.

### 5. Displaying Different Study Materials

Once the data for notes, flashcards, or quizzes is fetched, dedicated pages take over to display them in an interactive way.

#### a) Viewing Notes

The `app\course\[courseId]\notes\page.jsx` component is responsible for showing your chapter notes. Since notes can be long, it uses a `StepProgress` component to let you navigate through them chapter by chapter.

```javascript
// File: app\course\[courseId]\notes\page.jsx (Simplified)
"use client"
import axios from 'axios'
import { useParams } from 'next/navigation'
import React, { useEffect, useState } from 'react'
import StepProgress from '../_components/StepProgress'; // For chapter navigation
import { Loader2 } from 'lucide-react';

function ViewNotes() {
    const { courseId } = useParams();
    const [notes, setNotes] = useState();
    const [stepCount, setStepCount] = useState(0) // Tracks current chapter/note
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        GetNotes(); // Fetch notes when page loads
    }, [courseId])

    const GetNotes = async () => {
        // Requests notes from our API (which uses the route.jsx we just saw)
        const result = await axios.post('/api/study-type', {
            courseId: courseId,
            studyType: 'notes'
        });
        setNotes(result?.data); // Store the fetched notes
        setIsLoading(false);
    }

    if (isLoading) { /* ... loading spinner ... */ }

    return notes && ( // Only render if notes are loaded
        <div>
            {/* Shows progress and allows moving between note sections */}
            <div className='w-full md:w-1/2 mx-auto'>
                <StepProgress data={notes} setStepCount={setStepCount} stepCount={stepCount} />
            </div>
            <div className='mt-10 noteClass container mx-auto'>
                {/* Renders the HTML content of the current note */}
                <div dangerouslySetInnerHTML={{ __html: (notes[stepCount]?.notes)?.replace('```html', ' ') }} />
            </div>
        </div>
    )
}
export default ViewNotes
```
**Explanation:** This component fetches all notes for the course. It then displays one note (corresponding to `stepCount`) at a time. The `StepProgress` component allows you to click "Next" or "Previous" to move between notes. `dangerouslySetInnerHTML` is used because the notes are generated as rich HTML content.

#### b) Interacting with Flashcards

The `app\course\[courseId]\flashcards\page.jsx` handles displaying and interacting with flashcards using a carousel.

```javascript
// File: app\course\[courseId]\flashcards\page.jsx (Simplified)
"use client"
import axios from 'axios';
import { useParams } from 'next/navigation'
import React, { useEffect, useState } from 'react'
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious, } from "@/components/ui/carousel" // UI components
import FlashcardItem from './_components/FlashcardItem'; // Component for a single flashcard
import { Loader2 } from 'lucide-react';

function Flashcards() {
    const { courseId } = useParams();
    const [flashCards, setFlashCards] = useState([]); // Stores flashcard data
    const [isFlipped, setIsFlipped] = useState(false); // To flip the card
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        GetFlashCards(); // Fetch flashcards when page loads
    }, [courseId])

    const GetFlashCards = async () => {
        // Requests flashcards from our API
        const result = await axios.post('/api/study-type', {
            courseId: courseId,
            studyType: 'Flashcard'
        });
        setFlashCards(result?.data); // Store the fetched flashcards
        setIsLoading(false);
    }

    const handleClick = (index) => {
        setIsFlipped(!isFlipped) // Toggle flip state when clicked
    }

    if (isLoading) { /* ... loading spinner ... */ }

    return (
        <div className='container mx-auto  w-full px-6'>
            <h2 className='font-bold text-2xl'>Flashcards</h2>
            <Carousel> {/* Enables sliding between flashcards */}
                <CarouselContent className='py-10'>
                    {flashCards?.content && flashCards.content?.map((flashcard, index) => (
                        <CarouselItem key={index} className="flex items-center justify-center">
                            <FlashcardItem handleClick={handleClick} // Pass click handler for flipping
                                isFlipped={isFlipped} // Pass current flip state
                                flashcard={flashcard} />
                        </CarouselItem>
                    ))}
                </CarouselContent>
                <CarouselPrevious /> {/* Button to go to previous flashcard */}
                <CarouselNext /> {/* Button to go to next flashcard */}
            </Carousel>
        </div>
    )
}
export default Flashcards
```
**Explanation:** This page fetches all flashcards for the course. It uses a `Carousel` to display one flashcard at a time, allowing you to easily slide between them. Each `FlashcardItem` component shows the question on one side and can be "flipped" to reveal the answer when clicked, helping you test your memory.

#### c) Taking a Quiz

The `app\course\[courseId]\quiz\page.jsx` allows you to test your knowledge with interactive quizzes.

```javascript
// File: app\course\[courseId]\quiz\page.jsx (Simplified)
"use client"
import axios from 'axios'
import { useParams } from 'next/navigation'
import React, { useEffect, useState } from 'react'
import StepProgress from '../_components/StepProgress'; // For question navigation
import QuizCardItem from './_components/QuizCardItem'; // Component for a single quiz question
import EndScreen from '../_components/EndScreen';
import { Loader2 } from 'lucide-react';

function Quiz() {
    const { courseId } = useParams();
    const [quizData, setQuizData] = useState();
    const [stepCount, setStepCount] = useState(0); // Tracks current question
    const [quiz, setQuiz] = useState([]); // Array of quiz questions
    const [correctAns, setCorrectAns] = useState(); // Stores the correct answer for feedback
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        GetQuiz() // Fetch quiz questions when page loads
    }, [courseId])

    const GetQuiz = async () => {
        // Requests quiz data from our API
        const result = await axios.post('/api/study-type', {
            courseId: courseId,
            studyType: 'Quiz'
        });
        setQuizData(result.data);
        setQuiz(result.data?.content?.questions); // Extract the array of questions
        setIsLoading(false);
    }

    const checkAnswer = (userAnswer, currentQuestion) => {
        if (userAnswer == currentQuestion?.answer) {
            // User chose the correct answer!
            setCorrectAns(currentQuestion?.answer)
            // ... set feedback to 'correct' ...
        } else {
            // User chose the wrong answer. Show the correct one.
            setCorrectAns(currentQuestion?.answer)
            // ... set feedback to 'incorrect' ...
        }
    }

    if (isLoading) { /* ... loading spinner ... */ }

    return (
        <div className='container mx-auto'>
            <h2 className='font-bold text-2xl text-center mb-4'>Quiz</h2>
            <StepProgress data={quiz} stepCount={stepCount} setStepCount={setStepCount} />
            <div>
                <QuizCardItem
                    quiz={quiz?.[stepCount]} // Pass the current question to display
                    userSelectedOption={(v) => checkAnswer(v, quiz?.[stepCount])} // Handle user's selection
                    correctAnswer={correctAns} // Show correct answer after user attempts
                />
            </div>
            {/* Display "Correct!" or "Incorrect, the answer is..." feedback here */}
            {/* ... feedback messages ... */}
            <EndScreen data={quiz} stepCount={stepCount} />
        </div>
    )
}
export default Quiz
```
**Explanation:** This page fetches quiz questions and displays them one by one, similar to the notes. The `QuizCardItem` shows a question and its options. When you select an option, the `checkAnswer` function immediately tells you if you were correct or not and reveals the right answer, providing instant learning feedback.

## Under the Hood: The Journey of Your Study Materials

Let's trace the path of your study materials from the database to your screen when you decide to review notes for a course.

```mermaid
sequenceDiagram
    participant User
    participant Frontend as Atlas LMS Frontend (Browser)
    participant BackendAPI as Atlas LMS Backend API
    participant DrizzleORM as Drizzle ORM
    participant AtlasDB as Atlas DB (Database)

    User->>Frontend: Clicks "View Course" for Course ABC
    Frontend->>BackendAPI: GET /api/courses?courseId=ABC
    BackendAPI->>DrizzleORM: Query STUDY_MATERIAL_TABLE for Course ABC details
    DrizzleORM->>AtlasDB: SQL SELECT query
    AtlasDB-->>DrizzleORM: Course Data (e.g., courseLayout JSON)
    DrizzleORM-->>BackendAPI: Course Data
    BackendAPI-->>Frontend: Course Data (JSON)
    Frontend->>User: Displays Course Overview, Chapter List, Study Material Options (Notes, Flashcards, Quiz)

    User->>Frontend: Clicks "View Notes"
    Frontend->>BackendAPI: POST /api/study-type (courseId: ABC, studyType: 'notes')
    BackendAPI->>DrizzleORM: Query CHAPTER_NOTES_TABLE for notes related to Course ABC
    DrizzleORM->>AtlasDB: SQL SELECT query
    AtlasDB-->>DrizzleORM: Notes Data (array of HTML strings)
    DrizzleORM-->>BackendAPI: Notes Data
    BackendAPI-->>Frontend: Notes Data (JSON array)
    Frontend->>User: Displays first chapter's notes with navigation (StepProgress)
```
As you can see, the process involves the user interacting with the `Frontend`, which then communicates with the `BackendAPI`. The `BackendAPI` uses `Drizzle ORM` to retrieve the relevant data from the `Atlas DB` (our database). Finally, the `Frontend` receives this data and presents it to the `User` in an interactive and organized way. This entire flow ensures that your learning content is easily accessible and engaging.

## Conclusion

In this chapter, we've explored the **Study Material & Course Management** system of Atlas LMS AI. We saw how it takes the courses and materials saved (thanks to [Data Persistence (Drizzle ORM)](02_data_persistence__drizzle_orm__.md)) and brings them to life. From viewing a course's overview and chapters to diving into interactive notes, flashcards, and quizzes, this system is designed to make your personalized learning journey smooth and engaging.

But where do all these amazing notes, flashcards, and quizzes come from in the first place? That's the real magic of Atlas LMS AI! Next, we'll uncover how the platform intelligently generates all this content for you, by exploring the [AI Content Generation Engine](04_ai_content_generation_engine_.md)!

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)