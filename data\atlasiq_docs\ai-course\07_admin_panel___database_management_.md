# Chapter 7: Admin Panel & Database Management

Welcome back for our final chapter! We've covered a lot in the `ai-course` project: from securing user access in [Chapter 1: User Authentication & Authorization](01_user_authentication___authorization_.md) to organizing courses in [Chapter 2: Course & Chapter Management](02_course___chapter_management_.md), generating content with AI in [Chapter 3: AI Content Generation](03_ai_content_generation_.md), managing credits in [Chapter 4: Credit & Billing System](04_credit___billing_system_.md), enabling team collaboration in [Chapter 5: Workspace Collaboration](05_workspace_collaboration_.md), and enhancing learning with interactive tools in [Chapter 6: Interactive Learning Tools](06_interactive_learning_tools_.md).

Now, imagine you're the person responsible for the entire platform. How do you keep an eye on everything? How do you manage all the users, courses, and ensure that crucial data is safe and sound? This is where the **Admin Panel & Database Management** comes in. It's the "command center" for administrators, providing tools to oversee and manage the entire platform. It's akin to a city manager's office, from which they can monitor city operations, manage citizens, and maintain the city's infrastructure.

### The Big Problem: Keeping the Platform Running Smoothly

As the platform grows, so does the need for oversight. How do we monitor usage trends, update user roles, fix course issues, and, most critically, protect all the valuable data from accidental loss or corruption? This is the central problem that the "Admin Panel & Database Management" system solves.

Let's focus on a core use case: **An administrator needs to monitor the overall health of the platform, manage a specific user's account, and perform a database backup.**

Our goal for this chapter is to understand how administrators access powerful tools to maintain, control, and secure the `ai-course` platform's operations and its underlying data.

### Key Concepts: Your Platform's Control Center

To understand this crucial part of our platform, let's break down its main components:

1.  **Admin Panel (The Control Interface)**: This is a special part of the website, only accessible to users with the `admin` role. It provides a user-friendly interface for all administrative tasks.
    *   **Analogy**: The cockpit of an airplane, designed specifically for pilots to control the aircraft, not for passengers.

2.  **Dashboards (The Overview Screens)**: These are visual summaries that display key statistics about the platform, like the number of users, courses, and recent activity.
    *   **Analogy**: The large monitor in the city manager's office showing live traffic, population stats, and public service requests.

3.  **User Management (Citizen Services)**: Tools to view, add, modify, or disable user accounts. Admins can change roles (e.g., make someone an `instructor`), reset passwords, or adjust their credits ([Chapter 4: Credit & Billing System](04_credit___billing_system_.md)).
    *   **Analogy**: The city's department that manages citizen registrations, issues permits, or resolves citizen complaints.

4.  **Course Management (Urban Planning)**: Allows administrators to oversee all courses, regardless of who created them. They can edit course details, review chapters, and even delete courses if necessary.
    *   **Analogy**: The urban planning department that oversees all city development projects, ensuring they meet standards and can be modified or removed if needed.

5.  **Activity Logs (Event Recorder)**: A chronological record of important events and actions happening on the platform, useful for debugging and auditing.
    *   **Analogy**: The city's security camera footage and incident reports, showing "who did what, when."

6.  **Database Backup & Restore (Disaster Recovery Plan)**: This is the most critical function for data safety. It allows admins to create copies (backups) of the entire database and, if something goes wrong, use those copies to restore the data to a previous state.
    *   **Analogy**: The city's archive system where all important documents are regularly copied and stored safely, ready to be retrieved in case of a fire or natural disaster.

### Solving the Use Case: Monitoring, User Management, and Backup

Let's see how an administrator would use these tools.

#### How the Frontend Handles Admin Tasks (Simplified)

Only users with the `admin` role can access `/admin` routes. The `app\admin\layout.jsx` component defines the navigation for these admin pages.

```javascript
// From: app\admin\layout.jsx
// Simplified snippet
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const AdminLayout = ({ children }) => {
  const pathname = usePathname();

  const navItems = [
    { href: '/admin/users', label: 'Users' },
    { href: '/admin/logs', label: 'Activity Logs' },
    { href: '/admin/courses', label: 'Courses' },
    { href: '/admin/backup', label: 'Backup' }, // Added for clarity
    { href: '/admin/dashboard', label: 'Dashboard' }, // Added for clarity
  ];

  return (
    <div className="min-h-screen bg-gray-100">
      <nav className="bg-white shadow-sm">
        {/* ... navigation links ... */}
        <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
          {navItems.map((item) => (
            <Link key={item.href} href={item.href}
              className={`${pathname === item.href ? 'border-blue-500' : 'border-transparent'}`}
            >
              {item.label}
            </Link>
          ))}
        </div>
      </nav>
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {children} {/* This is where dashboard, users, etc., pages are rendered */}
      </main>
    </div>
  );
};
```

**Explanation**: This `AdminLayout` ensures that only authenticated users with the `admin` role can even see and navigate these pages. It provides a simple navigation bar to jump between different admin sections like "Users," "Courses," "Activity Logs," and "Backup."

**1. Viewing the Admin Dashboard (`app\admin\dashboard\page.jsx`)**

When an admin navigates to `/admin/dashboard`, they see an overview of platform statistics.

```javascript
// From: app\admin\dashboard\page.jsx (simplified snippet)
import { useState, useEffect } from 'react';
import { Line, Pie, Bar } from 'react-chartjs-2'; // For charts

export default function DashboardPage() {
    const [dashboardData, setDashboardData] = useState(null);
    const [loading, setLoading] = useState(true);

    const fetchDashboardData = async () => {
        setLoading(true);
        try {
            const response = await fetch('/api/admin/dashboard?timeframe=all'); // <-- Fetches dashboard stats
            const data = await response.json();
            if (data.success) {
                setDashboardData(data.stats);
            } else {
                console.error('Failed to fetch dashboard data:', data.error);
            }
        } catch (err) {
            console.error('Error fetching dashboard data:', err);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchDashboardData(); // Fetch data when page loads
    }, []);

    if (loading) return <div>Loading dashboard...</div>;
    if (!dashboardData) return <div>No dashboard data available.</div>;

    return (
        <div className="container mx-auto p-6">
            <h1 className="text-2xl font-bold mb-6">Admin Dashboard</h1>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                {/* Example stat card for Total Courses */}
                <div className="bg-white p-6 rounded-lg shadow-sm">
                    <h3 className="text-lg font-semibold">Total Courses</h3>
                    <p className="text-3xl font-bold">{dashboardData.totalCourses}</p>
                    <p className="text-sm text-gray-500">{dashboardData.publishedCourses} Published</p>
                </div>
                {/* ... other stat cards for Users, Chapters, Surveys ... */}
            </div>
            {/* ... Charts for categories, levels, trends ... */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white p-6 rounded-lg shadow-sm">
                    <h3 className="text-lg font-semibold">Course Categories</h3>
                    {/* Pie chart for categories */}
                    <Pie data={{ labels: dashboardData.categories.map(c => c.name), /* ... */ }} />
                </div>
                {/* ... other charts ... */}
            </div>
        </div>
    );
}
```

**Explanation**: When the dashboard page loads, it sends a `GET` request to `/api/admin/dashboard`. The backend retrieves various statistics from the database (total users, courses, surveys, category distribution, etc.) and sends them back. The frontend then uses these `dashboardData` to display interactive charts and key metrics, giving the admin a snapshot of the platform's performance.

**2. Managing Users (`app\admin\users\page.jsx`)**

An admin can view and modify user details, including their roles and credits.

```javascript
// From: app\admin\users\page.jsx (simplified snippet)
import { useState, useEffect } from 'react';

export default function UsersPage() {
    const [users, setUsers] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [selectedUser, setSelectedUser] = useState(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isCreditsModalOpen, setIsCreditsModalOpen] = useState(false);
    const [creditsData, setCreditsData] = useState({});

    useEffect(() => {
        fetchUsers(); // Fetch users when the page loads
    }, []);

    const fetchUsers = async () => {
        setIsLoading(true);
        try {
            const response = await fetch('/api/admin/users'); // <-- Fetches all users
            const data = await response.json();
            if (data.success) {
                setUsers(data.users || []);
            } else {
                console.error('Failed to fetch users:', data.error);
            }
        } catch (error) {
            console.error('Error fetching users:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleEditUser = (user) => {
        setSelectedUser(user);
        // Populate form data with user's current info
        setIsModalOpen(true); // Open general user edit modal
    };

    const handleEditCredits = async (user) => {
        // Fetch specific credit details for the user
        const response = await fetch(`/api/admin/users/credits?email=${user.email}`); // <-- Fetches credit details
        const data = await response.json();
        if (data.success) {
            setCreditsData({ ...data.credits, email: user.email, operation: 'add', amount: 0 });
            setIsCreditsModalOpen(true); // Open credit edit modal
        } else {
            console.error('Failed to fetch user credits:', data.error);
        }
    };

    const handleUpdateCredits = async (e) => {
        e.preventDefault();
        // Send updated credits data to backend
        const response = await fetch('/api/admin/users/manage', { // <-- Sends update to manage API
            method: 'POST', // Or PUT for updates
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: creditsData.email,
                operation: creditsData.operation,
                amount: parseInt(creditsData.amount, 10)
            })
        });
        if (response.ok) {
            alert('Credits updated!');
            setIsCreditsModalOpen(false);
            fetchUsers(); // Refresh user list
        } else {
            alert('Failed to update credits!');
        }
    };

    return (
        <div className="container mx-auto p-6">
            <h1 className="text-2xl font-bold mb-6">User Management</h1>
            {/* ... Search bar, sorting ... */}
            <table className="min-w-full">
                {/* ... Table headers ... */}
                <tbody>
                    {users.map((user) => (
                        <tr key={user.id}>
                            <td>{user.name} ({user.email})</td>
                            <td>{user.role}</td>
                            <td>{user.totalCredits - user.usedCredits} / {user.totalCredits}</td>
                            <td>
                                <button onClick={() => handleEditUser(user)}>Edit</button>
                                <button onClick={() => handleEditCredits(user)}>Credits</button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
            {/* ... Modals for Edit User and Edit Credits (not shown here) ... */}
        </div>
    );
}
```

**Explanation**: The `UsersPage` fetches a list of all users from `/api/admin/users`. For each user, an admin can click "Edit" to change their name, email, or role, or "Credits" to adjust their credit balance. The "Credits" action first fetches the user's current credit information from `/api/admin/users/credits` and then sends updates (add or subtract `amount`) to `/api/admin/users/manage`.

**3. Database Backup & Restore (`app\admin\backup\page.jsx`)**

This page allows the administrator to create new database backups and restore from existing ones.

```javascript
// From: app\admin\backup\page.jsx (simplified snippet)
import { useState, useEffect } from 'react';

export default function BackupPage() {
    const [backups, setBackups] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [isRestoring, setIsRestoring] = useState(false);

    useEffect(() => {
        fetchBackups(); // Load existing backups when page loads
    }, []);

    const fetchBackups = async () => {
        try {
            const response = await fetch('/api/admin/backup'); // <-- Fetches list of backups
            const data = await response.json();
            if (data.success) {
                setBackups(data.backupFiles);
            } else {
                console.error('Failed to fetch backups:', data.error);
            }
        } catch (error) {
            console.error('Error fetching backups:', error);
        }
    };

    const createBackup = async () => {
        setIsLoading(true);
        try {
            const response = await fetch('/api/admin/backup', { method: 'POST' }); // <-- Triggers backup creation
            const data = await response.json();
            if (data.success) {
                alert('Backup created successfully!');
                fetchBackups(); // Refresh the list
            } else {
                alert('Failed to create backup: ' + data.error);
            }
        } catch (error) {
            console.error('Error creating backup:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const restoreBackup = async (backupFile) => {
        if (!confirm('Are you sure? This will overwrite ALL current data!')) return;
        setIsRestoring(true);
        try {
            const response = await fetch('/api/admin/restore', { // <-- Triggers database restore
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ backupFile }) // Tell backend which file to use
            });
            const data = await response.json();
            if (data.success) {
                alert('Database restored successfully!');
                // May need to refresh page or re-login after restore
            } else {
                alert('Failed to restore database: ' + data.error);
            }
        } catch (error) {
            console.error('Error restoring backup:', error);
        } finally {
            setIsRestoring(false);
        }
    };

    return (
        <div className="container mx-auto p-6">
            <h1 className="text-2xl font-bold mb-6">Database Backup & Restore</h1>
            <button onClick={createBackup} disabled={isLoading}>
                {isLoading ? 'Creating Backup...' : 'Create New Backup'}
            </button>
            <h3 className="text-xl font-semibold mt-8">Available Backups</h3>
            {backups.length === 0 ? (
                <p>No backups available</p>
            ) : (
                <ul>
                    {backups.map((backup) => (
                        <li key={backup.name} className="flex justify-between items-center py-2">
                            <span>{new Date(backup.timestamp).toLocaleString()} ({backup.name})</span>
                            <button onClick={() => restoreBackup(backup.name)} disabled={isRestoring}>
                                {isRestoring ? 'Restoring...' : 'Restore'}
                            </button>
                        </li>
                    ))}
                </ul>
            )}
        </div>
    );
}
```

**Explanation**: This page manages the lifecycle of database backups. It fetches a list of existing backups from `/api/admin/backup` (GET request). The "Create New Backup" button triggers a `POST` request to `/api/admin/backup`, which tells the server to save a copy of the database. The "Restore" button sends a `POST` request to `/api/admin/restore`, specifying which backup file to use to revert the database to a previous state. This is a powerful, potentially destructive, operation, so a confirmation prompt is crucial.

### Under the Hood: How Admin Panel & Database Management Works (Internal Implementation)

Let's peek behind the curtain to see how our backend APIs handle these crucial administrative operations.

#### High-Level Walkthrough

Here’s a simplified step-by-step process of how our server handles admin tasks:

```mermaid
sequenceDiagram
    participant Admin
    participant Frontend as Admin Panel (Browser)
    participant AdminAPI as Server (Backend API)
    participant Database
    participant FileSystem as Server (Backup Storage)

    Admin->>Frontend: Logs in (as admin)
    Frontend->>AdminAPI: Requests Authentication (checks role)
    AdminAPI-->>Frontend: Authenticated (as admin)

    Admin->>Frontend: Navigates to Dashboard
    Frontend->>AdminAPI: GET /api/admin/dashboard
    AdminAPI->>Database: Queries various statistics (users, courses, etc.)
    Database-->>AdminAPI: Statistical Data
    AdminAPI-->>Frontend: Dashboard Data
    Frontend->>Admin: Displays charts and stats

    Admin->>Frontend: Navigates to Users page / Manages user/credits
    Frontend->>AdminAPI: GET /api/admin/users (or POST/PUT /api/admin/users/manage)
    AdminAPI->>Database: Fetches / Updates User Data (or Credit Data)
    Database-->>AdminAPI: User Data / Status
    AdminAPI-->>Frontend: Success / Updated User List

    Admin->>Frontend: Clicks "Create New Backup"
    Frontend->>AdminAPI: POST /api/admin/backup
    AdminAPI->>Database: Reads all critical data
    Database-->>AdminAPI: All Data
    AdminAPI->>FileSystem: Writes data to a JSON backup file
    FileSystem-->>AdminAPI: File Saved
    AdminAPI-->>Frontend: Success + List of Backups

    Admin->>Frontend: Clicks "Restore" on a backup file
    Frontend->>AdminAPI: POST /api/admin/restore (with filename)
    AdminAPI->>FileSystem: Reads data from the specified backup file
    FileSystem-->>AdminAPI: Backup Data
    AdminAPI->>Database: Clears ALL current data
    Database-->>AdminAPI: Data Cleared
    AdminAPI->>Database: Inserts backup data into tables
    Database-->>AdminAPI: Data Restored
    AdminAPI-->>Frontend: Success (Warning: May require refresh)
```

**Explanation**:
1.  **Authentication**: An `Admin` user first logs in. The `AdminAPI` verifies their `role` in the `Database` (as seen in [Chapter 1: User Authentication & Authorization](01_user_authentication___authorization_.md)) to grant access to the `Admin Panel`.
2.  **Monitoring**: When the `Admin` views the Dashboard, the `Frontend` requests data from the `AdminAPI`. The `AdminAPI` queries the `Database` to gather counts of users, courses, chapters, etc., and sends this back to the `Frontend` for visualization.
3.  **User/Course Management**: When an `Admin` manages users or courses, the `Frontend` sends `POST` or `PUT` requests to the `AdminAPI` which then directly updates the relevant tables in the `Database`.
4.  **Backup**: To create a backup, the `Admin` triggers a `POST` request. The `AdminAPI` reads all the necessary data from the `Database` and then saves it as a structured JSON file on the server's `FileSystem`.
5.  **Restore**: To restore, the `Admin` selects a backup file and sends a `POST` request. The `AdminAPI` reads that file from the `FileSystem`, then performs a critical sequence: it first **deletes all existing data** from the `Database`'s tables and then **inserts** all the data from the backup file back into those tables. This makes the database revert to the state it was in when the backup was created.

#### Code Deep Dive: The Server's Administrative Backbone

Our project has several dedicated API routes for administrative functions. Crucially, almost all admin-related API routes perform a role check to ensure that only authenticated `admin` users can access them. This check is performed using the `verifyJWT` utility from `lib/auth.js` and `getTokenFromRequest` from `app/_utils/getTokenFromJWT.js` (as established in [Chapter 1: User Authentication & Authorization](01_user_authentication___authorization_.md)). For simplicity, some provided code snippets might have this check commented out in `development` environments, but it's essential for production.

**1. Admin Role Verification (Example from `app\api\admin\users\route.js` - GET)**

Before any sensitive admin action, the server verifies the user's role.

```javascript
// From: app\api\admin\users\route.js (simplified initial part of GET handler)
import { NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { getTokenFromRequest } from '@/app/_utils/getTokenFromJWT';

export async function GET(request) {
    try {
        const token = getTokenFromRequest(request);
        if (!token) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const userData = await verifyJWT(token);
        if (!userData || userData.role !== 'admin') {
            // In a real production app, you'd uncomment this line:
            // return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
            console.log('Development mode: Admin check skipped. User:', userData.role);
        }
        // ... proceed with fetching users if admin or in dev mode ...
```

**Explanation**: This snippet shows the standard security gate. It gets the `atlas_token` cookie from the request, verifies it, and then checks if the `role` inside the token is `admin`. If not, it rejects the request (unless in development mode for easier debugging).

**2. Admin Dashboard Data (`app\api\admin\dashboard\route.js` - GET)**

This API gathers and processes various statistics from the database to populate the admin dashboard.

```javascript
// From: app\api\admin\dashboard\route.js (simplified GET handler)
import { NextResponse } from 'next/server';
import { db } from '@/configs/db.jsx';
import { sql } from 'drizzle-orm'; // For raw SQL queries

export async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const timeframe = searchParams.get('timeframe') || 'all'; // e.g., 'week', 'month', 'all'
        let timeframeCondition = '';
        if (timeframe === 'month') { // Example for month
            timeframeCondition = `AND "created_at" > NOW() - INTERVAL '30 days'`;
        }
        
        // Count total courses and published courses
        const courseStats = await db.execute(sql`
            SELECT COUNT(*) as "totalCourses", COUNT(CASE WHEN "publish" = true THEN 1 END) as "publishedCourses"
            FROM "courseList" WHERE 1=1 ${sql.raw(timeframeCondition)}
        `);

        // Count total chapters
        const chapterStats = await db.execute(sql`
            SELECT COUNT(*) as "totalChapters" FROM "chapters" WHERE 1=1 ${sql.raw(timeframeCondition)}
        `);
        
        // Get number of users (simplified: counting unique creators)
        const userStats = await db.execute(sql`
            SELECT COUNT(DISTINCT "createdBy") as "totalUsers" FROM "courseList"
        `); // Simplified, actual user table would be better.

        // Get course categories distribution
        const categories = await db.execute(sql`
            SELECT "category" as name, COUNT(*) as count FROM "courseList"
            WHERE "category" IS NOT NULL GROUP BY "category" ORDER BY count DESC
        `);

        return NextResponse.json({
            success: true,
            stats: {
                totalCourses: Number(courseStats.rows[0]?.totalCourses) || 0,
                publishedCourses: Number(courseStats.rows[0]?.publishedCourses) || 0,
                totalChapters: Number(chapterStats.rows[0]?.totalChapters) || 0,
                totalUsers: Number(userStats.rows[0]?.totalUsers) || 0,
                categories: categories.rows || [],
                // ... other stats like levels, recent courses, etc.
            }
        });
    } catch (error) {
        console.error('Failed to fetch dashboard stats:', error);
        return NextResponse.json({ success: false, error: 'Failed to fetch dashboard stats' }, { status: 500 });
    }
}
```

**Explanation**: This `GET` endpoint queries different tables in the database (like `courseList` and `chapters`) using `COUNT(*)` to get totals. It also groups data by `category` or `level` to show distribution. The `timeframeCondition` allows the admin to view stats for different periods (e.g., last 7 days, last 30 days, or all time). The results are collected into a `stats` object and sent to the frontend.

**3. User Management (`app\api\admin\users\manage\route.js` - POST)**

This API endpoint handles adding new users, updating existing user details, and managing their credits.

```javascript
// From: app\api\admin\users\manage\route.js (simplified POST handler)
import { db } from '@/configs/db.jsx';
import { sql } from 'drizzle-orm';
import { NextResponse } from 'next/server';

export async function POST(req) {
    try {
        const requestData = await req.json();
        
        // Check if it's a credit update operation
        if (requestData.email && (requestData.operation === 'add' || requestData.operation === 'subtract')) {
            const { email, operation, amount } = requestData;
            // This calls a helper function to modify credits
            await handleCreditUpdate(email, operation, amount); 
            return NextResponse.json({ success: true, message: `Credits updated for ${email}` });
        }
        
        // Otherwise, it's a user create/update operation
        const { action, user } = requestData;
        
        if (action === 'create') {
            // Insert new user into the 'users' table
            await db.execute(sql`INSERT INTO users (name, email, username, role, clerk_id, password) 
                               VALUES (${user.name}, ${user.email}, ${user.username}, ${user.role}, 
                                       ${user.clerkId}, ${'hashed_temp_password'})`); // Hashed temporary password
            return NextResponse.json({ success: true, message: 'User created' });
        } else if (action === 'update') {
            // Update existing user in the 'users' table
            await db.execute(sql`UPDATE users SET name=${user.name}, email=${user.email}, 
                               username=${user.username}, role=${user.role} WHERE clerk_id=${user.clerkId}`);
            return NextResponse.json({ success: true, message: 'User updated' });
        }

        return NextResponse.json({ success: false, error: 'Invalid action' }, { status: 400 });
    } catch (error) {
        console.error('Error managing user/credits:', error);
        return NextResponse.json({ success: false, error: 'Failed to manage user/credits' }, { status: 500 });
    }
}

// Helper function to manage credits (called by the main POST handler)
async function handleCreditUpdate(email, operation, amount) {
    // This function modifies the 'user_credits' table based on add/subtract
    // For more details, refer to [Chapter 4: Credit & Billing System](04_credit___billing_system_.md)
    if (operation === 'add') {
        await db.execute(sql`UPDATE "user_credits" SET "total_credits" = "total_credits" + ${amount} WHERE "email" = ${email}`);
    } else if (operation === 'subtract') {
        await db.execute(sql`UPDATE "user_credits" SET "total_credits" = "total_credits" - ${amount} WHERE "email" = ${email}`);
    }
    // A more robust implementation would check for sufficient credits for 'subtract'
}
```

**Explanation**: This `POST` endpoint is a multi-purpose tool. If the request includes `operation` as `add` or `subtract` for credits, it calls `handleCreditUpdate` to modify the `user_credits` table. Otherwise, it handles creating a new user (inserting into the `users` table with a generated temporary password) or updating an existing user's details.

**4. Course Management (`app\api\admin\courses\route.js` - GET, PUT, DELETE)**

Administrators can view, modify, and delete any course on the platform.

```javascript
// From: app\api\admin\courses\route.js (simplified GET handler)
import { NextResponse } from 'next/server';
import { db } from '@/configs/db.jsx';
import { sql } from 'drizzle-orm';

export async function GET() {
    try {
        const courses = await db.execute(sql`
            SELECT "courseId", "name", "createdBy", "category", "level", "publish", "includeVideo", "created_at"
            FROM "courseList" ORDER BY "created_at" DESC
        `);
        return NextResponse.json({ success: true, courses: courses.rows });
    } catch (error) {
        console.error('Failed to fetch courses:', error);
        return NextResponse.json({ success: false, error: 'Failed to fetch courses' }, { status: 500 });
    }
}

// From: app\api\admin\courses\route.js (simplified PUT handler)
export async function PUT(request) {
    try {
        const { courseId, updates } = await request.json();
        // updates might contain { name: 'New Course Name', publish: true, category: 'Tech' }
        await db.execute(sql`
            UPDATE "courseList" SET 
            ${Object.entries(updates).map(([key, value]) => sql`"${sql.identifier(key)}" = ${value}`).join(sql`, `)}
            WHERE "courseId" = ${courseId}
        `);
        return NextResponse.json({ success: true, message: 'Course updated' });
    } catch (error) {
        console.error('Failed to update course:', error);
        return NextResponse.json({ success: false, error: 'Failed to update course' }, { status: 500 });
    }
}

// From: app\api\admin\courses\route.js (simplified DELETE handler)
export async function DELETE(request) {
    try {
        const { courseId } = await request.json();
        // IMPORTANT: Delete related data first due to database relationships
        await db.execute(sql`DELETE FROM "chapters" WHERE "courseId" = ${courseId}`); // Deletes all chapters
        await db.execute(sql`DELETE FROM "chapter_surveys" WHERE "courseId" = ${courseId}`); // Deletes related survey data
        await db.execute(sql`DELETE FROM "courseList" WHERE "courseId" = ${courseId}`); // Finally delete the course
        return NextResponse.json({ success: true, message: 'Course and related data deleted' });
    } catch (error) {
        console.error('Failed to delete course:', error);
        return NextResponse.json({ success: false, error: 'Failed to delete course' }, { status: 500 });
    }
}
```

**Explanation**:
- The `GET` request retrieves all courses from the `courseList` table, along with essential details for the admin view.
- The `PUT` request updates a course. It dynamically constructs the `UPDATE` SQL query based on the `updates` object provided by the frontend. This allows admins to change properties like `name`, `category`, `level`, or `publish` status.
- The `DELETE` request is critical. To maintain database integrity (due to "foreign key constraints" mentioned in [Chapter 2: Course & Chapter Management](02_course___chapter_management_.md)), it first deletes all associated `chapters` and `chapter_surveys` *before* deleting the main course entry from `courseList`.

**5. Database Backup (`app\api\admin\backup\route.js` - POST)**

This endpoint creates a snapshot of critical database tables and saves them to a file.

```javascript
// From: app\api\admin\backup\route.js (simplified POST handler)
import { NextResponse } from 'next/server';
import { db } from '@/configs/db.jsx';
import { CourseList, Chapters, ChapterSurveys } from '@/configs/schema';
import fs from 'fs/promises'; // Node.js file system module
import path from 'path';     // Node.js path module

export async function POST(request) {
    try {
        // Fetch all data from selected tables
        const courses = await db.select().from(CourseList);
        const chapters = await db.select().from(Chapters);
        const surveys = await db.select().from(ChapterSurveys);

        const backupData = {
            timestamp: new Date().toISOString(),
            courses,
            chapters,
            surveys
        };

        const backupDir = path.join(process.cwd(), 'backups'); // Folder to save backups
        await fs.mkdir(backupDir, { recursive: true }); // Create folder if it doesn't exist

        const fileName = `backup_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
        const filePath = path.join(backupDir, fileName);
        
        await fs.writeFile(filePath, JSON.stringify(backupData, null, 2)); // Save as pretty JSON

        // After saving, list all backup files (for frontend display)
        const backups = await fs.readdir(backupDir);
        const backupFiles = backups.filter(file => file.endsWith('.json'))
            .map(file => ({ name: file, timestamp: file.split('backup_')[1].split('.json')[0].replace(/-/g, ':') }))
            .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        return NextResponse.json({ success: true, message: 'Backup created successfully', backupFiles });
    } catch (error) {
        console.error('Backup failed:', error);
        return NextResponse.json({ error: 'Failed to create backup: ' + error.message }, { status: 500 });
    }
}
```

**Explanation**: This `POST` endpoint first fetches all data records from the `CourseList`, `Chapters`, and `ChapterSurveys` tables in our database. It then bundles this data into a JSON object, adds a timestamp, and saves it as a `.json` file in a dedicated `backups` folder on the server's file system (`process.cwd()` refers to the current working directory of the application). Finally, it reads and returns a list of all backup files, sorted by date, to the frontend.

**6. Database Restore (`app\api\admin\restore\route.js` - POST)**

This is the most sensitive operation, overwriting the current database with data from a selected backup.

```javascript
// From: app\api\admin\restore\route.js (simplified POST handler)
import { NextResponse } from 'next/server';
import { db } from '@/configs/db.jsx';
import { CourseList, Chapters, ChapterSurveys } from '@/configs/schema';
import fs from 'fs/promises';
import path from 'path';

export async function POST(request) {
    try {
        const { backupFile } = await request.json(); // Get the name of the backup file to use
        if (!backupFile) {
            return NextResponse.json({ error: 'Backup file name is required' }, { status: 400 });
        }

        const backupDir = path.join(process.cwd(), 'backups');
        const filePath = path.join(backupDir, backupFile);
        
        const backupContent = await fs.readFile(filePath, 'utf-8'); // Read the backup file
        const backupData = JSON.parse(backupContent); // Parse the JSON data

        // Use a database transaction for safe operations: either all succeed or all fail
        await db.transaction(async (tx) => {
            // 1. Clear existing data (in reverse order of foreign key dependencies)
            await tx.delete(ChapterSurveys);
            await tx.delete(Chapters);
            await tx.delete(CourseList);

            // 2. Restore data from backup (in order of foreign key dependencies)
            if (backupData.courses?.length > 0) {
                await tx.insert(CourseList).values(backupData.courses);
            }
            if (backupData.chapters?.length > 0) {
                await tx.insert(Chapters).values(backupData.chapters);
            }
            if (backupData.surveys?.length > 0) {
                await tx.insert(ChapterSurveys).values(backupData.surveys);
            }
        });

        return NextResponse.json({ success: true, message: 'Database restored successfully' });
    } catch (error) {
        console.error('Restore failed:', error);
        return NextResponse.json({ error: 'Failed to restore database: ' + error.message }, { status: 500 });
    }
}
```

**Explanation**: This `POST` endpoint is extremely powerful. It first reads the specified JSON `backupFile` from the server's file system. Then, it initiates a **database transaction**. Inside this transaction, it performs two critical steps:
1.  **Clear Data**: It deletes *all* existing records from the `ChapterSurveys`, `Chapters`, and `CourseList` tables. The order is important here (deleting child records before parent records to avoid foreign key errors).
2.  **Restore Data**: After clearing, it inserts all the data from the `backupData` (read from the JSON file) back into the respective tables. The insertion order is also crucial (inserting parent records before child records).

If any step within this transaction fails, the entire transaction is rolled back, meaning the database reverts to its state *before* the restore attempt, preventing partial or corrupted data. This makes the restore process atomic and safer.

### Conclusion

In this final chapter, we've explored the critical role of the **Admin Panel & Database Management** within the `ai-course` platform. We learned how it serves as the central command center, providing administrators with dashboards to monitor platform health, tools to manage users and courses, and, most importantly, robust functionalities for database backup and restoration. This comprehensive control system ensures the platform remains stable, secure, and ready to serve its users efficiently.

This concludes our tutorial journey through the core abstractions of the `ai-course` project. You now have a foundational understanding of how a complex AI-powered educational platform is built, from user authentication to content generation, collaboration, interactive learning, and essential administration.

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)