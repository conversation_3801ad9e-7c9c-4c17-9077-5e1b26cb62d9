# Chapter 2: User & Authentication System

Welcome back to the `mytask` journey! In [Chapter 1: User Interface Components & Pages](01_user_interface_components___pages_.md), we learned how `mytask` builds all the visual parts you interact with, like buttons and task lists. But what good is a beautiful application if it doesn't know who you are, or if it can't keep your personal tasks private? This is where the "User & Authentication System" comes in.

### The Problem: Knowing Who You Are & Keeping Things Secure

Imagine `mytask` is a private club. When you walk in, the club needs to know:
1.  **Who are you?** (Are you a registered member?)
2.  **Are you really you?** (Can you prove your identity, like showing an ID card?)
3.  **What are you allowed to do here?** (Are you just a regular guest, or a VIP, or even a manager who can see everything?)

Without a system to handle these questions, anyone could access anyone else's tasks, or even delete important information! This is the core problem that the "User & Authentication System" solves. It's like the bouncer and ID checker at our `mytask` private club.

### Solution: Users, Authentication, and Authorization

`mytask` uses a smart system involving three main ideas to keep things secure and organized:

1.  **User:** This is simply *you*! It's your account in `mytask`, containing your name, email, and special roles (like "Admin" or "Team Member").
2.  **Authentication:** This is the process of *proving* you are who you say you are. Usually, this means providing a username (or email) and a secret password. `mytask` also has a special way to authenticate using your Atlas University account, which we'll touch on here and dive deeper into in the next chapter.
3.  **Authorization:** Once you're authenticated (your identity is confirmed), authorization determines *what you're allowed to do*. For example, only an "Admin" might be able to add new team members or delete other users' accounts.

### How You Log In (The Central Use Case)

Let's walk through how you, as a user, log into `mytask`. This is the most common way you interact with the authentication system.

#### Scenario: Logging in with your `mytask` account

Most users will log in directly using an email and password they set up with `mytask`.

1.  **You go to the `Login` page:** You see a login form with spaces for your email and password (these are `Textbox` components from [Chapter 1: User Interface Components & Pages](01_user_interface_components___pages_.md)).
2.  **You enter your details:** You type your email and password.
3.  **You click "Log In":** You click a `Button` component.

Here's what `mytask` does under the hood to handle your login:

```mermaid
sequenceDiagram
    participant You
    participant Browser
    participant MyTaskFrontend as MyTask App (Your Computer)
    participant MyTaskBackend as MyTask Server
    participant Database

    You->>Browser: Go to Login Page
    Browser->>MyTaskFrontend: Loads Login Page (using UI Components)
    You->>MyTaskFrontend: Enter Email & Password
    MyTaskFrontend->>MyTaskBackend: Send Login Request (Email, Password)
    MyTaskBackend->>Database: Find User by Email
    Database-->>MyTaskBackend: User Found (Hashed Password)
    MyTaskBackend->>MyTaskBackend: Compare Passwords (Your Input vs. Hashed)
    alt Passwords Match
        MyTaskBackend->>MyTaskBackend: Create "ID Card" (JWT Token)
        MyTaskBackend-->>MyTaskFrontend: Send "ID Card" (Token) + User Info
        MyTaskFrontend->>Browser: Store "ID Card" (Cookie/Local Storage)
        MyTaskFrontend->>You: Show Dashboard (Logged In!)
    else Passwords Don't Match
        MyTaskBackend-->>MyTaskFrontend: Send Error Message
        MyTaskFrontend->>You: Show "Invalid Password" Error
    end
```

### Diving Deeper: How `mytask` Handles Login (Code Walkthrough)

Let's look at the pieces of code that make this happen. Don't worry about understanding every single line; we'll focus on the main ideas.

#### 1. On Your Computer (Client-Side)

When you type your email and password, the `mytask` application running in your browser does a few things:

**a. How `mytask` Talks to the Server for Authentication**

This small part of the code defines how the `mytask` app sends login requests to the server. It's like preparing a message to send to the security guard.

```javascript
// client\src\redux\slices\api\authApiSlice.js
import { apiSlice } from "../apiSlice";

export const authApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    login: builder.mutation({ // This prepares a "login" request
      query: (data) => ({
        url: `/api/users/login`, // Where to send the request on the server
        method: "POST",          // It's a "POST" request (sending data)
        body: data,              // The data (email, password)
      }),
    }),
    // ... other authentication actions like register, logout
  }),
});

export const { useLoginMutation } = authApiSlice;
```
**Explanation:** This snippet shows that `mytask` has a special "login" function that knows how to send your email and password securely to the `/api/users/login` address on the `mytask` server.

**b. How `mytask` Stores Your Login Status**

Once the server says "you're logged in!", the `mytask` app needs to remember that. It stores your user information (like your name and email) in a special place, often in your browser's local storage so it remembers you even if you close and reopen the tab.

```javascript
// client\src\redux\slices\authSlice.js
import { createSlice } from "@reduxjs/toolkit";

const authSlice = createSlice({
  name: "auth",
  initialState: {
    user: localStorage.getItem("userInfo") // Try to load user from last session
      ? JSON.parse(localStorage.getItem("userInfo")) : null,
    // ... other initial state related to Atlas auth
  },
  reducers: {
    setCredentials: (state, action) => {
      state.user = action.payload; // Store user data (name, email, etc.)
      localStorage.setItem("userInfo", JSON.stringify(action.payload)); // Save to browser storage
    },
    logout: (state) => {
      state.user = null; // Clear user data
      localStorage.removeItem("userInfo"); // Remove from browser storage
      // ... clear Atlas auth data too
    },
    // ... other actions
  },
});

export const { setCredentials, logout } = authSlice.actions;
export default authSlice.reducer;
```
**Explanation:** This code shows how `mytask` remembers if you're logged in. When you successfully log in, `setCredentials` stores your user data. When you log out, the `logout` function clears this data, essentially forgetting who you are. This ensures your tasks remain private and you need to log in again.

#### 2. On the Server (Backend)

When the `mytask` server receives your login request, it performs the security checks.

**a. The Login Process on the Server**

This is the "bouncer" at the private club. It receives your email and password, checks them against its records, and if they match, gives you a special "ID card" (a JWT token) to prove you're authenticated for future visits.

```javascript
// server\controllers\userController.js
import asyncHandler from "express-async-handler";
import User from "../models/userModel.js";
import createJWT from "../utils/index.js";

const loginUser = asyncHandler(async (req, res) => {
  // ... (Code for Atlas login detection is skipped here; see Chapter 3)

  const { email, password } = req.body; // Get email and password from your request

  let user = await User.findOne({ email }); // Find user in the database by email

  if (!user) { // If user not found
    return res.status(401).json({ message: "Invalid email or password." });
  }

  // Check if the provided password matches the stored password
  const isMatch = await user.matchPassword(password);

  if (isMatch) { // If passwords match
    createJWT(res, user._id); // Give them an "ID card" (JWT token)
    user.password = undefined; // Don't send password back (security!)
    return res.status(200).json(user); // Send user data back
  } else { // If passwords don't match
    return res.status(401).json({ message: "Invalid email or password" });
  }
});

// ... other user-related functions
export { loginUser };
```
**Explanation:** The `loginUser` function first looks for your email in the database. If it finds you, it then uses a special `matchPassword` function (explained next) to verify your password. If everything checks out, it creates a `JWT` (JSON Web Token), which is like your temporary ID card for `mytask`, and sends it back to your browser.

**b. The User Blueprint and Password Checking**

This defines what a "User" looks like in the `mytask` database and how passwords are securely stored and checked.

```javascript
// server\models\userModel.js
import bcrypt from "bcryptjs"; // For securely hashing passwords
import mongoose, { Schema } from "mongoose";

const userSchema = new Schema(
  {
    name: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    password: { type: String, required: false }, // Hashed password
    isAdmin: { type: Boolean, default: false },
    isExternal: { type: Boolean, default: true }, // Is this user from Atlas?
    atlasUserId: { type: String, default: null, unique: true }, // Atlas ID
    lastLogin: { type: Date, default: Date.now },
  },
  { timestamps: true } // Adds 'createdAt' and 'updatedAt' fields
);

// Before saving a user, hash their password (if it's new or changed)
userSchema.pre("save", async function (next) {
  if (!this.isModified("password")) { // Only hash if password changed
    return next();
  }
  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt); // Hash the password
});

// Method to compare entered password with the stored hashed password
userSchema.methods.matchPassword = async function (enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

const User = mongoose.model("User", userSchema);
export default User;
```
**Explanation:** The `userSchema` defines what information `mytask` stores about each user. Notice that the `password` field is for the *hashed* password, not the raw one. The `pre("save", ...)` part is a special instruction that tells `mytask` to *always* scramble (hash) your password before saving it to the database, so even if someone got into the database, they wouldn't see your real password. The `matchPassword` method is then used to securely compare the password you type with the scrambled one in the database.

**c. Protecting Routes (Checking Your ID Card)**

Once you're logged in, `mytask` needs to make sure only *you* can see *your* tasks. This is where the `protectRoute` middleware comes in. It's like the bouncer checking your ID card at every door in the club.

```javascript
// server\middleware\authMiddleware.js
import asyncHandler from "express-async-handler";
import jwt from "jsonwebtoken"; // To verify the "ID card" (token)
import User from "../models/userModel.js";

const protectRoute = asyncHandler(async (req, res, next) => {
  let token = req.cookies.token; // Look for the "ID card" in cookies

  if (token) {
    try {
      // Verify the "ID card" is real and not tampered with
      const decodedToken = jwt.verify(token, process.env.JWT_SECRET);

      // Find the user associated with this "ID card"
      req.user = await User.findById(decodedToken.userId).select("-password");
      
      // Allow the request to proceed to the actual task logic
      next();
    } catch (error) {
      console.error('Authentication failed:', error.message);
      return res.status(401).json({ message: "Not authorized. Login again." });
    }
  } else {
    return res.status(401).json({ message: "Not authorized. Login required." });
  }
});

// ... other middleware
export { protectRoute };
```
**Explanation:** Every time you try to access a protected part of `mytask` (like your task list), the `protectRoute` function runs first. It checks for your `token` (the ID card) in your browser's cookies. If it finds one, it verifies that it's legitimate. If it is, `mytask` knows who you are (`req.user`) and lets you access the page. If not, it sends you back to the login screen.

**d. Linking Paths to Functions**

Finally, here's how `mytask` connects the web addresses (like `/api/users/login`) to the actual functions that handle them.

```javascript
// server\routes\userRoute.js
import express from "express";
import {
  loginUser,
  logoutUser,
  registerUser,
  getTeamList,
} from "../controllers/userController.js";
import { isAdminRoute, protectRoute } from "../middleware/authMiddleware.js";

const router = express.Router();

router.post("/register", registerUser); // When someone POSTs to /register, call registerUser
router.post("/login", loginUser);       // When someone POSTs to /login, call loginUser
router.post("/logout", logoutUser);     // When someone POSTs to /logout, call logoutUser

router.get("/get-team", protectRoute, getTeamList); // To get team list, must be protected!
// ... other routes

export default router;
```
**Explanation:** This code maps URLs to the server-side functions. For instance, when `mytask` receives a "POST" request to `/api/users/login`, it knows to run the `loginUser` function we just discussed. Notice how `protectRoute` is used for `/get-team` – this means you *must* be logged in (have a valid ID card) to see the team list!

### User Roles: Admin vs. Member

`mytask` also has different levels of access, like "Admin" and "Team Member".

*   **Admin:** Can manage all users, create new ones, assign roles, and handle global settings.
*   **Team Member:** Can create and manage their own tasks, and potentially tasks assigned to them, but cannot manage other users.

This is handled by the `isAdmin` field in the `User` model and the `isAdminRoute` middleware, which checks if the logged-in user has `isAdmin: true`.

### Conclusion

In this chapter, we unpacked the "User & Authentication System" of `mytask`. We learned how it's crucial for identifying users, verifying their identity (authentication), and controlling what they can do (authorization). We walked through the process of logging in with a `mytask` account, seeing how your browser and the server work together, and how your "ID card" (JWT token) keeps your information secure.

But wait, there's more! `mytask` also integrates with Atlas University's system, allowing students and staff to log in using their university credentials. How does that work? That's what we'll explore in the next exciting chapter: [Atlas University Integration](03_atlas_university_integration_.md).

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)