# Chapter 1: User & Authentication System

Welcome to `atlas-message`! Imagine you're trying to enter a super-exclusive club. You can't just walk in, right? You need to show your ID at the door, maybe a special invite, and once inside, your pass might determine which areas you can access.

In the world of web applications, it's very similar! We need a way to know **who** is using our application, make sure they are truly who they say they are, and then decide **what** they are allowed to do. This is precisely the job of the **User & Authentication System**.

This system is like the bouncer and ID checker at our digital private event. It handles:

*   **Signing Up:** When someone wants to join `atlas-message` for the first time, they create a new account.
*   **Logging In:** When an existing user wants to enter the application, they prove their identity (like showing their ID).
*   **Knowing Who You Are (Sessions):** Once you're in, the system remembers you so you don't have to show your ID every single time you click something.
*   **Controlling Access (Roles):** Not everyone has the same permissions. Some users might be regular members, while others might be 'admins' who can manage the club.

Let's explore how `atlas-message` uses this system to manage its users.

## Your Digital ID: The User Account

Think of your "user account" as your digital identity within `atlas-message`. It stores important information about you.

Here's a simplified look at what information `atlas-message` typically stores for each user:

```typescript
// models/User.ts (simplified)
export interface IUser extends Document {
  name: string;      // Your name
  email: string;     // Your unique email address (like your ID number)
  password?: string; // Your secret password (optional if you sign in with Google)
  image?: string;    // Your profile picture
  role?: 'user' | 'admin'; // What kind of access you have
  accountStatus?: 'active' | 'inactive'; // Is your account currently usable?
  createdAt: Date;   // When you joined
}

const UserSchema = new Schema<IUser>(
  {
    name: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    password: { type: String, required: false },
    role: { type: String, enum: ['user', 'admin'], default: 'user' },
    accountStatus: { type: String, enum: ['active', 'inactive', 'suspended'], default: 'active' },
  },
  { timestamps: true } // Automatically adds createdAt and updatedAt
);

export const User = mongoose.models.User || mongoose.model<IUser>('User', UserSchema);
```

This code snippet from `models\User.ts` shows the blueprint for a user's data. It defines fields like `name`, `email`, `password`, and `role`. The `role` field is especially important, as it determines what you're allowed to do in the application.

## Proving Who You Are: Signup and Login

The core of the User & Authentication System is handling new sign-ups and existing user logins.

### Use Case: A New User Signs Up

Let's say a new user, Alice, wants to join `atlas-message`. She will go to a signup page, fill in her name, email, and choose a password.

When Alice clicks "Sign Up," the application needs to:
1.  Receive her information.
2.  Make sure her email isn't already used.
3.  Securely save her password (not directly, but in a scrambled form).
4.  Create her new user account in the database.

Here's how `atlas-message` handles the signup process behind the scenes, specifically in its API route for new users:

```typescript
// app/api/auth/signup/route.ts (simplified)
import { connectToDatabase } from '@/lib/mongodb';
import { User } from '@/models/User';
import bcrypt from 'bcryptjs'; // Used for securely scrambling passwords

export async function POST(request: NextRequest) {
  const { name, email, password } = await request.json();

  // 1. Check if required information is provided
  if (!name || !email || !password) {
    return NextResponse.json({ error: 'Missing information' }, { status: 400 });
  }

  await connectToDatabase(); // Connect to our database

  // 2. Check if user with this email already exists
  const existingUser = await User.findOne({ email: email.toLowerCase() });
  if (existingUser) {
    return NextResponse.json({ error: 'User already exists' }, { status: 409 });
  }

  // 3. Securely hash the password (scramble it)
  const hashedPassword = await bcrypt.hash(password, 12);

  // 4. Create the new user account in the database
  const user = await User.create({
    name: name.trim(),
    email: email.toLowerCase().trim(),
    password: hashedPassword, // Store the scrambled password
    role: 'user', // Default role for new users
    accountStatus: 'active',
  });

  // Send back a success message (without the password)
  const { password: _, ...userWithoutPassword } = user.toObject();
  return NextResponse.json({ message: 'User created!', user: userWithoutPassword }, { status: 201 });
}
```

This code snippet from `app\api\auth\signup\route.ts` shows the essential steps: it gets the user's details, checks for existing users, *hashes* (scrambles) the password for security, and then saves the new user record into the database. Hashing is super important because it means we never store your actual password, just a scrambled version that can be used to verify future logins.

### Use Case: An Existing User Logs In

Once Alice has an account, she needs to log in to use `atlas-message`. She'll go to the sign-in page, enter her email and password, and click "Sign In."

From Alice's perspective, this looks like this on the screen:

```tsx
// app/auth/signin/page.tsx (simplified)
'use client'
import { signIn, getSession } from 'next-auth/react' // Key functions for login
import { useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'

export default function SignInPage() {
  const [formData, setFormData] = useState({ email: '', password: '' });
  const router = useRouter();

  useEffect(() => {
    // Check if user is already logged in, if so, redirect to home
    const checkSession = async () => {
      const session = await getSession();
      if (session) {
        router.push('/');
      }
    };
    checkSession();
  }, [router]);

  const handleCredentialsSignIn = async (e: React.FormEvent) => {
    e.preventDefault(); // Stop the form from doing its default submit

    // Call NextAuth's signIn function
    const result = await signIn('credentials', {
      email: formData.email,
      password: formData.password,
      redirect: false, // We handle redirection manually
    });

    if (result?.error) {
      // Show an error if login failed
      console.error('Login error:', result.error);
    } else if (result?.ok) {
      // If successful, redirect to the main page
      router.push('/');
    }
  };

  return (
    <form onSubmit={handleCredentialsSignIn}>
      <input type="email" name="email" value={formData.email} onChange={handleChange} placeholder="Enter your email" required />
      <input type="password" name="password" value={formData.password} onChange={handleChange} placeholder="Enter your password" required />
      <button type="submit">Sign In</button>
    </form>
  );
}
```

This snippet from `app\auth\signin\page.tsx` shows how the `signIn` function from `next-auth/react` is used. When the user submits the form, `signIn('credentials', { email, password })` is called. This function tells `NextAuth` (our authentication library) to try to log the user in using the provided email and password.

## How Login Works: Under the Hood

When you click "Sign In," a series of steps happen behind the scenes to verify your identity. Here's a simplified sequence:

```mermaid
sequenceDiagram
    participant User
    participant Frontend as Your Browser (Next.js App)
    participant NextAuth as Authentication Helper (Next.js API)
    participant BackendAPI as Our Server Code (API Routes)
    participant Database as MongoDB

    User->>Frontend: Enters Email & Password, Clicks Sign In
    Frontend->>NextAuth: Calls `signIn('credentials', { email, password })`
    NextAuth->>BackendAPI: Sends login request to our credentials API route
    BackendAPI->>Database: Finds user by email
    Database-->>BackendAPI: Returns user data (including hashed password)
    BackendAPI->>BackendAPI: Compares provided password with stored hashed password
    BackendAPI-->>NextAuth: Success/Failure
    alt Success
        NextAuth->>Frontend: Creates and sends session (you're logged in!)
        Frontend->>User: Redirects to Main Application Page
    else Failure
        NextAuth->>Frontend: Sends error message
        Frontend->>User: Displays "Invalid Credentials" message
    end
```

### Deeper Dive into Code: The Main Gatekeeper

The `atlas-message` project uses a powerful library called `NextAuth.js` to handle all the complex parts of authentication. It acts as the central gatekeeper.

```typescript
// app/api/auth/[...nextauth]/route.ts (simplified)
import NextAuth from 'next-auth'
import { authOptions } from '@/lib/auth' // Our specific rules for authentication

const handler = NextAuth(authOptions) // NextAuth uses our rules

export { handler as GET, handler as POST } // Allows NextAuth to handle requests
```

This file (`app\api\auth\[...nextauth]\route.ts`) is where `NextAuth.js` is set up. It imports `authOptions` which define *how* users can log in (e.g., using email/password or Google). `NextAuth` then uses these options to process all authentication requests.

When `signIn('credentials', ...)` is called from the frontend, `NextAuth` uses the rules defined in `authOptions` to check the provided credentials against the user data in the database. If they match, `NextAuth` creates a "session" – a secure way for the application to remember that you're logged in without asking for your password repeatedly.

### Checking Who You Are: Admin Permissions

Once a user is authenticated (logged in), the system knows their identity and their `role`. This allows `atlas-message` to decide what features they can access. For example, 'admin' users have special privileges, like managing other user accounts.

You can see role checks in our API routes for managing users:

```typescript
// app/api/admin/users/route.ts (simplified)
import { getServerSession } from 'next-auth' // To get current user's session
import { authOptions } from '@/lib/auth'
import { isAdmin } from '@/lib/workspace-config' // A helper to check admin role

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions) // Get current user's session

  // Check if the user is logged in AND has the 'admin' role
  if (!session?.user?.email || !isAdmin(session.user.email)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  // If they are an admin, proceed to fetch all users from the database
  // ... (code to fetch and process user data)
}

// app/api/admin/users/[userId]/route.ts (simplified PATCH/DELETE)
// Similar check for updating/deleting specific users
export async function PATCH(request: NextRequest, { params }: { params: { userId: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.email || !isAdmin(session.user.email)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  // ... (code to update a user's details)
}
```

These snippets from `app\api\admin\users\route.ts` and `app\api\admin\users\[userId]\route.ts` show how the `getServerSession` function is used to get information about the currently logged-in user. Then, the `isAdmin` helper function checks their role. If they are not an administrator, the request is stopped with an "Unauthorized" error, preventing them from accessing sensitive admin features.

## Conclusion

The User & Authentication System is the bedrock of `atlas-message`. It ensures that only legitimate users can access the application, verifies their identity, and controls what they can do based on their assigned roles. It's the essential first step in building a secure and personalized messaging experience.

Now that we understand how users are managed and authenticated, let's move on to how these users organize themselves and collaborate within the application. Next, we'll dive into the concept of Workspaces!

[Next Chapter: Workspaces (Organizational Units)](02_workspaces__organizational_units__.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)