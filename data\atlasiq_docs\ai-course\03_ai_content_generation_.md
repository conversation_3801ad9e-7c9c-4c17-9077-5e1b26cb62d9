# Chapter 3: AI Content Generation

Welcome back! In [Chapter 2: Course & Chapter Management](02_course___chapter_management_.md), we learned how to structure our educational content, creating "placeholder" courses and chapters in our digital library. We set up the shelves and labeled the books. But what about the actual content *inside* those books? That's where **AI Content Generation** comes in – it's the intelligent engine that fills those empty chapters with rich, engaging learning materials!

### The Big Problem: Filling the Empty Pages

Imagine you've outlined a fantastic new course, say, "Introduction to Web Development," with chapters like "HTML Basics," "CSS Styling," and "JavaScript Fundamentals." Writing all the detailed lessons, creating quiz questions, and finding relevant images for each of these chapters can take *hundreds* of hours. It's a huge creative and writing task!

This is the problem **AI Content Generation** solves. Our `ai-course` platform has a powerful intelligent assistant ready to do this heavy lifting for you. You give it an outline (which we created in the previous chapter), and it drafts detailed chapter content, writes quiz questions, and even generates unique images, turning your empty placeholders into a complete learning experience. It's like having a team of specialized content creators and artists who instantly produce learning materials based on your instructions.

Let's focus on a core use case: **generating detailed content for a course's chapters, including text, quizzes, and images.**

Our goal for this chapter is to understand how, with just a few clicks, the system leverages AI to transform a simple chapter title into a full-fledged lesson.

### Key Concepts: Your AI Toolkit

To understand how AI content generation works, let's look at its main components:

1.  **AI Models (The Smart Workers):** These are like different specialized tools or "brains" that can understand your request and generate text or images. Our platform can use various models from different providers like Google's Gemini, OpenAI's GPT models (like DALL-E for images), Groq, and Anthropic's Claude. Each has its strengths!
    *   **Analogy:** Think of them as different expert chefs in a kitchen. One might be great at writing detailed recipes, another at creating beautiful food art, and another at quickly listing ingredients.

2.  **Prompts (Your Instructions):** A prompt is simply the instruction or question you give to the AI model. It's how you tell the "smart worker" what you want it to do. The better your prompt, the better the AI's output.
    *   **Analogy:** This is your "recipe" for the chef. You tell them exactly what kind of dish you want, the ingredients to use, and how it should look.

3.  **Content Pipeline (The Assembly Line):** This is the sequence of steps our platform takes to use the AI models, from receiving your request to saving the generated content. It orchestrates the process.

### Solving the Use Case: Generating Course Content

Recall from [Chapter 2: Course & Chapter Management](02_course___chapter_management_.md) that when you create a new course, the system initially generates just the *outline* (chapter names and basic descriptions). Now, we want to fill those chapters with actual lessons.

#### How the Frontend Triggers Generation (Simplified)

After creating a course outline, you're typically taken to a course detail page (e.g., `app\create-course\[courseId]\page.jsx` or `app\course\[courseId]\page.jsx`). Here, you'll see a button like "Generate Course Content" or "Generate Chapter Content."

When you click this button, the frontend (your browser) identifies which chapter (or chapters) needs content. It then sends a request to our backend server, providing the chapter's topic, the course context, and details about what kind of content (text, quiz, image) is needed.

```javascript
// From: app\course\[courseId]\start\page.jsx (simplified for demonstration)
import { useState, useEffect } from 'react';

function ChapterContentPage({ chapterId, courseId }) {
  const [chapter, setChapter] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Fetch initial chapter content from DB (might be empty initially)
    const fetchChapter = async () => { /* ... fetch from /api/chapters ... */ };
    fetchChapter();
  }, [chapterId, courseId]);

  const handleGenerateContent = async () => {
    setLoading(true);
    try {
      // Get the chapter's basic details (name, description)
      const chapterTopic = chapter?.name || "Unspecified Topic";
      const chapterDescription = chapter?.about || "No specific description available.";

      // 1. Call AI for text content
      console.log('Generating text content...');
      const textResponse = await fetch('/api/ai', { // <-- IMPORTANT: Call our general AI API!
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: `Write a detailed lesson about "${chapterTopic}". Include definitions, examples, and explanations.`,
          model: 'gemini-1.5-pro', // Example model
          apiType: 'gemini',
          maxTokens: 4000
        })
      });
      const textData = await textResponse.json();
      const generatedText = textData.response; // The AI's generated lesson!

      // 2. Call AI for quiz questions
      console.log('Generating quiz questions...');
      const quizResponse = await fetch('/api/generate-quiz', { // <-- Dedicated quiz API!
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sectionContent: generatedText,
          sectionTitle: chapterTopic,
          language: 'en'
        })
      });
      const quizData = await quizResponse.json();
      const generatedQuiz = quizData.questions; // The AI's generated quiz!

      // 3. Call AI for an image
      console.log('Generating image...');
      const imageResponse = await fetch('/api/generate-image', { // <-- Dedicated image API!
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: `An illustrative image for "${chapterTopic}"`,
          courseId,
          chapterId: parseInt(chapterId),
          sectionIndex: 0, // For image organization
          provider: 'openai' // Example provider
        })
      });
      const imageData = await imageResponse.json();
      const generatedImage = imageData.imageUrl; // The URL of the AI-generated image!

      // 4. Update the chapter in the database (via backend API)
      console.log('Updating chapter in database...');
      await fetch(`/api/chapters/${chapterId}/content?courseId=${courseId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: {
            title: chapterTopic,
            description: generatedText, // Store the generated text here
            quiz: generatedQuiz,       // Store the generated quiz here
            image: generatedImage      // Store the image URL here
          }
        })
      });

      console.log('Chapter content updated successfully!');
      // Refresh the chapter data to show new content
      // await fetchChapter();
    } catch (error) {
      console.error('Error generating chapter content:', error);
      alert('Failed to generate content: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {/* ... Display chapter name and initial content ... */}
      <button onClick={handleGenerateContent} disabled={loading}>
        {loading ? 'Generating...' : 'Generate Content'}
      </button>
      {/* ... Display generated content, quiz, image */}
    </div>
  );
}
```

**Explanation**: When the "Generate Content" button is clicked, our frontend makes *multiple* requests to different backend APIs:
1.  It calls the main `/api/ai` endpoint to get the detailed *text content* for the chapter.
2.  It then calls `/api/generate-quiz` to get *quiz questions* based on that generated text.
3.  Next, it calls `/api/generate-image` to create a visual for the chapter.
4.  Finally, it sends all this new content (text, quiz, image URL) to our `/api/chapters/[chapterId]/content` endpoint (which we saw in [Chapter 2: Course & Chapter Management](02_course___chapter_management_.md)) to save it into the database.

**Input**: The chapter's topic and a trigger to generate content.
**Output**: The chapter's `content` field in the database is filled with detailed lesson text, quiz questions, and a URL to a generated image. The frontend then displays this new rich content.

### Under the Hood: How AI Content Generation Works (Internal Implementation)

Let's look at the server-side magic that makes this happen.

#### High-Level Walkthrough

Here’s a simplified step-by-step process of how our server uses AI to generate content:

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant ContentAPI as Server (Backend API)
    participant AIProvider as AI Model (e.g., Gemini, OpenAI)
    participant FileSystem as Server (Image Storage)
    participant Database

    User->>Frontend: Clicks "Generate Content" for Chapter X
    Frontend->>ContentAPI: Request text content (POST /api/ai)
    ContentAPI->>AIProvider: Sends prompt (e.g., "Write about HTML")
    AIProvider-->>ContentAPI: Returns generated text
    ContentAPI->>Frontend: Sends text
    
    Frontend->>ContentAPI: Request quiz questions (POST /api/generate-quiz)
    ContentAPI->>AIProvider: Sends prompt with text (e.g., "Make quiz from this text...")
    AIProvider-->>ContentAPI: Returns generated quiz (JSON)
    ContentAPI->>Frontend: Sends quiz
    
    Frontend->>ContentAPI: Request image (POST /api/generate-image)
    ContentAPI->>AIProvider: Sends prompt (e.g., "Draw web dev tools")
    AIProvider-->>ContentAPI: Returns image data
    ContentAPI->>FileSystem: Saves image file
    ContentAPI->>Frontend: Sends image URL
    
    Frontend->>ContentAPI: Update Chapter in DB (PUT /api/chapters/[id]/content)
    ContentAPI->>Database: Saves text, quiz, image URL
    Database-->>ContentAPI: Confirmation
    ContentAPI-->>Frontend: Success
    Frontend->>User: Displays full chapter content
```

**Explanation**:
1.  **User Request**: You tell the **Frontend** to generate content for a chapter.
2.  **Backend Orchestration**: The **Frontend** makes a series of requests to our **Backend API** for different types of content (text, quiz, image).
3.  **AI Interaction**: For each request, the **Backend API** prepares a "prompt" and sends it to the chosen **AI Model** (e.g., Gemini for text, DALL-E for images).
4.  **Content Creation**: The **AI Model** processes the prompt and sends back the generated text, quiz data, or image data.
5.  **Storage**: For images, the **Backend API** saves the image file to the server's local storage (the `public` folder) and gets a URL.
6.  **Database Update**: Finally, the **Backend API** takes all the generated pieces and updates the relevant chapter record in the **Database** via the [Course & Chapter Management](02_course___chapter_management_.md) system.
7.  **Display**: The **Frontend** receives the success message and refreshes the page to show you the newly generated, rich chapter content.

#### Code Deep Dive: The Server's AI Brain

Our `ai-course` project has a central API route for general AI interactions, and specialized routes for specific tasks like quiz or image generation.

**1. The Central AI API (`app\api\ai\route.js` - POST)**

This is the main entry point for sending a prompt to an AI model and getting a text response. It supports different AI providers.

```javascript
// From: app\api\ai\route.js (simplified POST handler)
import OpenAI from 'openai'; // For OpenAI models
import { GoogleGenerativeAI } from '@google/generative-ai'; // For Gemini
import Groq from 'groq-sdk'; // For Groq
import axios from 'axios'; // For Anthropic (uses direct HTTP request)
import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    const { prompt, model, apiType, temperature, maxTokens, apiKey: clientApiKey } = await request.json();
    
    // Check for required inputs
    if (!prompt || !apiType) {
      return NextResponse.json({ error: 'Prompt and API type are required' }, { status: 400 });
    }
    
    let responseText;
    switch (apiType) { // Determine which AI model to use
      case 'openai':
        responseText = await handleOpenAIRequest(prompt, model, temperature, maxTokens, clientApiKey);
        break;
      case 'gemini':
        responseText = await handleGeminiRequest(prompt, model, temperature, maxTokens, clientApiKey);
        break;
      case 'groq':
        responseText = await handleGroqRequest(prompt, model, temperature, maxTokens, clientApiKey);
        break;
      case 'anthropic':
        responseText = await handleAnthropicRequest(prompt, model, temperature, maxTokens, clientApiKey);
        break;
      default:
        // For other types, return a mock response or error
        responseText = `This is a mock response from ${apiType} for: ${prompt}`;
        break;
    }
    
    return NextResponse.json({ response: responseText });
  } catch (error) {
    console.error('Error in AI API route:', error);
    // Return detailed error message
    return NextResponse.json({ error: error.message || 'An unknown error occurred' }, { status: 500 });
  }
}
```

**Explanation**: This main `POST` function receives your `prompt`, which `model` to use (e.g., `gpt-4o`, `gemini-1.5-pro`), and the `apiType` (e.g., `openai`, `gemini`). It then intelligently routes the request to a specific helper function (`handleOpenAIRequest`, `handleGeminiRequest`, etc.) based on the `apiType` to interact with the correct AI provider.

Let's look at one of these helper functions:

```javascript
// From: app\api\ai\route.js (simplified handleOpenAIRequest)
async function handleOpenAIRequest(prompt, model = 'gpt-3.5-turbo', temperature = 0.7, maxTokens = 4000, clientApiKey = null) {
  // Get the API key, either from client (user settings) or environment variables
  const apiKey = clientApiKey || process.env.NEXT_PUBLIC_OPENAI_API_KEY || process.env.OPENAI_API_KEY;
  if (!apiKey) {
    throw new Error('OpenAI API key is not configured. Please add your API key in Settings.');
  }
  
  try {
    const openai = new OpenAI({ apiKey }); // Initialize OpenAI client
    
    const response = await openai.chat.completions.create({ // Send the prompt!
      model,
      messages: [{ role: 'user', content: prompt }], // User's instruction
      temperature, // How creative/random the response should be
      max_tokens: maxTokens // Limit on response length
    });
    
    return response.choices[0].message.content; // Return the generated text
  } catch (error) {
    console.error('OpenAI API error:', error);
    throw new Error(`OpenAI API error: ${error.message}`);
  }
}
```

**Explanation**: This `handleOpenAIRequest` function takes the `prompt` and other settings. It first securely retrieves the OpenAI API key. Then, it uses the `OpenAI` library to make a call to the OpenAI service. The `messages` array contains the core instruction for the AI. Finally, it extracts and returns the generated text content from the AI's response. Similar `handle...Request` functions exist for Gemini, Groq, and Anthropic, each using their respective SDKs or direct API calls.

**2. Generating Quizzes (`app\api\generate-quiz\route.js` - POST)**

This specialized API endpoint takes a chunk of content and asks an AI model to generate multiple-choice quiz questions from it.

```javascript
// From: app\api\generate-quiz\route.js (simplified POST handler)
import { GoogleGenerativeAI } from "@google/generative-ai";
import OpenAI from 'openai';
import { NextResponse } from "next/server";

export async function POST(req) {
  try {
    const { sectionContent, sectionTitle, language = 'en' } = await req.json();
    
    if (!sectionContent || !sectionTitle) {
      return NextResponse.json({ questions: [], error: 'Missing content or title' }, { status: 400 });
    }
    
    let generatedQuestions = [];
    try {
      // Prioritize Gemini, then fallback to OpenAI if Gemini fails or key isn't set
      generatedQuestions = await generateQuestionsWithGemini(sectionContent, sectionTitle, language);
    } catch (geminiError) {
      console.warn('Gemini failed, trying OpenAI:', geminiError.message);
      if (process.env.OPENAI_API_KEY) { // Check if OpenAI key exists
        generatedQuestions = await generateQuestionsWithOpenAI(sectionContent, sectionTitle, language);
      } else {
        throw new Error('Gemini failed and OpenAI key not configured.');
      }
    }
    
    // Process and validate questions (e.g., ensure 4 options, valid format)
    const finalQuestions = processQuestions(generatedQuestions); // Helper function for validation
    
    return NextResponse.json({ questions: finalQuestions });
  } catch (error) {
    console.error('Error generating quiz:', error);
    return NextResponse.json({ questions: [], error: error.message }, { status: 500 });
  }
}

// From: app\api\generate-quiz\route.js (simplified generateQuestionsWithGemini)
async function generateQuestionsWithGemini(sectionContent, sectionTitle, language) {
  const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY;
  if (!apiKey) throw new Error('Gemini API key is not configured');
  
  const genAI = new GoogleGenerativeAI(apiKey);
  const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
  
  const languageInstruction = language === 'tr' ? "Generate ALL content in Turkish." : "Generate content in English.";
  
  const prompt = `Create exactly 5 multiple choice questions about "${sectionTitle}" based on this content:
${sectionContent}
${languageInstruction}
Format as JSON array: [{"question": "...", "options": ["...", "...", "...", "..."], "correctAnswer": "..."}]
Return ONLY the JSON array.`;
  
  const result = await model.generateContent(prompt);
  const text = result.response.text();
  
  // Try to extract JSON from the text, as AI sometimes adds extra commentary
  const jsonMatch = text.match(/\[\s*\{[\s\S]*\}\s*\]/);
  const jsonText = jsonMatch ? jsonMatch[0] : text;
  
  return JSON.parse(jsonText);
}
```

**Explanation**: This endpoint receives the chapter's `sectionContent` and `sectionTitle`. It constructs a very specific `prompt` asking the AI to generate 5 multiple-choice questions in a strict JSON format. It tries to use Gemini first, then falls back to OpenAI if needed. It then parses the AI's response, often needing to extract the pure JSON from surrounding text the AI might add. The `processQuestions` function ensures the output is valid (e.g., always 4 options).

**3. Generating Images (`app\api\generate-image\route.js` - POST)**

This API endpoint is responsible for taking a text description and asking an AI image model to create a visual. It also handles saving this image file to our server.

```javascript
// From: app\api\generate-image\route.js (simplified POST handler)
import { NextResponse } from 'next/server';
import { promises as fs } from 'fs'; // For file system operations
import path from 'path'; // For handling file paths
import OpenAI from 'openai'; // For DALL-E (OpenAI's image model)
import { GoogleGenerativeAI } from "@google/generative-ai"; // For Gemini (if it supports image generation)
import { ensureDirectoryExists, getImageUrl } from './utils'; // Helper functions

export async function POST(request) {
  try {
    const { prompt, courseId, chapterId, sectionIndex, provider = 'openai' } = await request.json();
    
    if (!prompt || !courseId || isNaN(parseInt(chapterId))) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    // Define where to save the image: public/uploads/courses/[courseId]/[chapterId]
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'courses', courseId, chapterId.toString());
    await ensureDirectoryExists(uploadDir); // Make sure the folder exists!
    
    const saveTimestamp = Date.now();
    let result = null;

    // Try generating with the chosen provider (e.g., OpenAI DALL-E)
    if (provider === 'openai') {
      result = await generateImageWithOpenAI(prompt, uploadDir, sectionIndex, saveTimestamp);
    } else if (provider === 'gemini') {
      result = await generateImageWithGemini(prompt, uploadDir, sectionIndex, saveTimestamp);
    }
    
    // If AI generation fails, fall back to a mock image
    if (!result) {
      result = await generateEnhancedMockImage(courseId, chapterId, sectionIndex, prompt, saveTimestamp);
    }
    
    const imageUrl = getImageUrl(courseId, chapterId.toString(), path.basename(result.fullPath));
    
    return NextResponse.json({ success: true, imageUrl, metadata: result });
  } catch (error) {
    console.error("Error in image generation:", error);
    return NextResponse.json({ success: false, error: error.message }, { status: 500 });
  }
}

// From: app\api\generate-image\route.js (simplified generateImageWithOpenAI)
async function generateImageWithOpenAI(prompt, uploadDir, sectionIndex, timestamp) {
  const openai = new OpenAI({ apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY });
  
  const response = await openai.images.generate({
    model: "dall-e-3", // The image generation model
    prompt: prompt,    // Your description for the image
    n: 1,              // Generate one image
    size: "1024x1024", // Image resolution
    response_format: "b64_json", // Get image as base64 data
  });
  
  const imageData = response.data[0].b64_json;
  const buffer = Buffer.from(imageData, 'base64'); // Convert base64 to image data
  
  const filename = `section_${sectionIndex || 0}_${timestamp}.png`;
  const fullPath = path.join(uploadDir, filename);
  await fs.writeFile(fullPath, buffer); // Save the image file to our server!
  
  return { fullPath, provider: 'openai', model: 'dall-e-3' };
}
```

**Explanation**: This endpoint receives a `prompt` (like "A colorful graph showing data trends") and identifiers (`courseId`, `chapterId`). It first creates the necessary folder structure in our `public` directory. Then, it uses the `OpenAI` client (specifically the DALL-E 3 model) to send the prompt and request an image. The AI returns the image data (in base64 format), which our server then converts into an image file (`.png`) and saves it to the designated folder. The path to this saved image is then returned, so the frontend can display it. If real AI image generation fails (e.g., no API key, API error), it smartly falls back to generating a visually pleasing placeholder SVG image.

**4. Saving Generated Content to the Database**

After all the AI models have done their work (generating text, quizzes, and images), the frontend makes a final call to update the `Chapters` table in the database. This uses an API endpoint we discussed in [Chapter 2: Course & Chapter Management](02_course___chapter_management_.md):

```javascript
// From: app\api\chapters\[chapterId]\content\route.js (simplified PUT handler - from Chapter 2)
import { NextResponse } from 'next/server';
import { db } from '@/configs/db';
import { Chapters } from '@/configs/schema';
import { eq, and } from 'drizzle-orm';

export async function PUT(request, { params, searchParams }) {
    try {
        const { chapterId } = params;
        const courseId = searchParams?.get('courseId');
        const updatedContent = await request.json(); // This contains the AI-generated text, quiz, image URL
        
        // Find the specific chapter in the database
        const existingChapter = await db.select().from(Chapters)
            .where(and(eq(Chapters.chapterId, parseInt(chapterId)), eq(Chapters.courseId, courseId)));

        if (existingChapter.length > 0) {
            await db.update(Chapters)
                .set({ 
                    content: updatedContent.content, // Save the new rich content (JSON object)
                    updated_at: new Date()
                })
                .where(and(eq(Chapters.chapterId, parseInt(chapterId)), eq(Chapters.courseId, courseId)));
        } else {
            // ... (handle chapter not found or create it) ...
        }
        return NextResponse.json({ success: true, message: 'Chapter content updated' });
    } catch (error) {
        console.error('Error updating chapter content:', error);
        return NextResponse.json({ error: 'Failed to update chapter content' }, { status: 500 });
    }
}
```

**Explanation**: This `PUT` request takes the `chapterId`, `courseId`, and the `updatedContent` (which is the JSON object containing the AI-generated lesson text, quiz, and image URL). It then updates the `content` column of the specific chapter entry in the `Chapters` table in our database. Now, your once-empty chapter is brimming with AI-powered learning material!

### Conclusion

In this chapter, we've explored the exciting world of **AI Content Generation** within the `ai-course` platform. We learned how AI models act as intelligent assistants, taking your prompts to generate detailed lesson text, engaging quiz questions, and unique images. We traced the path from a user's click on the frontend, through the backend's orchestration of multiple AI services (OpenAI, Gemini, etc.), to the final storage of rich content in our database. This powerful system transforms basic outlines into comprehensive, ready-to-learn courses.

Generating all this amazing content isn't free, though. These powerful AI models consume resources, and our platform tracks this usage.

**Next Chapter**: [Credit & Billing System](04_credit___billing_system_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)