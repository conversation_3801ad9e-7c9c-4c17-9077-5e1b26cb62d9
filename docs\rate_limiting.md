# Rate Limiting Özelliği

<PERSON>, Agentic RAG sisteminde bot-specific rate limiting özelliğinin nasıl çalıştığını ve nasıl yapılandırılacağını açıklar.

## Genel Bakış

Rate limiting özelliğ<PERSON>, her bot için ayrı ayrı yapılandırılabilen istek sınırlaması sağlar. Bu özellik:

- **Session-based**: Her session_id için ayrı rate limit uygulanır
- **Bot-specific**: Her bot için farklı limitler ayarlanabilir
- **Configurable**: Config dosyalarından kolayca ayarlanabilir
- **In-memory**: Basit ve hızlı in-memory implementasyon

## Yapılandırma

### Bot Config Dosyasında Rate Limiting

Her bot'un YAML config dosyasına `rate_limit` bölümü eklenmelidir:

```yaml
name: StudentBot
# ... diğer ayarlar ...
rate_limit:
  enabled: true                 # Rate limiting aktif/pasif
  requests_per_minute: 10       # Dakikada maksimum istek sayısı
  requests_per_hour: 100        # Saatte maksimum istek sayısı
```

### Örnek Yapılandırmalar

#### StudentBot (Öğrenci Botu)
```yaml
rate_limit:
  enabled: true
  requests_per_minute: 10
  requests_per_hour: 100
```

#### AtlasIQBot (Araştırma Botu)
```yaml
rate_limit:
  enabled: true
  requests_per_minute: 15
  requests_per_hour: 200
```

#### Rate Limiting'i Devre Dışı Bırakma
```yaml
rate_limit:
  enabled: false
  requests_per_minute: 10  # Bu değerler göz ardı edilir
  requests_per_hour: 100
```

## Teknik Detaylar

### Implementasyon

Rate limiting özelliği şu bileşenlerden oluşur:

1. **SimpleRateLimiter** (`app/core/rate_limiter.py`): Ana rate limiting mantığı
2. **BotConfig** (`app/models/bot_config.py`): Rate limit yapılandırma modeli
3. **Query Endpoint** (`app/main.py`): Rate limit kontrolü

### Sliding Window Algoritması

Sistem sliding window algoritması kullanır:

- Her session için istek zamanları deque'da saklanır
- Her yeni istek geldiğinde, eski istekler temizlenir
- Mevcut pencerede istek sayısı kontrol edilir
- Limit aşılırsa HTTP 429 hatası döndürülür

### Session Isolation

Her session_id için ayrı rate limit uygulanır:

```python
key = f"{bot_name}:{session_id or 'anonymous'}"
```

Bu sayede:
- Farklı kullanıcılar birbirini etkilemez
- Aynı kullanıcının farklı session'ları ayrı sayılır
- Anonymous istekler de desteklenir

## API Yanıtları

### Başarılı İstek
```json
{
  "response": "Bot yanıtı...",
  "metadata": {...}
}
```

### Rate Limit Aşıldığında
```json
{
  "detail": {
    "error": "Rate limit exceeded",
    "message": "Too many requests for StudentBot. Please wait 45 seconds before trying again.",
    "retry_after": 45,
    "limit": "10 requests per 60 seconds"
  }
}
```

HTTP Status Code: `429 Too Many Requests`

## Test Etme

### Manuel Test

Test script'i kullanarak rate limiting'i test edebilirsiniz:

```bash
python test_rate_limiting.py
```

Bu script:
- Farklı bot'lar için rate limiting test eder
- Tek session ve çoklu session senaryolarını test eder
- Detaylı sonuç raporu verir

### Curl ile Test

```bash
# İlk istek (başarılı olmalı)
curl -X POST "http://localhost:8000/bots/StudentBot/query" \
  -H "Content-Type: application/json" \
  -d '{"query": "Test", "session_id": "test123"}'

# Çok sayıda istek gönderip rate limit'i test etme
for i in {1..15}; do
  echo "Request $i:"
  curl -X POST "http://localhost:8000/bots/StudentBot/query" \
    -H "Content-Type: application/json" \
    -d '{"query": "Test '$i'", "session_id": "test123"}'
  echo -e "\n---"
done
```

## Monitoring ve Logging

Rate limiting olayları log'lanır:

```
INFO: Rate limit check passed for bot: StudentBot
WARNING: Rate limit exceeded for key: StudentBot:test123
DEBUG: Rate limiting disabled for bot: AdminBot
```

## Performans Notları

### Memory Kullanımı

- Her session için istek zamanları bellekte saklanır
- Eski kayıtlar otomatik olarak temizlenir
- Varsayılan olarak 1 saat sonra kayıtlar silinir

### Cleanup

Sistem otomatik olarak eski kayıtları temizler:

```python
# 1 saatlik kayıtları temizle
limiter.cleanup_old_entries(max_age_seconds=3600)
```

## Gelişmiş Yapılandırma

### Farklı Bot Türleri İçin Öneriler

| Bot Türü | requests_per_minute | requests_per_hour | Açıklama |
|----------|-------------------|------------------|----------|
| Student Bot | 10 | 100 | Öğrenci kullanımı için makul limit |
| Research Bot | 15 | 200 | Araştırma için biraz daha yüksek |
| Admin Bot | 50 | 1000 | Yönetici işlemleri için yüksek limit |
| Public Bot | 5 | 50 | Genel kullanım için düşük limit |

### Rate Limit Stratejileri

1. **Conservative**: Düşük limitler, sistem koruması öncelikli
2. **Balanced**: Orta limitler, kullanıcı deneyimi ve sistem koruması dengeli
3. **Liberal**: Yüksek limitler, kullanıcı deneyimi öncelikli

## Sorun Giderme

### Yaygın Sorunlar

1. **Rate limit çok düşük**: `requests_per_minute` değerini artırın
2. **Rate limit çalışmıyor**: `enabled: true` olduğundan emin olun
3. **Session isolation çalışmıyor**: `session_id` gönderildiğinden emin olun

### Debug Modu

Debug log'ları için log level'ı DEBUG'a ayarlayın:

```python
logger.setLevel(logging.DEBUG)
```

## Gelecek Geliştirmeler

- Redis backend desteği (distributed systems için)
- Dinamik rate limit ayarlama
- Rate limit metrics ve dashboard
- IP-based rate limiting
- Burst allowance özelliği
