# Chapter 5: Workspace Collaboration

Welcome back! In [Chapter 4: Credit & Billing System](04_credit___billing_system_.md), we learned how our `ai-course` platform manages the "fuel" (credits) needed to power our amazing AI content generation. Now that we know how individuals can create incredible courses, the next big question is: **How can a team work together on these courses?**

Imagine you're building a large educational program with colleagues. You need a way to share courses, assign different responsibilities, and keep track of everyone's contributions. This is exactly what **Workspace Collaboration** provides. It's like setting up a shared digital office or a "co-working space" specifically for your team's educational projects. In this space, you can collectively create, manage, and organize courses, just as if you were all working on the same whiteboard!

### The Big Problem: Working Together on Shared Content

If everyone creates courses only for themselves, it's hard to build a consistent and comprehensive learning experience as a team. How do you share courses you've created with others? How do you ensure only the right people can edit a course, while others can only view it? And how do you bring new team members into your shared projects? This is the central problem "Workspace Collaboration" solves.

Let's focus on a core use case: **Creating a new virtual workspace for your team and inviting members to join it.**

Our goal for this chapter is to understand how you can set up these shared environments, bring your team in, and start managing courses together.

### Key Concepts: Your Team's Digital Office

To understand Workspace Collaboration, let's break down its main components:

1.  **Workspaces (Your Digital Co-working Space)**: This is a virtual team room. It's a dedicated space where your team can gather all their shared courses and collaborate.
    *   **Analogy**: Think of it as a shared folder on a cloud drive, like Google Drive or Dropbox, where only your team has access to the files inside.

2.  **Members & Roles (Your Team Members & Their Hats)**: People are added to a workspace as "members." Each member can have a specific "role" (like `admin`, `instructor`, or `student`), which determines what they are allowed to do within that workspace (e.g., only admins can delete courses).
    *   **Analogy**: Your team members in the office. One might be the "manager" (admin), another an "instructor" (teacher), and others "students" (learners). Each has different responsibilities.

3.  **Courses in Workspaces (Shared Projects)**: Once a workspace is set up, courses can be added to it. These courses then become shared projects that all members of that workspace can access, based on their roles.
    *   **Analogy**: The project documents, presentations, and design files stored inside your shared digital folder.

4.  **Invitations (Sending an Access Key)**: To add new members to a workspace, you send them an invitation. They accept the invitation to gain access.
    *   **Analogy**: Sending someone a special email link that grants them access to your shared Google Drive folder.

### Solving the Use Case: Creating a New Workspace and Adding Members

Let's walk through the process of setting up a new team workspace and bringing your colleagues in.

#### How the Frontend Handles Workspace Creation (Simplified)

You'll typically find the workspace management area in your dashboard (e.g., `app\dashboard\workspaces\page.jsx`). Here, you'll see a "Create Workspace" button. Clicking this button opens a dialog where you can enter details for your new workspace.

```javascript
// From: app\dashboard\workspaces\page.jsx (simplified snippet)
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import CreateWorkspaceDialog from '../_components/CreateWorkspaceDialog'; // The dialog component

export default function WorkspacesPage() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false); // State to control dialog visibility
  // ... other state and imports ...

  const handleCreateWorkspace = async (workspaceData) => {
    try {
      const response = await fetch('/api/workspace', { // <-- IMPORTANT: Calls our workspace API!
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(workspaceData), // Send workspace name, responsible person etc.
      });

      const data = await response.json();
      if (response.ok) {
        console.log('Workspace created successfully:', data.workspace);
        // After creation, refresh the list of workspaces or add the new one
        // fetchWorkspaces(); // Re-fetch all workspaces to update the list
        setCreateDialogOpen(false); // Close the dialog
        return { success: true, workspace: data.workspace };
      } else {
        console.error('Failed to create workspace:', data.error);
        return { success: false, error: data.error };
      }
    } catch (error) {
      console.error('Error creating workspace:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  };

  return (
    <div>
      {/* ... JSX for page title and existing workspaces list ... */}
      <Button onClick={() => setCreateDialogOpen(true)}>
        Create Workspace
      </Button>

      <CreateWorkspaceDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSubmit={handleCreateWorkspace} // Pass the handler to the dialog
        // ... other props like user data ...
      />
    </div>
  );
}
```

**Explanation**: When you click "Create Workspace" and fill out the `CreateWorkspaceDialog`, the `handleCreateWorkspace` function is triggered. This function sends a `POST` request to the `/api/workspace` endpoint on our server. The `workspaceData` in the request body contains details like the workspace name, responsible person's name, etc. If successful, the dialog closes, and the list of workspaces refreshes.

**Input**: A new workspace's name, responsible person, and contact email.
**Output**: A new workspace is created and appears in your list.

#### How to View Workspaces and Their Contents (Frontend Perspective)

After creation, the `app\dashboard\workspaces\page.jsx` fetches and displays all the workspaces you're part of. You can click on a workspace card to navigate to its details page (`app\dashboard\workspaces\[workspaceCode]\page.jsx`), which shows members and courses.

```javascript
// From: app\dashboard\workspaces\page.jsx (simplified fetchWorkspaces)
import { useState, useEffect } from 'react';

export default function WorkspacesPage() {
  const [workspaces, setWorkspaces] = useState([]);
  const [loading, setLoading] = useState(true);
  // ... other state and imports ...

  useEffect(() => {
    // Only fetch if user email is available (meaning user is logged in)
    if (userEmail) { // userEmail would be set after fetching current user info
      fetchWorkspaces();
    }
  }, [userEmail]); // Re-run when userEmail changes

  const fetchWorkspaces = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/workspace', { // <-- Fetches your workspaces!
        credentials: 'include', // Ensures our login cookie is sent
      });
      const data = await response.json();
      
      if (response.ok) {
        setWorkspaces(data.workspaces || []); // Update the state with fetched workspaces
      } else {
        console.error('Failed to fetch workspaces:', data.error);
      }
    } catch (error) {
      console.error('Error fetching workspaces:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {/* ... JSX rendering the list of workspaces using WorkspaceCard component ... */}
      {loading ? (
        // Show loading skeletons
        <div>Loading workspaces...</div>
      ) : workspaces.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {workspaces.map((workspace) => (
            <WorkspaceCard 
              key={workspace.workspace_code} 
              workspace={workspace} 
              isOwner={workspace.isOwner} // Flag to show if current user is owner
              onClick={() => router.push(`/dashboard/workspaces/${workspace.workspace_code}`)}
            />
          ))}
        </div>
      ) : (
        // No workspaces found message
        <div>No workspaces yet. Create one!</div>
      )}
    </div>
  );
}
```

**Explanation**: When the `WorkspacesPage` loads, `fetchWorkspaces` makes a `GET` request to `/api/workspace`. The server responds with a list of all workspaces the current user is part of (either as an owner or a member). The frontend then displays these as `WorkspaceCard` components. Clicking a card navigates to `app\dashboard\workspaces\[workspaceCode]\page.jsx`, which then fetches members and courses for that specific workspace.

**Input**: The user's authenticated session (via cookie).
**Output**: A list of workspaces the user belongs to, displayed on the dashboard.

#### Adding Members to a Workspace (Frontend Perspective)

Once inside a specific workspace's detail page (`app\dashboard\workspaces\[workspaceCode]\page.jsx`), there's typically a section or button for "Members." From there, an administrator can add or invite new users.

```javascript
// From: app\dashboard\_components\WorkspaceMembers.jsx (simplified)
import { useState } from 'react';
import { Button } from '@/components/ui/button';

function WorkspaceMembers({ workspaceCode, users, isAdmin }) {
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState('student'); // Default role
  const [processing, setProcessing] = useState(false);

  const handleAddMember = async () => {
    setProcessing(true);
    try {
      const response = await fetch('/api/workspace/users', { // <-- Calls the workspace users API!
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          workspace_code: workspaceCode, 
          username: inviteEmail, // We're using username to store email for direct add
          email: inviteEmail, // For future invitation system, if not direct add
          role: inviteRole,
          action: 'add' // Directly add the user to the workspace
        }),
      });
      const data = await response.json();
      if (response.ok) {
        alert(`User ${inviteEmail} added successfully!`);
        // Refresh member list (in a real app, this would trigger a re-fetch)
      } else {
        alert(data.error || 'Failed to add member');
      }
    } catch (error) {
      console.error('Error adding member:', error);
      alert('An unexpected error occurred.');
    } finally {
      setProcessing(false);
    }
  };

  return (
    <div>
      {/* ... Display current members ... */}
      {isAdmin && (
        <div className="mt-6 p-4 border rounded-lg">
          <h3 className="font-semibold mb-2">Add New Member</h3>
          <input 
            type="email" 
            placeholder="Member Email" 
            value={inviteEmail} 
            onChange={(e) => setInviteEmail(e.target.value)} 
            className="border p-2 rounded mr-2"
          />
          <select 
            value={inviteRole} 
            onChange={(e) => setInviteRole(e.target.value)} 
            className="border p-2 rounded mr-2"
          >
            <option value="admin">Admin</option>
            <option value="instructor">Instructor</option>
            <option value="student">Student</option>
          </select>
          <Button onClick={handleAddMember} disabled={processing}>
            {processing ? 'Adding...' : 'Add Member'}
          </Button>
        </div>
      )}
    </div>
  );
}
```

**Explanation**: If the current user is an admin of the workspace, they can see an "Add New Member" section. When they enter an email and select a role, the `handleAddMember` function sends a `POST` request to `/api/workspace/users`. The `action: 'add'` in the body tells the backend to directly add the user to the `WorkspaceUsers` table.

**Input**: Email and desired role for the new member.
**Output**: The new member is added to the workspace's member list (or an error if the user is already a member).

### Under the Hood: How Workspace Collaboration Works (Internal Implementation)

Let's peek behind the curtain to see how our backend handles these multi-user interactions.

#### High-Level Walkthrough: Workspaces, Members, and Courses

```mermaid
sequenceDiagram
    participant User
    participant Frontend as Website (Your Browser)
    participant WorkspaceAPI as Server (Workspace API)
    participant Database

    User->>Frontend: Clicks "Create Workspace"
    Frontend->>WorkspaceAPI: POST /api/workspace (Workspace Name, etc.)
    WorkspaceAPI->>Database: Insert new Workspace entry
    Database-->>WorkspaceAPI: Workspace Created
    WorkspaceAPI->>Database: Insert creator into WorkspaceUsers
    Database-->>WorkspaceAPI: Creator Added
    WorkspaceAPI-->>Frontend: Success + New Workspace Code
    Frontend->>User: Shows new workspace

    User->>Frontend: Views Workspaces
    Frontend->>WorkspaceAPI: GET /api/workspace
    WorkspaceAPI->>Database: Fetch Workspaces (owner or member)
    Database-->>WorkspaceAPI: Workspaces List
    WorkspaceAPI-->>Frontend: Workspaces Data
    Frontend->>User: Displays Workspaces

    User->>Frontend: Adds Member to Workspace
    Frontend->>WorkspaceAPI: POST /api/workspace/users (Workspace Code, Member Email, Role, Action:'add')
    WorkspaceAPI->>Database: Insert Member into WorkspaceUsers
    Database-->>WorkspaceAPI: Member Added
    WorkspaceAPI-->>Frontend: Success
    Frontend->>User: Member now in workspace

    User->>Frontend: Adds Course to Workspace
    Frontend->>WorkspaceAPI: POST /api/workspace/courses (Workspace Code, Course ID)
    WorkspaceAPI->>Database: Insert Course into WorkspaceCourses
    Database-->>WorkspaceAPI: Course Linked
    WorkspaceAPI-->>Frontend: Success
    Frontend->>User: Course linked to workspace
```

**Explanation**:
1.  **Workspace Creation**: When you create a workspace, your **Frontend** sends the details to the **WorkspaceAPI**. The API creates an entry in the `Workspace` table and then immediately adds you (the creator) to the `WorkspaceUsers` table for that workspace, setting you as an owner.
2.  **Viewing Workspaces**: When you load the workspaces page, the **Frontend** asks the **WorkspaceAPI** for a list. The API queries the `Workspace` and `WorkspaceUsers` tables in the **Database** to find all workspaces you own or are a member of.
3.  **Adding Members**: When you add a member, the **Frontend** sends the details to the **WorkspaceAPI**. The API adds the new member's email (or username, in our simplified schema) and their role to the `WorkspaceUsers` table.
4.  **Adding Courses**: Courses are linked to workspaces by adding an entry in the `WorkspaceCourses` table, connecting a specific `course_id` to a `workspace_code`.

#### Code Deep Dive: The Server's Collaboration Engine

Our backend provides several API routes to manage workspaces, members, and courses.

**1. Creating a Workspace (`app\api\workspace\route.js` - POST)**

This API is responsible for creating the main workspace entry and immediately adding the creator as a member.

```javascript
// From: app\api\workspace\route.js (simplified POST handler)
import { db } from '@/configs/db';
import { Workspace, WorkspaceUsers } from '@/configs/schema';
import { NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth'; // For user authentication
import { cookies } from 'next/headers'; // To access the JWT token

// Helper to generate a unique code for the workspace
function generateWorkspaceCode(name) { /* ... implementation ... */ return "WS-" + Math.random().toString(36).substring(2, 6).toUpperCase(); }

export async function POST(request) {
  try {
    const token = cookies().get('atlas_token')?.value; // Get user's login token
    const user = await verifyJWT(token); // Verify who the user is (see Chapter 1)
    if (!user || !user.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const userEmail = user.email;

    const { workspace_name, responsible_name, email, web_address, user_email } = await request.json();
    if (!workspace_name) {
      return NextResponse.json({ error: 'Workspace name is required' }, { status: 400 });
    }

    const workspace_code = generateWorkspaceCode(workspace_name); // Create unique code
    const effectiveEmail = user_email || email || userEmail; // Determine creator's email

    // Insert new workspace into 'Workspace' table
    const newWorkspace = await db.insert(Workspace).values({
      workspace_code,
      workspace_name,
      email: effectiveEmail, // Store creator's email (also acts as owner)
      responsible_name: responsible_name || '',
      web_address: web_address || '',
      created_by: effectiveEmail,
    }).returning(); // Return the newly created workspace

    // Add the creator as the first member (admin role is implied by being the owner)
    await db.insert(WorkspaceUsers).values({
      workspace_code,
      username: effectiveEmail, // Link to user's email/username
    });
    
    return NextResponse.json({ workspace: newWorkspace[0] }, { status: 201 });
  } catch (error) {
    console.error('Error in workspace creation:', error);
    return NextResponse.json({ error: 'Failed to create workspace' }, { status: 500 });
  }
}
```

**Explanation**: This `POST` endpoint first authenticates the user using their JWT token (from [Chapter 1: User Authentication & Authorization](01_user_authentication___authorization_.md)). It then generates a unique `workspace_code` and inserts the new workspace's details into the `Workspace` table. Crucially, it then adds the creating user to the `WorkspaceUsers` table, linking them to this new workspace.

**2. Fetching Workspaces (`app\api\workspace\route.js` - GET)**

This API allows a user to see all the workspaces they are involved with.

```javascript
// From: app\api\workspace\route.js (simplified GET handler)
import { db } from '@/configs/db';
import { Workspace, WorkspaceUsers } from '@/configs/schema';
import { eq, inArray, sql } from 'drizzle-orm';
import { NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { cookies } from 'next/headers';

export async function GET() {
  try {
    const token = cookies().get('atlas_token')?.value;
    const user = await verifyJWT(token);
    if (!user || !user.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const userEmail = user.email;

    let workspaces = [];
    
    // 1. Fetch workspaces where the user is the owner (created_by or email field)
    const ownerWorkspaces = await db.select().from(Workspace)
      .where(eq(Workspace.created_by, userEmail)); // Or eq(Workspace.email, userEmail)
    
    // 2. Fetch workspace codes where the user is listed in WorkspaceUsers
    const memberWorkspaceCodesResult = await db.execute(
      sql`SELECT workspace_code FROM workspace_users WHERE username = ${userEmail}`
    );
    const memberCodes = memberWorkspaceCodesResult.map(row => row.workspace_code);
    
    let memberWorkspaces = [];
    if (memberCodes.length > 0) {
      // 3. Fetch full workspace details for those member codes
      memberWorkspaces = await db.select().from(Workspace)
        .where(inArray(Workspace.workspace_code, memberCodes));
    }
    
    // Combine and remove duplicates (a user can be both owner and member)
    const allWorkspaces = [...ownerWorkspaces, ...memberWorkspaces];
    workspaces = Array.from(new Map(allWorkspaces.map(ws => [ws.workspace_code, ws])).values());

    // Add an 'isOwner' flag for frontend display
    const workspacesWithOwnership = workspaces.map(workspace => ({
      ...workspace,
      isOwner: workspace.created_by === userEmail || workspace.email === userEmail // Check ownership
    }));

    return NextResponse.json({ workspaces: workspacesWithOwnership });
  } catch (error) {
    console.error('Error fetching workspaces:', error);
    return NextResponse.json({ error: 'Failed to fetch workspaces' }, { status: 500 });
  }
}
```

**Explanation**: This `GET` endpoint first authenticates the user. Then, it queries the `Workspace` table to find workspaces where the current user is listed as the `created_by` (owner). It also queries the `WorkspaceUsers` table to find all `workspace_code`s where the user is a member. Finally, it combines these lists, removes duplicates, and returns a comprehensive list of workspaces the user has access to.

**3. Adding/Inviting Users to a Workspace (`app\api\workspace\users\route.js` - POST)**

This API allows an admin to add new members. The provided code has a simplified direct "add" feature.

```javascript
// From: app\api\workspace\users\route.js (simplified POST handler)
import { db } from '@/configs/db';
import { Workspace, WorkspaceUsers } from '@/configs/schema';
import { eq, and } from 'drizzle-orm';
import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    // In a full implementation, you'd verify the user adding the member is an admin.
    // For now, we assume authentication passed.

    const { workspace_code, email, role, action, username } = await request.json();

    if (!workspace_code || (!email && !username)) { // Require email or username
      return NextResponse.json({ error: 'Workspace code and user identifier are required' }, { status: 400 });
    }

    // Check if the workspace exists (omitted for brevity, handled in actual code)

    // Using 'username' field for the user's identifier (email) in our simplified schema
    const userIdentifier = email || username; 

    // Check if the user is already in the workspace
    const existingUser = await db.select().from(WorkspaceUsers).where(
      and(
        eq(WorkspaceUsers.workspace_code, workspace_code),
        eq(WorkspaceUsers.username, userIdentifier)
      )
    );

    if (existingUser.length > 0) {
      return NextResponse.json({ error: 'User is already a member of this workspace' }, { status: 400 });
    }

    // Add the user directly to the workspace
    await db.insert(WorkspaceUsers).values({
      workspace_code,
      username: userIdentifier, // Add user identifier to WorkspaceUsers
      // In a more complex schema, you'd also store the 'role' here
    });

    return NextResponse.json({ success: true, message: 'User added to workspace' }, { status: 201 });
  } catch (error) {
    console.error('Error managing workspace user:', error);
    return NextResponse.json({ error: 'Failed to manage workspace user' }, { status: 500 });
  }
}
```

**Explanation**: This `POST` endpoint takes a `workspace_code` and the `email` (or `username` as it's used in the `WorkspaceUsers` table) of the person to add. It first checks if the user is already a member to prevent duplicates. If not, it inserts a new record into the `WorkspaceUsers` table, effectively making that person a member of the specified workspace. The code also briefly touches on the concept of an `action` for `invite` vs. `add`, where `invite` would involve generating a token and sending an email (mocked in the provided code).

**4. Adding Courses to a Workspace (`app\api\workspace\courses\route.js` - POST)**

This API links an existing course to a workspace, making it accessible to all workspace members.

```javascript
// From: app\api\workspace\courses\route.js (simplified POST handler)
import { db } from '@/configs/db';
import { WorkspaceCourses } from '@/configs/schema';
import { eq, and } from 'drizzle-orm';
import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    // In a real app, you'd check if the user has permission to add courses to this workspace.

    const { workspace_code, course_id } = await request.json();

    if (!workspace_code || !course_id) {
      return NextResponse.json({ error: 'Workspace code and course ID are required' }, { status: 400 });
    }

    // Check if the course is already in the workspace
    const existingEntry = await db.select().from(WorkspaceCourses).where(
      and(
        eq(WorkspaceCourses.workspace_code, workspace_code),
        eq(WorkspaceCourses.course_id, course_id)
      )
    );

    if (existingEntry.length > 0) {
      return NextResponse.json({ error: 'Course is already in the workspace' }, { status: 409 });
    }

    // Add the course to the workspace by creating a link record
    await db.insert(WorkspaceCourses).values({
      workspace_code,
      course_id,
    });

    return NextResponse.json({ message: 'Course added to workspace successfully' }, { status: 201 });
  } catch (error) {
    console.error('Error adding course to workspace:', error);
    return NextResponse.json({ error: 'Failed to add course to workspace' }, { status: 500 });
  }
}
```

**Explanation**: This `POST` endpoint takes a `workspace_code` and a `course_id`. It creates a new entry in the `WorkspaceCourses` table, which acts as a bridge, linking that specific course to the given workspace. This way, the course becomes part of the shared team environment. There's also a corresponding `DELETE` endpoint to remove a course from a workspace.

**5. Deleting a Workspace (`app\api\workspace\route.js` - DELETE)**

Deleting a workspace is a powerful operation that removes the workspace and all its associations.

```javascript
// From: app\api\workspace\route.js (simplified DELETE handler)
import { db } from '@/configs/db';
import { Workspace, WorkspaceUsers, WorkspaceCourses } from '@/configs/schema';
import { eq, count, sql } from 'drizzle-orm';
import { NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { cookies } from 'next/headers';

export async function DELETE(request) {
  try {
    const token = cookies().get('atlas_token')?.value;
    const user = await verifyJWT(token);
    if (!user || !user.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const userEmail = user.email;

    const { workspace_code } = await request.json();
    if (!workspace_code) {
      return NextResponse.json({ error: 'Workspace code is required' }, { status: 400 });
    }

    // Check if user is the owner
    const workspace = await db.select().from(Workspace).where(eq(Workspace.workspace_code, workspace_code));
    if (workspace.length === 0 || workspace[0].created_by !== userEmail) {
      return NextResponse.json({ error: 'Only the workspace owner can delete it.' }, { status: 403 });
    }

    // IMPORTANT: Check if the workspace is empty of other members and courses
    const usersCount = await db.select({ count: count() }).from(WorkspaceUsers).where(eq(WorkspaceUsers.workspace_code, workspace_code));
    const coursesCount = await db.select({ count: count() }).from(WorkspaceCourses).where(eq(WorkspaceCourses.workspace_code, workspace_code));

    // Only allow deletion if no other users (count > 1 implies owner + others) and no courses
    if (usersCount[0]?.count > 1 || coursesCount[0]?.count > 0) {
      return NextResponse.json({ error: 'Cannot delete workspace. Remove all users (except yourself) and courses first.' }, { status: 400 });
    }

    // Delete associated data first (crucial for database integrity)
    await db.delete(WorkspaceUsers).where(eq(WorkspaceUsers.workspace_code, workspace_code));
    await db.delete(WorkspaceCourses).where(eq(WorkspaceCourses.workspace_code, workspace_code));

    // Finally, delete the workspace itself
    await db.delete(Workspace).where(eq(Workspace.workspace_code, workspace_code));

    return NextResponse.json({ message: 'Workspace deleted successfully' }, { status: 200 });
  } catch (error) {
    console.error('Error deleting workspace:', error);
    return NextResponse.json({ error: 'Failed to delete workspace' }, { status: 500 });
  }
}
```

**Explanation**: This `DELETE` endpoint authenticates the user and ensures only the owner can delete a workspace. Before deletion, it performs vital checks: it verifies that the workspace contains no other members (besides the owner) and no linked courses. If the workspace is not "empty" (as defined), it prevents deletion. If it is empty, it first deletes all related entries in `WorkspaceUsers` and `WorkspaceCourses` (to maintain database consistency), and *then* deletes the main workspace entry from the `Workspace` table.

### Conclusion

In this chapter, we've explored **Workspace Collaboration**, understanding how it enables teams to work together efficiently on educational content. We learned that workspaces act as shared digital environments where members can collaborate on courses, with roles defining their permissions. We walked through the process of creating a new workspace, adding members, and managing courses within these team rooms. This feature is crucial for building and scaling collaborative learning initiatives within organizations.

Now that we understand how teams can manage courses, let's look at how we can make learning itself more engaging!

**Next Chapter**: [Interactive Learning Tools](06_interactive_learning_tools_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)