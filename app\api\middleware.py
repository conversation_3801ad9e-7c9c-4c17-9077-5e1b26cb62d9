"""
Middleware for the Agentic RAG API.
"""

import uuid
from datetime import datetime

from fastapi import Request
from app.core.logging_config import get_api_logger
from app.core.logging_helpers import get_simple_logger_instance

api_logger = get_api_logger()
simple_logger = get_simple_logger_instance()


async def log_requests(request: Request, call_next):
    """Log all HTTP requests."""
    request_id = str(uuid.uuid4())[:8]
    start_time = datetime.now()

    # Log request
    api_logger.info(
        f"[{request_id}] {request.method} {request.url.path}",
        extra={
            "request_id": request_id,
            "method": request.method,
            "path": request.url.path,
            "query_params": str(request.query_params),
            "client_ip": request.client.host if request.client else "unknown",
        },
    )

    # Process request
    response = await call_next(request)

    # Calculate duration
    duration = (datetime.now() - start_time).total_seconds()

    # Log response
    api_logger.info(
        f"[{request_id}] Response: {response.status_code} ({duration:.3f}s)",
        extra={
            "request_id": request_id,
            "status_code": response.status_code,
            "duration": duration,
        },
    )

    # Simple log
    simple_logger.api_request(
        request.method, request.url.path, response.status_code, duration
    )

    return response
