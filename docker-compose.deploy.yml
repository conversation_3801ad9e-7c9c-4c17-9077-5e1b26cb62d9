# docker-compose.deploy.yml
# This file is intended for production deployment.
# It pulls pre-built images from a container registry (e.g., AWS ECR).
version: '3.8'

services:
  # Agentic RAG Backend Service
  agentic-rag:
    # IMPORTANT: This will be replaced by an environment variable from the .env file.
    # <PERSON> is responsible for setting the IMAGE_URI in the .env file on the server.
    image: ${IMAGE_URI}
    user: root
    container_name: agentic-rag-backend
    restart: unless-stopped
    ports:
      - "3820:8000"
    env_file:
      - .env
    environment:
      # Most variables are in .env. Override or set specific ones if needed.
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=false # Should be false in production
      - LOG_LEVEL=INFO
      - LOG_DIR=logs
    volumes:
      # Mount persistent data volumes from the host
      - ./data:/app/data
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
