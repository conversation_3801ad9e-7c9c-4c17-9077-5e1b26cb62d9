# Chapter 5: Internationalization (I18n)

Welcome back to the `myform` tutorial! In [Chapter 4: Form Response & Analytics](04_form_response___analytics_.md), we learned how `myform` collects and helps you understand the data from your forms. Now, imagine `myform` is used by people from all over the world. A user in Turkey might prefer to see everything in Turkish, while someone in the UK wants English. How do we make `myform` welcoming and easy to use for everyone, no matter what language they speak?

This is where **Internationalization (I18n)** comes in!

## What is Internationalization (I18n)?

Internationalization, often shortened to **I18n** (because there are 18 letters between the 'I' and the 'n'), is the process of designing and developing an application so that it can be easily adapted to different languages and regions without requiring changes to the core code.

Think of I18n in `myform` like a **dynamic phrasebook** for the entire application:

*   Instead of hardcoding (fixing) text directly into the buttons, labels, and messages, `myform` looks up the correct phrase in a special "phrasebook."
*   This phrasebook contains all the text needed for the app, translated into different languages.
*   When you change the language setting, `myform` simply switches to a different version of the phrasebook.

This means all the displayed text, from the "Sign In" button to detailed descriptions, automatically changes based on the user's selected language. For `myform`, we primarily support **English (en)** and **Turkish (tr)**.

## Our Use Case: Switching Myform's Language

Let's walk through the most common scenario: you want to change the language of your `myform` application from English to Turkish.

### How You Use It (The User Experience)

You'll typically find a "Language Switcher" button in the navigation bar of `myform`. It usually looks like a small globe icon.

Here's a simplified look at the component responsible for this:

```tsx
// components/language/LanguageSwitcher.tsx (Simplified)
'use client';
import { useState } from 'react';
import { useLanguage } from '@/contexts/LanguageContext'; // Our special language manager
import { GlobeAltIcon } from '@heroicons/react/24/outline';

export default function LanguageSwitcher() {
  const { language, changeLanguage, t } = useLanguage(); // Get current language, changer, and translations
  const [isOpen, setIsOpen] = useState(false);

  const handleLanguageChange = (lang: 'en' | 'tr') => {
    changeLanguage(lang); // Tell the language manager to change the language
    setIsOpen(false); // Close the dropdown
  };

  return (
    <div className="relative">
      <button onClick={() => setIsOpen(!isOpen)}>
        <GlobeAltIcon className="h-5 w-5" />
        <span className="hidden sm:inline">{t.language[language]}</span> {/* Shows "English" or "Türkçe" */}
      </button>
      {isOpen && (
        <div>
          <button onClick={() => handleLanguageChange('en')}>
            {t.language.en} {/* Button for English */}
          </button>
          <button onClick={() => handleLanguageChange('tr')}>
            {t.language.tr} {/* Button for Turkish */}
          </button>
        </div>
      )}
    </div>
  );
}
```

**Explanation:**

1.  **The `GlobeAltIcon` Button:** This is the button you click to open the language options.
2.  **`useLanguage()` Hook:** This is a special tool from our `LanguageContext` (which we'll explore soon) that gives us:
    *   `language`: The current active language (e.g., 'en' or 'tr').
    *   `changeLanguage`: A function to switch the language.
    *   `t`: This is our "phrasebook" object, containing all the translated texts.
3.  **Language Display:** The `<span>{t.language[language]}</span>` part dynamically shows "English" or "Türkçe" next to the globe icon, depending on the current `language` setting.
4.  **Changing Language:** When you click either the "English" or "Turkish" button inside the dropdown, `handleLanguageChange` is called. It then tells our language manager (`changeLanguage`) to switch to the selected language.

Once you click a new language, you'll see all the text on the page instantly change! For example, a "Sign In" button will become "Giriş Yap", and "My Forms" will become "Formlarım".

## Under the Hood: How Internationalization Works

Let's peek behind the scenes to see how `myform` manages all these different languages.

### The Journey: Changing Language Step-by-Step

Here's what happens when you click to change the language:

```mermaid
sequenceDiagram
    participant User as You (Browser)
    participant LanguageSwitcher as Language Switcher Component
    participant LanguageContext as Language Manager
    participant LocalStorage as Browser Storage
    participant LocalesFiles as Phrasebook Files (en.ts, tr.ts)

    User->>LanguageSwitcher: Clicks "Turkish" button
    LanguageSwitcher->>LanguageContext: Calls changeLanguage('tr')
    LanguageContext->>LanguageContext: Updates internal 'language' state to 'tr'
    LanguageContext->>LocalesFiles: Loads 'tr' (Turkish) translations from locales/tr.ts
    LocalesFiles-->>LanguageContext: Provides Turkish translation object
    LanguageContext->>LanguageContext: Updates internal 'translations' state with Turkish object
    LanguageContext->>LocalStorage: Saves 'language: tr'
    LanguageContext->>User: Notifies all components to re-render (implicitly via React context)
    LanguageSwitcher->>User: Updates its own display (e.g., shows "Türkçe")
    OtherComponents->>User: Update their text using new 't' object (e.g., "Giriş Yap")
```

**Explanation of the Flow:**
1.  **You** click on a language option (e.g., "Turkish") in the `LanguageSwitcher`.
2.  The `LanguageSwitcher` tells the `LanguageContext` (our "Language Manager") to `changeLanguage` to 'tr'.
3.  The `LanguageContext` updates its internal record of the current language.
4.  It then goes to the `LocalesFiles` (our "Phrasebook Files") and picks up all the Turkish translations.
5.  It updates its internal "phrasebook" with these new Turkish translations.
6.  It also saves your language preference (`'tr'`) into the `LocalStorage` in your browser, so `myform` remembers your choice even if you close and reopen the browser.
7.  Because the `LanguageContext` is connected to all parts of the app, any component that uses `useLanguage()` automatically gets the new Turkish translations and updates its displayed text.

### The Phrasebooks: `locales/en.ts` and `locales/tr.ts`

These files are the actual "phrasebooks" that contain all the text for each language. They are structured as JavaScript objects where each piece of text has a unique "key" (like a label), and the "value" is the actual text in that language.

#### The English Phrasebook (`locales/en.ts`)

```typescript
// locales/en.ts (Simplified)
export default {
  // Common
  appName: 'Myform',
  loading: 'Loading...',
  
  // Authentication
  auth: {
    signIn: "Sign In",
    username: "Username",
    password: "Password",
  },
  
  // Navigation
  nav: {
    dashboard: 'Dashboard',
    forms: 'Forms',
    signOut: 'Sign Out',
  },
  
  // Language (for the switcher itself)
  language: {
    en: 'English',
    tr: 'Turkish',
  },
  // ... many more translations
};
```

**Explanation:**
This file holds all the English text. Notice how `auth.signIn` is "Sign In", `nav.forms` is "Forms", etc. These are the *keys* we use in our code.

#### The Turkish Phrasebook (`locales/tr.ts`)

```typescript
// locales/tr.ts (Simplified)
export default {
  // Common
  appName: 'Myform',
  loading: 'Yükleniyor...',
  
  // Authentication
  auth: {
    signIn: "Giriş Yap",
    username: "Kullanıcı Adı",
    password: "Şifre",
  },
  
  // Navigation
  nav: {
    dashboard: 'Gösterge Paneli',
    forms: 'Formlar',
    signOut: 'Çıkış Yap',
  },
  
  // Language (for the switcher itself)
  language: {
    en: 'İngilizce',
    tr: 'Türkçe',
  },
  // ... many more translations (matching keys to en.ts)
};
```

**Explanation:**
This file holds all the Turkish text. It has the *exact same keys* as `en.ts`, but the *values* are the Turkish translations. For example, `auth.signIn` is "Giriş Yap". This consistent key system is crucial.

#### The Index File (`locales/index.ts`)

This file acts like a central directory for our phrasebooks, making it easy to import and manage them.

```typescript
// locales/index.ts
import en from './en';
import tr from './tr';

export type Language = 'en' | 'tr'; // Defines allowed languages

export const locales = {
  en, // English phrasebook
  tr  // Turkish phrasebook
};

export type LocaleType = typeof en; // Defines the shape of a phrasebook object

export default locales;
```

**Explanation:**
This file simply imports our `en.ts` and `tr.ts` files and puts them into a `locales` object. It also defines types like `Language` (which can only be 'en' or 'tr') and `LocaleType` (which describes the structure of our phrasebook, so our code knows what to expect).

### The Language Manager: `contexts/LanguageContext.tsx`

This is the central brain of our I18n system. It manages the current language, holds the active "phrasebook," and provides functions to change the language to any part of `myform`.

```tsx
// contexts/LanguageContext.tsx (Simplified)
'use client';
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { locales, Language, LocaleType } from '@/locales'; // Our phrasebooks

type LanguageContextType = {
  language: Language; // Current language ('en' or 'tr')
  t: LocaleType;      // The active phrasebook (translations)
  changeLanguage: (lang: Language) => void; // Function to switch language
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider = ({ children }: { children: ReactNode }) => {
  const [language, setLanguage] = useState<Language>('en'); // Start with English
  const [translations, setTranslations] = useState<LocaleType>(locales.en); // Start with English phrasebook

  // When the app first loads, check if a language was saved in your browser
  useEffect(() => {
    if (typeof window !== 'undefined') { // Make sure we are in the browser
      const savedLanguage = localStorage.getItem('language') as Language;
      if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'tr')) {
        setLanguage(savedLanguage); // Set saved language
        setTranslations(locales[savedLanguage]); // Load saved phrasebook
        document.documentElement.lang = savedLanguage; // Also set for HTML accessibility
      }
    }
  }, []); // Run only once when component mounts

  // Function to change the language
  const changeLanguage = (lang: Language) => {
    setLanguage(lang); // Update current language state
    setTranslations(locales[lang]); // Load the new phrasebook
    if (typeof window !== 'undefined') {
      localStorage.setItem('language', lang); // Save choice for next time
      document.documentElement.lang = lang; // Update HTML for accessibility/SEO
    }
  };

  return (
    <LanguageContext.Provider value={{ language, t: translations, changeLanguage }}>
      {children} {/* This makes the language available to all parts of the app */}
    </LanguageContext.Provider>
  );
};

// This is the special tool (hook) that other components use
export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
```

**Explanation:**
This `LanguageContext` is a powerful part of React.
1.  **`useState`**: It keeps track of the `language` (e.g., 'en') and the `translations` (the active phrasebook, `locales.en` or `locales.tr`).
2.  **`useEffect`**: When `myform` starts, this automatically checks your browser's `localStorage` to see if you previously saved a language preference. If so, it loads that language.
3.  **`changeLanguage` Function:** This is the core function. When called (e.g., from the `LanguageSwitcher`), it:
    *   Updates the `language` state.
    *   Loads the correct `locales` (phrasebook) based on the new language.
    *   Saves your preference to `localStorage` so it remembers for your next visit.
    *   Also updates the `lang` attribute on the `<html>` tag, which is good for accessibility and search engines.
4.  **`LanguageProvider`**: This component wraps your entire `myform` application. By doing this, it makes the `language`, `t` (translations), and `changeLanguage` function available to *any* component inside the application, without having to pass them manually.
5.  **`useLanguage` Hook:** This is the simplest way for any component to access the `language`, `t`, and `changeLanguage` from the `LanguageContext`.

### How Other Components Use the Translations

Once `LanguageContext` is set up, any component can easily get the translated text it needs:

```tsx
// app/auth/login/LoginForm.tsx (Simplified - from Chapter 1)
'use client';
import { useLanguage } from '@/contexts/LanguageContext'; // Import our language hook

export default function LoginForm() {
  const { t } = useLanguage(); // Get the current phrasebook

  return (
    <form>
      {/* Now use 't' to get translated text */}
      <input type="text" placeholder={t.auth.username} /> 
      <input type="password" placeholder={t.auth.password} />
      <button type="submit">{t.auth.signIn}</button> {/* "Sign In" or "Giriş Yap" */}
    </form>
  );
}
```

**Explanation:**
Instead of writing `placeholder="Username"`, we write `placeholder={t.auth.username}`. When the language changes, the `t` object automatically contains the correct translation, and React updates the text on the screen. This makes `myform` very flexible and easy to adapt to new languages in the future, just by adding new phrasebook files!

## Conclusion

In this chapter, we've explored **Internationalization (I18n)** in `myform`. We learned how this system acts like a dynamic phrasebook, allowing `myform` to seamlessly support multiple languages, primarily English and Turkish. We saw how the `LanguageSwitcher` component interacts with the `LanguageContext` to switch between `locales` (our translation phrasebooks), ensuring all user-facing text automatically updates, providing a truly localized and user-friendly experience for a diverse audience.

Next, we'll dive into how `myform` securely handles sensitive information like external API keys, which are vital for features like our AI-powered form generation.

[Next Chapter: External API Key Handling](06_external_api_key_handling_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)