# Logging System Documentation

## Overview

The Agentic RAG system includes a comprehensive logging system that provides structured, file-based logging with rotation and different log levels. This system helps monitor application performance, debug issues, and analyze usage patterns.

## Features

- **Centralized Configuration**: Single configuration point for all logging settings
- **Multiple Log Files**: Separate logs for different purposes (API, errors, queries, tools, performance)
- **JSON Structured Logs**: Machine-readable logs for analysis tools
- **Log Rotation**: Automatic rotation to prevent disk space issues
- **Performance Monitoring**: Built-in performance tracking and metrics
- **Colored Console Output**: Enhanced readability during development
- **Log Analysis Tools**: Scripts to analyze and generate reports from logs

## Log Files

The system creates the following log files in the `logs/` directory:

### Main Log Files

- **`app.log`**: Main application log with all general information
- **`app.json`**: Main application log in JSON format for analysis
- **`api.log`**: HTTP API requests and responses
- **`api.json`**: API logs in JSON format with structured data
- **`errors.log`**: Error-only log with detailed stack traces
- **`queries.log`**: Query processing workflow and results
- **`queries.json`**: Query logs in JSON format with metadata
- **`tools.log`**: Tool execution details and performance
- **`tools.json`**: Tool logs in JSON format with execution metrics
- **`performance.log`**: Performance metrics and timing information

### Log Rotation

Each log file is automatically rotated when it reaches 10MB (configurable). The system keeps 5 backup files by default.

## Configuration

### Environment Variables

You can configure logging through environment variables:

```bash
# Log directory
LOG_DIR=logs

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Enable console logging (useful for development)
DEBUG=true
```

### Programmatic Configuration

```python
from app.core.logging_config import setup_logging

# Setup logging with custom parameters
setup_logging(
    log_dir="custom_logs",
    log_level="DEBUG",
    max_file_size=20 * 1024 * 1024,  # 20MB
    backup_count=10,
    enable_console=True,
    enable_json_logs=True
)
```

## Usage

### Getting Loggers

```python
from app.core.logging_config import get_logger, get_api_logger, get_query_logger

# General logger
logger = get_logger(__name__)
logger.info("This is an info message")

# Specialized loggers
api_logger = get_api_logger()
query_logger = get_query_logger()
```

### Using Decorators

The system provides decorators for automatic logging:

```python
from app.core.logging_helpers import log_api_request, log_tool_execution, log_query_processing

@log_api_request
async def my_api_endpoint():
    # API calls are automatically logged
    pass

@log_tool_execution
async def execute_tool():
    # Tool execution is automatically logged
    pass

@log_query_processing
async def process_query():
    # Query processing is automatically logged
    pass
```

### Query Logger Helper

For detailed query logging:

```python
from app.core.logging_helpers import QueryLogger

# Initialize query logger
query_logger = QueryLogger(bot_name="MyBot")

# Log query start
query_logger.log_query_start("What is the weather?", user_id="user123")

# Log tool selection
query_logger.log_tool_selection(["WeatherTool"], "Weather query detected")

# Log tool results
query_logger.log_tool_result("WeatherTool", "Found weather data", 1.5)

# Log response generation
query_logger.log_response_generation(150, 0.8)

# Log completion
query_logger.log_query_complete(success=True)
```

### Performance Monitoring

```python
from app.core.logging_helpers import log_performance

# Context manager for performance logging
with log_performance("Database query"):
    # Your code here
    result = database.query()
```

## Log Analysis

### Using the Analysis Script

The system includes a log analysis script:

```bash
# Basic analysis
python scripts/analyze_logs.py

# Specify log directory
python scripts/analyze_logs.py --log-dir /path/to/logs

# Save report to file
python scripts/analyze_logs.py --output report.txt

# JSON output for programmatic use
python scripts/analyze_logs.py --json --output analysis.json
```

### Analysis Features

The analysis script provides:

- **System Summary**: Total log entries, log levels, time ranges
- **Error Analysis**: Error counts, types, patterns, recent errors
- **Query Analysis**: Query success rates, bot usage, performance metrics
- **Tool Analysis**: Tool usage statistics, performance metrics
- **API Analysis**: Request counts, response times, status codes
- **Performance Analysis**: Operation timing, slow operations

### Sample Analysis Output

```
================================================================================
AGENTIC RAG SYSTEM - LOG ANALYSIS REPORT
================================================================================
Generated: 2024-01-15 14:30:00
Log Directory: logs

SYSTEM SUMMARY
----------------------------------------
Total log entries: 15420
Log levels: {'INFO': 12500, 'ERROR': 120, 'WARNING': 800, 'DEBUG': 2000}
Time range: 2024-01-14T09:00:00 to 2024-01-15T14:30:00

ERROR ANALYSIS
----------------------------------------
Total errors: 120
Error types: {'Connection Error': 45, 'Timeout Error': 30, 'General Error': 45}

QUERY ANALYSIS
----------------------------------------
Total queries: 1250
Successful: 1180
Failed: 70
Average duration: 2.345s
Bot usage: {'AcademicBot': 600, 'StudentBot': 400, 'AtlasIQBot': 250}

TOOL ANALYSIS
----------------------------------------
Total executions: 3200
Successful: 3050
Failed: 150
Tool usage: {'SQLQueryTool': 1200, 'DocumentSearchTool': 1000, 'WebSearchTool': 800, 'MongoDBQueryTool': 200}
Tool performance:
  - SQLQueryTool: avg 1.234s (min: 0.100s, max: 5.678s)
  - DocumentSearchTool: avg 0.890s (min: 0.050s, max: 3.456s)
  - WebSearchTool: avg 2.345s (min: 0.500s, max: 8.901s)

API ANALYSIS
----------------------------------------
Total requests: 1300
Average response time: 2.567s
Status codes: {200: 1180, 404: 50, 500: 70}
```

## JSON Log Format

JSON logs include structured data for easy parsing:

```json
{
  "timestamp": "2024-01-15T14:30:00.123456",
  "level": "INFO",
  "logger": "app.core.agentic_rag",
  "message": "Query processed successfully",
  "module": "agentic_rag",
  "function": "process_query",
  "line": 342,
  "request_id": "abc12345",
  "bot_name": "AcademicBot",
  "query_id": "def67890",
  "duration": 2.345,
  "extra": {
    "query_length": 150,
    "tools_used": ["SQLQueryTool", "DocumentSearchTool"],
    "response_length": 1200
  }
}
```

## Monitoring and Alerts

### Log Patterns to Monitor

The system automatically monitors for these patterns:
- ERROR and CRITICAL messages
- "Failed to" messages
- "Connection refused" errors
- "Timeout" errors

### Performance Thresholds

Default performance thresholds:
- Query processing: 30 seconds
- Tool execution: 10 seconds
- API response: 5 seconds

### Custom Monitoring

You can implement custom monitoring by parsing JSON logs:

```python
import json

def monitor_logs(log_file):
    with open(log_file, 'r') as f:
        for line in f:
            log_entry = json.loads(line)
            
            # Check for slow queries
            if log_entry.get('duration', 0) > 10:
                send_alert(f"Slow query detected: {log_entry['duration']}s")
            
            # Check for errors
            if log_entry.get('level') == 'ERROR':
                send_alert(f"Error: {log_entry['message']}")
```

## Best Practices

1. **Use Appropriate Log Levels**:
   - DEBUG: Detailed diagnostic information
   - INFO: General information about system operation
   - WARNING: Something unexpected happened but system continues
   - ERROR: Serious problem occurred
   - CRITICAL: System cannot continue

2. **Include Context**:
   - Always include relevant IDs (request_id, user_id, bot_name)
   - Add timing information for performance analysis
   - Include error details and stack traces

3. **Avoid Logging Sensitive Data**:
   - Don't log passwords, API keys, or personal information
   - Truncate long query texts in logs
   - Use log levels appropriately to control what gets logged

4. **Regular Log Analysis**:
   - Run log analysis regularly to identify trends
   - Monitor error rates and performance metrics
   - Set up alerts for critical issues

5. **Log Rotation and Cleanup**:
   - Configure appropriate log rotation settings
   - Set up automated cleanup of old log files
   - Monitor disk space usage

## Troubleshooting

### Common Issues

1. **Logs not appearing**: Check LOG_LEVEL environment variable
2. **Permission errors**: Ensure write permissions to log directory
3. **Disk space issues**: Configure log rotation and cleanup
4. **Performance impact**: Adjust log levels in production

### Debug Mode

Enable debug mode for detailed logging:

```bash
export DEBUG=true
export LOG_LEVEL=DEBUG
```

### Log File Permissions

Ensure proper permissions:

```bash
chmod 755 logs/
chmod 644 logs/*.log
```

## Integration with External Tools

### ELK Stack (Elasticsearch, Logstash, Kibana)

JSON logs can be easily ingested by ELK stack:

```yaml
# logstash.conf
input {
  file {
    path => "/path/to/logs/*.json"
    codec => "json"
  }
}

output {
  elasticsearch {
    hosts => ["localhost:9200"]
  }
}
```

### Grafana

Use Grafana with Loki for log visualization:

```yaml
# promtail.yml
clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: agentic-rag
    static_configs:
      - targets:
          - localhost
        labels:
          job: agentic-rag
          __path__: /path/to/logs/*.json
```

This comprehensive logging system provides full visibility into your Agentic RAG system's operation, making it easier to monitor, debug, and optimize performance.
