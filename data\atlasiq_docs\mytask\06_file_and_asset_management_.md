# Chapter 6: File and Asset Management

Welcome back to the `mytask` journey! In [Chapter 5: Notifications & Communications](05_notifications___communications_.md), we learned how `mytask` intelligently sends you updates about your tasks, whether inside the app or via external messages. Now, imagine you're working on a task like "Prepare Marketing Presentation." This task probably needs more than just a title and description; it needs files! Maybe a draft presentation, some images, or a report.

### The Problem: Managing All Your Digital Stuff

Think about working on a project where you have many supporting documents:
*   Images for a presentation.
*   Word documents with notes.
*   PDFs of research papers.
*   Spreadsheets with data.

If you just keep these files on your computer, how do you share them easily with your team? How do you know which version is the latest? What if you accidentally delete something important? Sending them via email every time someone needs them is chaotic and quickly leads to a mess of different versions.

This is the problem that **File and Asset Management** solves for `mytask`. It's like having a super organized, secure digital locker right inside your task management system for all your task-related documents.

### Solution: Your Secure Digital Locker

`mytask` provides a robust way to handle any files attached to tasks. It's built to:

1.  **Upload:** Easily send new files (like documents or images) from your computer to the `mytask` server.
2.  **Secure Storage:** Store these files safely on the server, so they don't get lost.
3.  **Unique Access:** Give each file a special, unique web address (URL) so you and your team can view or download them easily from anywhere.
4.  **Manage:** Keep track of which files belong to which task, and allow you to remove them when they're no longer needed.

Let's walk through a common example: "Attaching a file to an existing task."

#### Central Use Case: Attaching a Report to a Task

Imagine you have a task "Review Q3 Sales Report" and you want to attach the actual PDF report to it.

1.  **You open the Task:** You navigate to the "Review Q3 Sales Report" task in `mytask`.
2.  **You click "Attach File":** You see an option to add files. This might open a file selection window on your computer.
3.  **You select the file:** You choose the "Q3_Sales_Report.pdf" file from your computer.
4.  **`mytask` uploads and links:** The file is uploaded, and `mytask` updates the task to show that "Q3_Sales_Report.pdf" is now attached. You can then click on it to view it!

### How it All Works Together (Under the Hood)

Let's see the journey of your "Q3_Sales_Report.pdf" from your computer to being securely linked to your task.

```mermaid
sequenceDiagram
    participant You
    participant MyTaskApp as MyTask App (Browser)
    participant MyTaskServer as MyTask Server
    participant FileStorage as Server File Storage
    participant Database

    You->>MyTaskApp: Select "Q3_Sales_Report.pdf" for upload
    MyTaskApp->>MyTaskServer: Send File (via special upload request)
    MyTaskServer->>FileStorage: Save File Securely
    FileStorage-->>MyTaskServer: File Saved! (Returns file info like name, path)
    MyTaskServer->>Database: Update Task with File Info (e.g., link report to task)
    Database-->>MyTaskServer: Task Updated!
    MyTaskServer-->>MyTaskApp: Confirmation: File Linked
    MyTaskApp->>You: Display Task with Attached File
```

**Step-by-step Explanation:**

1.  **You initiate upload:** You interact with the `mytask` application in your browser and select the file you want to attach.
2.  **App sends file:** Your `mytask` application sends the actual file data to a special "file upload" part of the `mytask` server.
3.  **Server processes and saves file:** The `mytask` server receives the file. It uses special tools to handle the file data, save it to a designated secure folder on the server's hard drive (`FileStorage`), and give it a unique name.
4.  **Server updates task in database:** After saving the file, the server gets back important information about it (like its unique name, type, and size). It then tells the main `mytask` database to update your task, adding a reference to this newly uploaded file.
5.  **Task is updated:** The database links the file's information to your task record.
6.  **Confirmation to you:** The server sends a success message back to your `mytask` application, which then updates your view of the task, showing the file is now attached.

### Diving Deeper: The Code Behind File Management

Let's look at the pieces of code that make this happen.

#### 1. What is an "Asset" in `mytask`? (The Blueprint)

`mytask` needs to remember information about each attached file. This is stored within the `Task` blueprint itself, in a field called `assets`.

```javascript
// server\models\taskModel.js (Simplified)
import mongoose, { Schema } from "mongoose";

const taskSchema = new Schema(
  {
    // ... other task properties like title, description, team
    assets: [ // This is an array to hold multiple files
      {
        filename: { type: String, required: true },     // Unique name on server
        originalname: { type: String, required: true }, // Original name user uploaded
        mimetype: { type: String },                     // Type of file (e.g., image/png)
        size: { type: Number },                         // Size in bytes
        path: { type: String },                         // Internal server path
        url: { type: String },                          // Public URL to access file
      },
    ],
    // ...
  },
  { timestamps: true }
);

const Task = mongoose.model("Task", taskSchema);
export default Task;
```
**Explanation:** The `assets` field in our `Task` blueprint (schema) is an array of objects. Each object describes one file attached to the task. It includes details like the file's original name, its unique name on the server, its type, size, and the special `url` that allows `mytask` (and you!) to view or download it later.

#### 2. The File Upload "Guard" (Server-Side Middleware)

When you send a file from your browser, the `mytask` server first passes it through a special "guard" called `multer`. `multer` is a tool that helps `mytask` handle file uploads. It decides *where* to save the file temporarily and *what type* of files are allowed.

```javascript
// server\middleware\uploadMiddleware.js (Simplified)
import multer from 'multer';
import path from 'path';
import fs from 'fs'; // For file system operations

// Configure where to store files temporarily
const uploadsDir = path.join(__dirname, '../uploads/tasks');
if (!fs.existsSync(uploadsDir)) { // Create folder if it doesn't exist
  fs.mkdirSync(uploadsDir, { recursive: true });
}

const storage = multer.diskStorage({
  destination: function(req, file, cb) {
    cb(null, uploadsDir); // Save to the uploads/tasks folder
  },
  filename: function(req, file, cb) {
    // Give the file a unique name based on time + random number
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname); // Keep original extension
    cb(null, uniqueSuffix + extension);
  }
});

// Filter to allow only certain file types (e.g., images, PDFs, Word docs)
const fileFilter = (req, file, cb) => {
  const allowedTypes = [ 'image/jpeg', 'image/png', 'application/pdf', 'application/msword' ];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true); // Accept the file
  } else {
    cb(new Error('Unsupported file type.'), false); // Reject the file
  }
};

// Create the upload instance
const upload = multer({ 
  storage: storage,
  fileFilter: fileFilter,
  limits: { fileSize: 10 * 1024 * 1024 } // Max 10MB per file
});

export default upload;
```
**Explanation:** This `uploadMiddleware.js` file sets up `multer`. It tells `multer` to save files in the `uploads/tasks` folder on the server. It also creates a unique name for each uploaded file (like `16788812345-12345.pdf`) and checks if the file type is allowed (e.g., it won't let you upload a `.exe` file). This runs *before* the file controller processes the request.

#### 3. Handling the Upload (Server-Side Controller)

After `multer` handles the raw file, the `fileController` takes over. It manages saving the file's metadata and preparing URLs.

```javascript
// server\controllers\fileController.js (Simplified uploadFiles)
import asyncHandler from "express-async-handler";
import path from 'path';
import fs from 'fs';

const uploadFiles = asyncHandler(async (req, res) => {
  if (!req.files || req.files.length === 0) {
    return res.status(400).json({ message: "No files uploaded" });
  }

  const uploadedFiles = req.files.map(file => {
    // Create a path relative to the server for storage
    const relativePath = `uploads/tasks/${file.filename}`;
    
    // For convenience, also copy file to client's assets (optional, for direct serving)
    const clientAssetsDir = path.join(__dirname, '../../client/assets');
    fs.copyFileSync(file.path, path.join(clientAssetsDir, file.filename));

    return {
      filename: file.filename,     // The unique name given by Multer
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      path: relativePath,
      url: `/api/files/${file.filename}` // The URL to access this file later
    };
  });

  res.status(200).json({
    status: true,
    message: "Files uploaded successfully",
    files: uploadedFiles // Send back info about the uploaded files
  });
});

export { uploadFiles };
```
**Explanation:** The `uploadFiles` function receives the processed files from `multer`. For each file, it creates a structured object containing all the details defined in our `assets` blueprint (filename, original name, type, size). Crucially, it generates a `url` like `/api/files/your-unique-file.pdf`. This `url` is what `mytask` will store in the task's `assets` array and use later to display or download the file. The `fs.copyFileSync` line also makes a copy of the uploaded file to a public `client/assets` folder, which is useful for web browsers to access images or documents directly.

#### 4. Linking Files to Tasks (Server-Side Task Controller)

After the files are uploaded and `fileController` provides their metadata, the `taskController` is responsible for updating the actual task with this information. When you update a task and provide new `assets`, the `updateTask` function adds these new assets.

```javascript
// server\controllers\taskController.js (Simplified updateTask for assets)
const updateTask = asyncHandler(async (req, res) => {
  const { id } = req.params; // ID of the task to update
  const { title, description, assets } = req.body; // Incoming task data, including new assets

  try {
    const task = await Task.findById(id);
    if (!task) return res.status(404).json({ message: "Task not found" });

    // ... (Permission checks, as discussed in Chapter 4) ...

    // Update the assets array of the task
    if (assets !== undefined) {
      task.assets = assets; // Replace or update the assets list
    }
    
    // ... (other task updates like title, description, team, etc.) ...

    const updatedTask = await task.save(); // Save the task with updated assets

    res.status(200).json({ message: "Task updated successfully.", task: updatedTask });
  } catch (error) {
    console.error('Error updating task:', error);
    return res.status(400).json({ status: false, message: error.message });
  }
});
```
**Explanation:** When you edit a task and attach files, the `mytask` app on your browser first sends the files to the `/api/files/upload` endpoint (handled by `fileController`). That endpoint returns the `assets` information (like `filename`, `url`). Then, your `mytask` app sends *this `assets` information* along with other task details to the `updateTask` function. This `updateTask` function simply takes the provided `assets` array and saves it directly into the `task` record in the database.

#### 5. Defining the Server Routes

For all this to work, `mytask` needs specific web addresses (routes) for file operations:

```javascript
// server\routes\fileRoutes.js
import express from 'express';
import { protectRoute } from '../middleware/authMiddleware.js'; // From Chapter 2
import upload from '../middleware/uploadMiddleware.js';         // Our Multer setup
import { uploadFiles, getFile, deleteFile } from '../controllers/fileController.js';

const router = express.Router();

// Route to upload files (uses our 'upload' middleware)
router.post('/upload', upload.array('file', 10), uploadFiles); // Accepts up to 10 files

// Route to get a specific file (e.g., for displaying in browser)
router.get('/:filename', getFile);

// Route to delete a specific file (requires login)
router.delete('/:filename', protectRoute, deleteFile);

export default router;
```
**Explanation:** This `fileRoutes.js` file tells the `mytask` server:
*   When a `POST` request comes to `/api/files/upload`, use the `upload` middleware (Multer) to handle the file data, then pass it to `uploadFiles` in `fileController`.
*   When a `GET` request comes to `/api/files/:filename` (e.g., `/api/files/report.pdf`), use the `getFile` function to send that file back to the browser.
*   When a `DELETE` request comes to `/api/files/:filename`, first `protectRoute` (ensure user is logged in), then use `deleteFile` to remove the file from the server.

This clear separation of roles ensures that file handling is efficient, secure, and well-organized.

### Conclusion

In this chapter, we explored `mytask`'s **File and Asset Management** system. We learned how it provides a secure digital locker for all your task-related documents, allowing you to easily upload, store, access, and manage files like images and reports. We saw how files are handled by special middleware (`multer`), processed by dedicated controllers (`fileController`), and then securely linked to your tasks within the database.

Now that we've covered the user interface, authentication, Atlas integration, task management, notifications, and file management, it's time to pull back the curtain on the underlying technical foundation. In the next chapter, we'll dive into how all this data is stored and how the different parts of `mytask` communicate: [Data Persistence & API Layer](07_data_persistence___api_layer_.md).

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)