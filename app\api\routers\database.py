"""
Database-related API endpoints.
"""

import asyncio
from fastapi import APIRouter
import pyodbc
import pandas as pd

from app.models.api_models import (
    SqlConnectionRequest,
    SqlConnectionResponse,
    SqlQueryRequest,
    SqlQueryResponse,
)
from app.core.logging_config import get_logger
from app.core.logging_helpers import get_simple_logger_instance

logger = get_logger(__name__)
simple_logger = get_simple_logger_instance()

router = APIRouter(prefix="/database", tags=["Database"])


@router.post("/test-connection", response_model=SqlConnectionResponse)
async def test_sql_connection(request: SqlConnectionRequest):
    """Test SQL database connection."""
    try:
        logger.info("Testing SQL database connection...")

        # Test connection
        conn = await asyncio.to_thread(pyodbc.connect, request.connection_string)
        await asyncio.to_thread(conn.close)

        logger.info("✅ SQL connection successful")
        # Use a custom log message for SQL connection success
        simple_logger.logger.info("🔗 SQL BAĞLANTI TESTİ | ✅ BAŞARILI")

        return SqlConnectionResponse(success=True, message="✅ Bağlantı başarılı.")

    except Exception as e:
        error_msg = f"❌ Bağlantı başarısız: {str(e)}"
        logger.error(f"SQL connection test failed: {str(e)}")
        simple_logger.error_occurred("SQL bağlantı hatası", str(e))

        return SqlConnectionResponse(success=False, message=error_msg, error=str(e))


@router.post("/test-query", response_model=SqlQueryResponse)
async def test_sql_query(request: SqlQueryRequest):
    """Test SQL database query and list tables with sample data."""
    try:
        logger.info("Testing SQL database query...")

        # Test connection first
        conn = await asyncio.to_thread(pyodbc.connect, request.connection_string)
        logger.info("✅ Bağlantı başarılı.")

        # Get list of tables
        cursor = conn.cursor()
        await asyncio.to_thread(
            cursor.execute,
            """
            SELECT TABLE_SCHEMA, TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_TYPE = 'BASE TABLE'
        """,
        )
        tables = await asyncio.to_thread(cursor.fetchall)

        all_dataframes = {}
        table_names = []

        # Get first 5 rows from each table
        for schema, table in tables:
            table_name = f"{schema}.{table}"
            table_names.append(table_name)
            query = f"SELECT TOP 5 * FROM [{schema}].[{table}]"
            try:
                df = await asyncio.to_thread(pd.read_sql, query, conn)
                all_dataframes[table_name] = df
                logger.info(f"✅ {table_name} yüklendi ({len(df)} satır).")
            except Exception as e:
                logger.warning(f"❌ {table_name} yüklenemedi: {e}")

        await asyncio.to_thread(conn.close)

        # Prepare sample data from first table
        sample_data = None
        if all_dataframes:
            first_table = list(all_dataframes.keys())[0]
            first_df = all_dataframes[first_table]
            sample_data = {
                "table_name": first_table,
                "row_count": len(first_df),
                "columns": list(first_df.columns),
                "sample_rows": first_df.to_dict("records"),
            }

        success_message = f"✅ Sorgu başarılı. {len(table_names)} tablo yüklendi."
        logger.info(success_message)
        # Use a custom log message for SQL query success
        simple_logger.logger.info(
            f"📊 SQL SORGU TESTİ | ✅ BAŞARILI | {len(table_names)} tablo yüklendi"
        )

        return SqlQueryResponse(
            success=True,
            message=success_message,
            tables=table_names,
            sample_data=sample_data,
        )

    except Exception as e:
        error_msg = f"❌ Sorgu başarısız: {str(e)}"
        logger.error(f"SQL query test failed: {str(e)}")
        simple_logger.error_occurred("SQL sorgu hatası", str(e))

        return SqlQueryResponse(success=False, message=error_msg, error=str(e))
