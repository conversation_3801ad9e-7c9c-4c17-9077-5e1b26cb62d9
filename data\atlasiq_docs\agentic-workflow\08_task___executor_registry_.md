# Chapter 8: Task & Executor Registry

Welcome back! In the [previous chapter](07_execution_phases_.md), we explored **Execution Phases**, the detailed records created during a **[Workflow Execution](05_workflow_execution_.md)** that show exactly what happened when each individual task ran, including its inputs, outputs, and logs. You've seen how workflows are built from **[Tasks](02_tasks_.md)** (and **[AI Agents](03_ai_agents_.md)**), how the **[Flow Editor](04_flow_editor_.md)** helps you arrange them visually, and how an **[Execution Plan](06_execution_plan_.md)** turns that visual design into a step-by-step sequence.

But there's still a piece missing. How does the system know what a "Navigate URL" task *is*? How does it know its name, icon, inputs it expects, or outputs it produces? And more importantly, when the **[Workflow Execution](05_workflow_execution_.md)** engine needs to *run* a "Navigate URL" task, how does it find the specific piece of code that actually performs the navigation action?

This is where the **Task Registry** and the **Executor Registry** come in.

### What are Task & Executor Registries?

Imagine `agentic-workflow` is a workshop with many different tools.

*   When you're designing your project (in the [Flow Editor](04_flow_editor_.md)), you need a **catalogue** of all the available tools. This catalogue tells you the tool's name, what it looks like (icon), what materials it needs (inputs), and what it produces (outputs). This catalogue is like the **Task Registry**. It's a central list that maps a unique `TaskType` (like `NAVIGATE_URL`) to all its descriptive information.
*   When you actually *use* a tool in the workshop (during a **[Workflow Execution](05_workflow_execution_.md)**), you need to know *how* to operate that specific tool – you need the actual tool itself, ready to perform its action. This collection of all the tools, ready to be picked up and used, is like the **Executor Registry**. It's a central list that maps a `TaskType` to the specific *code function* (the "Executor") that performs the task's logic.

In simple terms:

*   **Task Registry:** Describes *what* a task is (metadata for UI, planning).
*   **Executor Registry:** Knows *how* to run a task (the actual backend code).

These two registries act as central lookup tables that connect a task's unique identifier (`TaskType`) to everything the system needs to display it, validate it, plan it, and execute it.

### The Task Registry: Describing Tasks

The **Task Registry** is a collection of definitions for every standard [Task](02_tasks_.md) type available in the system. It's defined in the code, specifically in files like `lib/workflow/task/registry.ts` (and `registry.tsx`, which might be slightly different for UI-specific aspects).

Let's look at a simplified version of the `TaskRegistry` object:

```typescript
// Inside lib/workflow/task/registry.ts (simplified)
import { TaskType } from "@/types/task";
import { WorkflowTask } from "@/types/workflow";
// Import definitions for each task type
import { NavigateUrlTask } from "./NavigateUrlTask";
import { ExtractTextFromElementTask } from "./ExtractTextFromElement";
import { AgentTask } from "./AgentTask"; // Definition for AI Agent task

// The Task Registry is an object mapping TaskType to WorkflowTask definition
export const TaskRegistry: Partial<Record<TaskType, WorkflowTask>> = {
  [TaskType.NAVIGATE_URL]: NavigateUrlTask,
  [TaskType.EXTRACT_TEXT_FROM_ELEMENT]: ExtractTextFromElementTask,
  [TaskType.AGENT]: AgentTask,
  // ... definitions for all other task types ...
};
```

This `TaskRegistry` object is essentially a dictionary or map. You can look up a task definition using its `TaskType` as the key. For example, `TaskRegistry[TaskType.NAVIGATE_URL]` would give you the definition object for the "Navigate URL" task.

What does a task definition (`WorkflowTask`) contain? As we saw briefly in [Chapter 2](02_tasks_.md), it includes the metadata the system needs:

```typescript
// Inside types/workflow.ts (simplified WorkflowTask type)
import { TaskParam, TaskType } from "@/types/task";
import { LucideProps } from "lucide-react";

export type WorkflowTask = {
  label: string; // Human-readable name (e.g., "Navigate Url")
  icon: React.FC<LucideProps>; // Icon component for the UI
  type: TaskType; // The unique identifier (e.g., TaskType.NAVIGATE_URL)
  isEntryPoint?: boolean; // Can this task be the first one?
  inputs: TaskParam[]; // List of expected inputs
  outputs: TaskParam[]; // List of produced outputs
  credits: number; // How many credits does it cost?
};
```

So, the Task Registry stores all these details for every task type.

#### Who Uses the Task Registry?

The Task Registry is primarily used by the frontend UI and the parts of the backend responsible for validation and planning:

1.  **[Flow Editor](04_flow_editor_.md) (Task Menu):** The menu on the left uses the `TaskRegistry` to get the `label`, `icon`, and `credits` for each task type so it can display the draggable buttons.

    ```typescript
    // Inside app/workflow/_components/TaskMenu.tsx (simplified TaskMenuBtn component)
    function TaskMenuBtn({ taskType }: { taskType: TaskType }) {
      // Look up the task definition in the registry
      const task = TaskRegistry[taskType];

      if (!task) { /* handle error */ }

      return (
        <Button /* ... */>
          <div className="flex gap-2">
            <task.icon size={20} /> {/* Use icon from registry */}
            {task.label} {/* Use label from registry */}
          </div>
          <Badge /* ... */>
            <CoinsIcon size={16} />
            {task.credits} {/* Use credits from registry */}
          </Badge>
        </Button>
      );
    }
    ```

2.  **[Flow Editor](04_flow_editor_.md) (Node Display):** When drawing nodes on the canvas, the `NodeComponent.tsx` looks up the task definition in the `TaskRegistry` to display the correct label, icon, and list of input/output handles.

    ```typescript
    // Inside app/workflow/_components/nodes/NodeComponent.tsx (simplified)
    import { TaskRegistry } from "@/lib/workflow/task/registry";
    // ... other imports ...

    const NodeComponent = memo((props: NodeProps) => {
      const nodeData = props.data as AppNodeData;
      // Look up the task definition using the node's type
      const task = TaskRegistry[nodeData.type];

      if (!task) { /* handle error */ }

      return (
        <NodeCard /* ... */>
          {/* Show label/icon from the looked-up task definition */}
          <NodeHeader taskType={nodeData.type} nodeId={props.id} />
          <NodeInputs>
            {/* Render inputs based on task.inputs from definition */}
            {task.inputs.map((input) => (
              <NodeInput key={input.name} input={input} nodeId={props.id} />
            ))}
          </NodeInputs>
          <NodeOutputs>
            {/* Render outputs based on task.outputs from definition */}
            {task.outputs.map((output) => (
              <NodeOutput key={output.name} output={output} />
            ))}
          </NodeOutputs>
        </NodeCard>
      );
    });
    ```

3.  **[Flow Editor](04_flow_editor_.md) (Validation):** When you try to draw connections (`isValidConnection`) or when generating the [Execution Plan](06_execution_plan_.md) (`FlowToExecutionPlan`), the system uses the `TaskRegistry` to get the expected `type` for inputs and outputs and check if required inputs are satisfied.

    ```typescript
    // Inside lib/workflow/executionPlan.ts (simplified getInvalidInputs function)
    function getInvalidInputs(node: AppNode, edges: Edge[], planned: Set<string>) {
      // Look up the task definition for the node
      const task = (node.data.type === AGENT_TYPE ? AgentTask : TaskRegistry[node.data.type as TaskType]);

      if (!task) return []; // Should not happen

      for (const input of task.inputs) { // Loop through expected inputs from definition
        // ... validation logic using input.required and connections ...
      }
      return []; // Simplified, actual function returns list of invalid inputs
    }
    ```

In essence, the Task Registry is the source of truth for all descriptive and structural information about every available task type.

### The Executor Registry: Running Tasks

The **Executor Registry** is where the system finds the actual *code* that performs the logic for each task type. It maps a `TaskType` to a specific function, called an **Executor Function** (or just Executor), which contains the instructions for what that task should *do*.

This registry is defined in `lib/workflow/executor/registry.ts`.

```typescript
// Inside lib/workflow/executor/registry.ts (simplified)
// Import each specific Executor function
import { NavigateUrlExecutor } from "@/lib/workflow/executor/NavigateUrlExecutor";
import { ExtractTextFromElementExecutor } from "@/lib/workflow/executor/ExtractTextFromElementExecutor";
import { AgentExecutor } from "@/lib/workflow/executor/AgentExecutor"; // Executor for AI Agent task
import { TaskType } from "@/types/task";
import { ExecutionEnvironment } from "@/types/executor";
import { WorkflowTask } from "@/types/workflow";
import { AGENT_TYPE } from "@/lib/workflow/task/AgentTask";


// Define the type for an Executor function
type ExecutorFn<T extends WorkflowTask> = (
  environment: ExecutionEnvironment<T> // Executor functions receive an environment object
) => Promise<boolean>; // They return true for success, false for failure

// The Executor Registry is an object mapping TaskType (or AGENT_TYPE) to the ExecutorFn
export const ExecutorRegistry = {
  [TaskType.NAVIGATE_URL]: NavigateUrlExecutor as ExecutorFn<any>,
  [TaskType.EXTRACT_TEXT_FROM_ELEMENT]: ExtractTextFromElementExecutor as ExecutorFn<any>,
  [AGENT_TYPE]: AgentExecutor as ExecutorFn<any>, // AGENT_TYPE is also mapped here
  // ... executors for all other task types ...
};
```

Like the Task Registry, the `ExecutorRegistry` is a map. You can look up the specific function to run for a task type using its `TaskType` (or `AGENT_TYPE` for AI Agents) as the key. `ExecutorRegistry[TaskType.NAVIGATE_URL]` gives you the `NavigateUrlExecutor` function.

The specific logic for *how* a "Navigate URL" task works (controlling the browser to go to a page) is contained *inside* the `NavigateUrlExecutor` function. Similarly, the logic for *how* an "AI Agent" task works (looking up the agent, calling the LLM API via a script) is inside the `AgentExecutor` function (as we saw in [Chapter 3](03_ai_agents_.md)).

Executor functions are designed to be independent. They receive all the inputs they need and use the provided `ExecutionEnvironment` object to access shared resources (like a browser instance) and report their outputs and logs.

#### Who Uses the Executor Registry?

The Executor Registry is used exclusively by the **[Workflow Execution](05_workflow_execution_.md)** engine, specifically by the `executeWorkflowPhase` function.

When `executeWorkflowPhase` is processing a specific task instance from the [Execution Plan](06_execution_plan_.md) (represented by an **Execution Phase** record), it needs to find and run the code for that task type.

```typescript
// Inside lib/workflow/executeWorkflow.ts (simplified executeWorkflowPhase)
async function executeWorkflowPhase(
  phase: ExecutionPhase, environment: Environment, edges: Edge[], userId: string
) {
  // ... setup inputs, update phase status, decrement credits ...

  const node = JSON.parse(phase.node) as AppNode; // Get the task node details

  let success = false;
  if (/* ... credits sufficient ... */) {
    // Find the correct Executor function in the registry using the node's type
    let runFn = ExecutorRegistry[node.data.type as TaskType];
    // Special case for AGENT type, as its key is AGENT_TYPE string, not TaskType enum
    if (node.data.type === AGENT_TYPE) runFn = ExecutorRegistry[AGENT_TYPE];

    if (!runFn) {
       // This shouldn't happen if everything is registered correctly, indicates a bug
       logCollector.error(`not found executor for ${node.data.type}`);
       success = false;
    } else {
       // Create the environment for the executor
       const executionEnvironment: ExecutionEnvironment<any> =
         createExecutionEnvironment(node, environment, logCollector);
       // *** Call the Executor function found in the registry! ***
       success = await runFn(executionEnvironment);
    }
  }

  // ... finalize phase: save outputs/logs/status ...

  return { success, creditsConsumed };
}
```

This code shows the core interaction: `executeWorkflowPhase` looks up the `TaskType` in the `ExecutorRegistry` to get the `runFn` (the Executor function) and then calls that function, passing it the necessary environment and inputs. The Executor function then performs the task's action.

### Putting It All Together (The Flow)

Let's visualize how the registries fit into the overall process, using our workshop analogy:

```mermaid
sequenceDiagram
    participant User
    participant FlowEditor as Design Studio (UI)
    participant TaskRegistry as Tool Catalogue
    participant WorkflowExecution as Workshop Manager (Engine)
    participant ExecutorRegistry as Tool Collection (Executors)
    participant TaskExecutor as Specific Tool Use (Executor Code)

    User->>FlowEditor: Design Workflow (drag/connect tasks)
    FlowEditor->>TaskRegistry: Look up task details (label, icon, inputs, outputs) for display
    TaskRegistry-->>FlowEditor: Return task definitions
    FlowEditor->>FlowEditor: User connects tasks based on inputs/outputs
    User->>FlowEditor: Save Workflow
    FlowEditor->>WorkflowExecution: Save blueprint (definition)
    WorkflowExecution->>WorkflowExecution: Generate Execution Plan (uses TaskRegistry for validation)
    WorkflowExecution->>WorkflowExecution: Saved Workflow + Execution Plan

    User->>WorkflowExecution: Start Workflow Run (select saved blueprint)
    WorkflowExecution->>WorkflowExecution: Create Execution Phases (records)
    loop For each Execution Phase (task instance) in the plan
        WorkflowExecution->>WorkflowExecution: Prepare inputs for this phase
        WorkflowExecution->>ExecutorRegistry: Look up Executor for this task type
        ExecutorRegistry-->>WorkflowExecution: Return the specific Executor function
        WorkflowExecution->>TaskExecutor: Call the Executor function (pass inputs, environment, log collector)
        TaskExecutor-->>WorkflowExecution: Return success/failure, outputs
        WorkflowExecution->>WorkflowExecution: Update Execution Phase record (status, inputs, outputs, logs)
    end
    WorkflowExecution->>WorkflowExecution: Finalize Execution (overall status, total credits)

    User->>FlowEditor: View Workflow Runs
    FlowEditor->>WorkflowExecution: Get Execution Details (including Phases)
    WorkflowExecution->>WorkflowExecution: Retrieve Execution and Phase records
    WorkflowExecution-->>FlowEditor: Return data
    FlowEditor->>TaskRegistry: Look up task details (name, icon) for Phase display
    TaskRegistry-->>FlowEditor: Return task definitions
    FlowEditor->>User: Display Execution Phases with details (inputs, outputs, logs)
```

As you can see:

*   The **Task Registry** is used during the *design and planning* phase to understand what tasks *are*.
*   The **Executor Registry** is used during the *execution* phase to find and run the code that performs the task's action.

They are distinct but work together to enable the full lifecycle of building and running automations.

### Why Separate Registries?

Having two separate registries (one for description/metadata, one for execution logic) provides several benefits:

1.  **Clear Separation of Concerns:** The UI code only needs to know *about* tasks (names, icons, what inputs/outputs they have) from the `TaskRegistry`. It doesn't need access to the backend execution code. The execution engine only needs to know *how* to run a task using the `ExecutorRegistry`. It doesn't necessarily need all the UI metadata during execution.
2.  **Maintainability:** If you change the icon or label of a task, you only update its definition in the `TaskRegistry` (in `lib/workflow/task/registry.ts`). If you change *how* a task performs its action, you only update its code in the specific Executor file and ensure it's correctly mapped in the `ExecutorRegistry` (`lib/workflow/executor/registry.ts`). These changes are often isolated.
3.  **Flexibility:** It makes it easier to potentially add new task types or executors in the future. You just need to define the new task's metadata and create its executor function, then add them to the respective registries.

This design allows the different parts of the system (frontend, backend, execution engine) to interact with tasks in a structured and organized way.

### Conclusion

The **Task Registry** and **Executor Registry** are fundamental components in `agentic-workflow`. The **Task Registry** is a central catalogue providing metadata like names, icons, inputs, outputs, and costs for every available task type, used by the UI and planning logic. The **Executor Registry** is a central collection of the actual code functions (Executors) that perform the specific actions of each task type, used exclusively by the execution engine. Together, these registries provide the necessary mappings for the system to understand, display, plan, and execute workflows composed of various tasks and AI Agents.

In the next chapter, we'll look at another important concept for running tasks that require sensitive information: **[Credentials](09_credentials_.md)**.

[Credentials](09_credentials_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)