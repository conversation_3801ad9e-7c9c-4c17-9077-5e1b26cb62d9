# Chapter 2: Tasks

Welcome back to the `agentic-workflow` tutorial! In the [previous chapter](01_workflows_.md), we learned that a **Workflow** is like a recipe or blueprint for automating a task. But what makes up this recipe?

Think about making a cake. The recipe tells you the overall process, but it's broken down into individual steps: "Add flour," "Mix eggs," "Bake for 30 minutes." In `agentic-workflow`, these individual steps are called **Tasks**.

### What is a Task?

A **Task** is the fundamental building block of any Workflow. It represents a single, specific action that the automation system can perform. Just as you combine ingredients and steps to make a cake, you combine Tasks to create a Workflow that achieves a goal.

For our example of checking a product price from Chapter 1, the Workflow might look like this:

1.  **Task:** Go to the online store page (e.g., "Navigate URL" Task).
2.  **Task:** Find the price on that page (e.g., "Extract Text from Element" Task).
3.  **Task:** Save the price (e.g., "Add Property to JSON" Task or "Deliver via Webhook" Task).

Each one of these is a distinct Task.

### Tasks in the Visual Editor (Nodes)

When you build a workflow using the visual editor (which we'll explore in detail in the [Flow Editor chapter](04_flow_editor_.md)), Tasks are shown as **nodes**. These nodes look like blocks that you can connect.

Imagine the nodes are like building blocks on a canvas:

```mermaid
graph LR
    A[Task A: Start Process] --> B(Task B: Do Something)
    B --> C[Task C: Get Data]
    C --> D(Task D: Save Result)
```

In this simple flowchart, A, B, C, and D are all Tasks, represented as nodes.

### Inputs and Outputs (Ingredients and Results)

Tasks don't operate in isolation. They often need information to do their job, and they often produce results that other tasks can use.

*   **Inputs:** These are the "ingredients" or data a Task needs to perform its action. In the visual editor, inputs are typically represented as connection points on the *left* side of a node.
*   **Outputs:** These are the "results" or data that a Task produces after it finishes its action. Outputs are typically represented as connection points on the *right* side of a node.

Let's look at our product price example with inputs and outputs:

```mermaid
graph LR
    A[Navigate URL Task] --> B[Page to HTML Task]
    B --> C[Extract Text from Element Task]
    C --> D[Add Property to JSON Task]

    A -- "Output: Web page" --> B -- "Input: Web page"
    B -- "Output: HTML" --> C -- "Input: HTML"
    C -- "Output: Extracted Text" --> D -- "Input: Property Value"
    D -- "Output: Updated JSON" --> E[Deliver via Webhook Task] -- "Input: Body"
```

*   The "Navigate URL" task takes a URL as input and outputs a "Web page" (an instance of the browser page).
*   The "Page to HTML" task takes the "Web page" output from the previous task as its input and outputs the HTML content of that page.
*   The "Extract Text from Element" task takes the HTML as input and outputs the specific text it found (like the price).
*   And so on...

This flow of outputs becoming inputs is how Tasks link together to perform the complete workflow.

### Types of Tasks

`agentic-workflow` comes with a variety of pre-built Task types that you can use. These cover common actions needed for automation, especially web automation and data handling.

You can see some of these Task types listed in the `TaskMenu.tsx` file, which defines the menu on the left side of the workflow editor where you can drag and drop tasks:

```typescript
// Inside app/workflow/_components/TaskMenu.tsx (simplified)
import { TaskType } from "@/types/task";

// ... other imports ...

export default function TaskMenu() {
  return (
    // ... Accordion structure ...
    <AccordionItem value="interactions">
      <AccordionTrigger className="font-bold">User interactions</AccordionTrigger>
      <AccordionContent className="flex flex-col gap-1">
        <TaskMenuBtn taskType={TaskType.NAVIGATE_URL} /> // Go to a URL
        <TaskMenuBtn taskType={TaskType.FILL_INPUT} /> // Type into a form field
        <TaskMenuBtn taskType={TaskType.CLICK_ELEMENT} /> // Click a button/link
        {/* ... other interaction tasks ... */}
      </AccordionContent>
    </AccordionItem>
    <AccordionItem value="extraction">
      <AccordionTrigger className="font-bold">Data extraction</AccordionTrigger>
      <AccordionContent className="flex flex-col gap-1">
        <TaskMenuBtn taskType={TaskType.PAGE_TO_HTML} /> // Get page source code
        <TaskMenuBtn taskType={TaskType.EXTRACT_TEXT_FROM_ELEMENT} /> // Find text by selector
        <TaskMenuBtn taskType={TaskType.EXTRACT_DATA_WITH_AI} /> // Use AI to find data
        {/* ... other extraction tasks ... */}
      </AccordionContent>
    </AccordionItem>
    {/* ... other categories like storage, timing, results, etc. ... */}
  );
}
```

This code snippet shows how the `TaskMenu` component groups different available `TaskType`s. Each `TaskType` corresponds to a specific action. The full list of defined `TaskType`s can be seen in `types\task.ts`:

```typescript
// Inside types\task.ts (simplified)
export enum TaskType {
  // Browser Tasks
  LAUNCH_BROWSER = "LAUNCH_BROWSER",
  NAVIGATE_URL = "NAVIGATE_URL",
  PAGE_TO_HTML = "PAGE_TO_HTML",
  EXTRACT_TEXT_FROM_ELEMENT = "EXTRACT_TEXT_FROM_ELEMENT",
  // ... many others ...
  DELIVER_VIA_WEBHOOK = "DELIVER_VIA_WEBHOOK",
  // ... other task types ...
  AGENT = "AGENT", // Special type for AI Agents (more in next chapter)
}
```

This `enum` is just a list of names (like unique IDs) for each type of Task the system knows about.

### Inside a Task Definition

Each Task type has a definition that describes:
*   Its unique `type` (from the `TaskType` enum).
*   A human-readable `label` (what you see in the editor).
*   An `icon` to represent it visually.
*   Whether it can be a starting point (`isEntryPoint`).
*   How many `credits` it costs to run.
*   The list of its `inputs`.
*   The list of its `outputs`.

Here's a simplified look at the definition for the `NavigateUrlTask` from `NavigateUrlTask.tsx`:

```typescript
// Inside lib/workflow/task/NavigateUrlTask.tsx (simplified)
import { TaskParamType, TaskType } from "@/types/task";
import { WorkflowTask } from "@/types/workflow";
import { Link2Icon } from "lucide-react";

export const NavigateUrlTask = {
  type: TaskType.NAVIGATE_URL,
  label: "Navigate Url",
  icon: Link2Icon,
  credits: 2,
  inputs: [
    {
      name: "Web page", // Needs an existing browser page
      type: TaskParamType.BROWSER_INSTANCE, // Special type for browser context
      required: true,
    },
    {
      name: "URL", // Needs the URL to navigate to
      type: TaskParamType.STRING, // Simple text
      required: true,
    },
  ],
  outputs: [
    {
      name: "Web page", // Outputs the updated browser page
      type: TaskParamType.BROWSER_INSTANCE,
    },
  ],
} satisfies WorkflowTask;
```

This definition clearly shows:
*   It's the `NAVIGATE_URL` task.
*   It's called "Navigate Url".
*   It uses the `Link2Icon`.
*   It costs 2 credits.
*   It requires two inputs: a "Web page" (an active browser session) and a "URL" (a text string). Both are `required`.
*   It produces one output: the same "Web page" instance, but now navigated to the new URL.

The visual editor uses these definitions (stored in something called the `TaskRegistry`, which we'll cover later) to draw the nodes, show their labels, icons, inputs, and outputs.

```typescript
// Inside app/workflow/_components/nodes/NodeComponent.tsx (simplified)
import { TaskRegistry } from "@/lib/workflow/task/registry";
import { AppNodeData } from "@/types/appNode";
import { NodeProps } from "@xyflow/react";
import { memo } from "react";

const NodeComponent = memo((props: NodeProps) => {
  const nodeData = props.data as AppNodeData; // Data for this node instance
  const task = TaskRegistry[nodeData.type]; // Look up the task definition

  if (!task) { /* handle error */ }

  return (
    // ... render the node card ...
      <NodeHeader taskType={nodeData.type} nodeId={props.id} /> // Show label/icon from definition
      <NodeInputs>
        {task.inputs.map((input) => ( // Render input handles and fields based on definition
          <NodeInput key={input.name} input={input} nodeId={props.id} />
        ))}
      </NodeInputs>
      <NodeOutputs>
        {task.outputs.map((output) => ( // Render output handles based on definition
          <NodeOutput key={output.name} output={output} />
        ))}
      </NodeOutputs>
    // ...
  );
});
```

This component takes the data associated with a specific node instance in the workflow (`nodeData`), finds its corresponding task definition in the `TaskRegistry`, and then uses that definition (the `task` variable) to display the correct label, icon, inputs, and outputs for that node.

### Conclusion

In this chapter, you learned that **Tasks** are the individual steps or building blocks of a Workflow. Each Task performs a specific action and has defined inputs (what it needs) and outputs (what it produces). In the visual editor, Tasks are represented as nodes that you connect, showing how the outputs of one Task flow into the inputs of another to form the automation sequence.

The next chapter will introduce a special and powerful type of Task: **[AI Agents](03_ai_agents_.md)**, which can perform more complex actions using artificial intelligence.

[AI Agents](03_ai_agents_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)