# Chapter 1: User Authentication & Authorization

Welcome to the **ai-course** project! In this first chapter, we're diving into a super important part of any online platform: **User Authentication & Authorization**.

Imagine our **ai-course** platform is like an exclusive club. We need a way to know who's allowed in and, once inside, what parts of the club they can access. This is exactly what User Authentication & Authorization does for our platform. It acts as the platform's security guard, managing who can enter and what areas they're allowed to access. It handles things like user logins, new registrations, password resets, and verifies each user's identity and assigned role (like an admin or a regular student) to make sure they only interact with features appropriate for their permissions. It's the secure foundation for our entire application.

### The Big Problem: Knowing Who You Are

When you visit a website, how does it know it's *you*? And once it knows it's you, how does it decide what you're allowed to see or do? This is the core problem Authentication and Authorization solve.

Let's think about a very common task: **logging in to the ai-course platform.**

Our goal for this chapter is to understand how, when you enter your email and password, the system verifies your identity and grants you access.

### Key Concepts: Authentication vs. Authorization

These two terms sound similar but mean different things. Let's break them down:

1.  **Authentication (Who are you?)**: This is the process of proving your identity.
    *   **Analogy**: Think of showing your ID card to a bouncer at the club entrance. You're proving you are who you say you are.
    *   **In our project**: This involves actions like:
        *   **Logging In**: Providing an email and password.
        *   **Registering**: Creating a new account.
        *   **Password Reset**: Proving you own an account to change its password.

2.  **Authorization (What can you do?)**: This is about determining what actions an authenticated user is *allowed* to perform.
    *   **Analogy**: Once inside the club, your ID might also indicate if you're a "VIP" (allowing access to special areas) or a "regular guest."
    *   **In our project**: This involves checking a user's `role` (e.g., `admin`, `user`). An admin might be able to create new courses, while a regular user can only view them.

### Solving the Use Case: Logging In

Let's focus on our main use case: a user logging in to the **ai-course** platform.

When you go to the login page (like `/auth/sign-in`), you see fields for your email and password. When you click "Sign In," here's what happens from the user's perspective:

#### How the Frontend Handles Login (Simplified)

The login page (`app\auth\sign-in\[[...sign-in]]\page.jsx`) is a React component that manages the email and password you type. When you submit the form, it sends this information to our backend server.

```javascript
// From: app\auth\sign-in\[[...sign-in]]\page.jsx
// Simplified code snippet focusing on the login submission
import { useState } from 'react';

export default function LocalSignInPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState(null);
  // ... other state and imports

  const handleSubmit = async (e) => {
    e.preventDefault(); // Stop the browser from refreshing
    setError(null); // Clear any previous errors

    try {
      const res = await fetch('/api/auth/local-login', { // <-- IMPORTANT: This sends data to our login API!
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password }) // Send email and password
      });

      if (!res.ok) {
        const data = await res.json();
        setError(data.error || 'Login failed.'); // Show error if login fails
      } else {
        // If login is successful, the server sets a cookie,
        // and we can then redirect the user.
        console.log('Login successful!');
        // router.replace('/dashboard'); // We will redirect the user
      }
    } catch (err) {
      setError('An unexpected error occurred.');
    }
  };

  return (
    // ... JSX for the login form ...
    <form onSubmit={handleSubmit}>
      {/* Input fields for email and password */}
      <button type="submit">Sign In</button>
    </form>
  );
}
```

**Explanation**: This code snippet shows that when you click "Sign In", your browser sends your email and password to a special address on our server: `/api/auth/local-login`. It's like sending a secret message to the bouncer.

**Input**: Your email and password.
**Output**: If successful, you're logged in and sent to the dashboard. If not, you see an error message.

### Under the Hood: How Login Works (Internal Implementation)

Now, let's peek behind the curtain and see what happens on the server when `/api/auth/local-login` is called.

#### High-Level Walkthrough

Here’s a simplified step-by-step process of what happens when you try to log in:

```mermaid
sequenceDiagram
    participant User
    participant Frontend as Website (Your Browser)
    participant BackendAPI as Server (Auth API)
    participant Database

    User->>Frontend: Enters Email & Password, Clicks Login
    Frontend->>BackendAPI: Sends login request (/api/auth/local-login)
    BackendAPI->>Database: "Is this email registered?"
    Database-->>BackendAPI: Sends user info (or "not found")
    alt User Found & Password Correct
        BackendAPI->>BackendAPI: Verifies password & Creates a "Session Token"
        BackendAPI-->>Frontend: Sends success message + Session Token (in a cookie)
        Frontend->>Frontend: Stores Session Token
        Frontend->>User: Redirects to Dashboard
    else User Not Found or Incorrect Password
        BackendAPI-->>Frontend: Sends error message
        Frontend->>User: Displays error
    end
```

**Explanation**:
1.  **You (User)** type your email and password into the website.
2.  Your **Website (Frontend)** sends this information to our **Server's Authentication API (BackendAPI)**.
3.  The **BackendAPI** asks the **Database**: "Do I have a user with this email?"
4.  The **Database** responds with the user's information if found.
5.  The **BackendAPI** then checks if the password you provided matches the one stored for that user.
6.  If everything matches, the **BackendAPI** creates a special "session token" – a kind of digital ID badge – and tells your **Website** to store it. This token is crucial because it proves you're logged in without sending your password every time.
7.  Finally, your **Website** redirects you to the dashboard, and you're in! If there's a problem (wrong email/password), the server sends an error message instead.

#### Code Deep Dive: The Server's Role

The main brain for our local login process is located in `app\api\auth\local-login\route.js`. Let's look at its key parts.

First, it receives your email and password:

```javascript
// From: app\api\auth\local-login\route.js
import { NextResponse } from 'next/server';
import { Client } from 'pg'; // For connecting to our PostgreSQL database
import bcrypt from 'bcryptjs'; // To securely compare passwords
import jwt from 'jsonwebtoken'; // To create secure tokens

export async function POST(req) {
  try {
    const { email, password } = await req.json(); // Get email and password from the request
    if (!email || !password) {
      return NextResponse.json({ error: 'Email and password are required.' }, { status: 400 });
    }
    // ... database connection setup ...
  } catch (err) {
    // ... error handling ...
  }
}
```

**Explanation**: The server expects an email and password. It quickly checks if both are provided. If not, it sends an error back.

Next, it connects to the database and looks up the user:

```javascript
// From: app\api\auth\local-login\route.js
// ... (imports and initial checks) ...
const DB_CONNECTION_STRING = process.env.NEXT_PUBLIC_DB_CONNECTION_STRING || process.env.DATABASE_URL;
if (!DB_CONNECTION_STRING) return NextResponse.json({ error: 'DB connection string missing.' }, { status: 500 });

const client = new Client({ connectionString: DB_CONNECTION_STRING });
await client.connect(); // Connect to the database

// Find user by email
const userRes = await client.query('SELECT * FROM users WHERE email = $1', [email]);
if (userRes.rows.length === 0) {
  await client.end();
  return NextResponse.json({ error: 'Invalid email or password.' }, { status: 401 });
}
const user = userRes.rows[0]; // Get the found user's data
// ... (password comparison and token generation) ...
```

**Explanation**: It connects to our PostgreSQL database. Then, it runs a database query to find a user matching the provided email. If no user is found, it sends an error because the email isn't recognized.

Now, for the critical security step: password comparison. We use `bcrypt` for this. It's a special tool that compares the password you type with a *hashed* (scrambled) version of your password stored in the database, without ever revealing your actual password.

```javascript
// From: app\api\auth\local-login\route.js
// ... (imports, initial checks, database connection, user lookup) ...
const user = userRes.rows[0];

// Compare password securely
const valid = await bcrypt.compare(password, user.password);
if (!valid) {
  await client.end();
  return NextResponse.json({ error: 'Invalid email or password.' }, { status: 401 });
}
// ... (token generation and cookie setting) ...
```

**Explanation**: `bcrypt.compare` checks if the plain-text password you provided matches the stored hashed password. If `valid` is `false`, it means the password is wrong, and an error is sent.

If the password is correct, the server generates a **JSON Web Token (JWT)**. This token is like that digital ID badge. It contains some basic user information (like `id`, `email`, `role`) and is signed so that our server can verify it hasn't been tampered with. It also has an expiration date, just like a real ID badge expires.

```javascript
// From: app\api\auth\local-login\route.js
// ... (imports, initial checks, database connection, user lookup, password comparison) ...
const user = userRes.rows[0]; // Already verified user

// Generate JWT (JSON Web Token)
const jwtSecret = process.env.JWT_SECRET || 'atlas_dev_secret';
const token = jwt.sign({
  id: user.id,
  email: user.email,
  name: user.name,
  username: user.username,
  role: user.role
}, jwtSecret, { expiresIn: '7d' }); // Token valid for 7 days

await client.end(); // Close the database connection

// Set the token as an HTTP-only cookie
const response = NextResponse.json({ success: true, token });
response.cookies.set('atlas_token', token, {
  httpOnly: true, // IMPORTANT: Makes it inaccessible to client-side JavaScript for security
  sameSite: 'lax',
  path: '/',
  maxAge: 60 * 60 * 24 * 7 // 7 days in seconds
});
return response; // Send the response with the cookie
```

**Explanation**: The `jwt.sign` function creates the token, embedding user details. This token is then sent back to your browser as a **cookie** named `atlas_token`. Cookies are small pieces of data websites store in your browser. By setting `httpOnly: true`, we make sure this token can only be accessed by the server, which is a key security measure.

#### Checking Your Identity (Session Check)

Once you're logged in, every time your browser makes a request to our server, it automatically sends this `atlas_token` cookie. Our server can then verify this token to know who you are and if you're still authenticated.

The `app\api\auth\session-check\route.js` and `app\api\auth\me\route.js` files handle this. They extract the token and use a `verifyJWT` function (from `lib/auth.js`, which uses `jsonwebtoken.verify`) to decode and validate it.

```javascript
// From: app\api\auth\me\route.js (simplified)
import { verifyJWT } from '@/lib/auth';
import { NextResponse } from 'next/server';
import { getTokenFromRequest } from '@/app/_utils/getTokenFromJWT';

export async function GET(request) {
  const token = getTokenFromRequest(request); // Gets 'atlas_token' from cookies
  if (!token) return NextResponse.json({ error: 'No token' }, { status: 401 });

  try {
    const user = await verifyJWT(token); // Decodes and verifies the token
    if (!user) return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    
    // Check user role for authorization
    const isAdmin = user.role === 'admin';
    
    return NextResponse.json({ 
      user: { ...user, isAdmin } // Returns authenticated user data
    });
  } catch (err) {
    // ... error handling ...
  }
}
```

**Explanation**: This API route is used to check if a user is currently logged in and to retrieve their information. It extracts the `atlas_token` cookie, verifies its authenticity, and then extracts the user's details (like `id`, `email`, `name`, `role`). This is how our system knows if you're a regular user or an `admin` for [Admin Panel & Database Management](07_admin_panel___database_management_.md) features.

#### Logging Out

Logging out is simple! It involves clearing that `atlas_token` cookie from your browser.

```javascript
// From: app\api\auth\logout\route.js (simplified)
import { NextResponse } from 'next/server';

export const runtime = 'edge'; // For better performance

export async function POST(request) {
  const response = NextResponse.json({ success: true, message: 'Successfully logged out' });
  
  // Remove the JWT cookie by setting it to empty and expiring it immediately
  response.cookies.set('atlas_token', '', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/',
    expires: new Date(0), // Set to 1970-01-01 to expire immediately
    maxAge: 0 // Set max age to 0 for immediate expiration
  });

  console.log('[LOGOUT] Authentication cookies cleared');
  return response;
}
```

**Explanation**: This code tells your browser to remove the `atlas_token` cookie by setting its expiration date to a time in the past (`new Date(0)`). Once the token is gone, the server no longer recognizes you as logged in.

### Other Authentication Features

Beyond logging in and out, our system also supports:

*   **Registration**: New users can create accounts using the `/auth/register` page, which calls the `app\api\auth\register\route.js` endpoint to store their details in the database.
*   **Password Reset**: If you forget your password, the `/auth/forgot-password` page lets you request a reset. This sends an email with a special link (handled by `app\api\auth\request-password-reset\route.js`). Clicking that link takes you to `/reset-password`, where you can set a new password, which is then handled by `app\api\auth\reset-password\route.js`.

### Conclusion

In this chapter, we've explored the fundamental concepts of User Authentication and Authorization. We learned that Authentication is about proving *who you are* (like logging in), and Authorization is about determining *what you can do* once your identity is confirmed. We walked through the process of logging in, from the user's input on the frontend to the server's database checks, password verification, and the creation of a secure session token via cookies. This system is the bedrock that keeps our **ai-course** platform secure and ensures users can only access what they're supposed to.

Now that we understand how users get into the system, let's move on to managing the core content: courses and chapters!

**Next Chapter**: [Course & Chapter Management](02_course___chapter_management_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)