# Chapter 1: User Authentication (Clerk)

Welcome to the Atlas University LMS AI project! In this first chapter, we're going to dive into a crucial part of any online application: knowing who's who! Imagine you're building a special online library where every student has their own locker filled with unique study materials. How do you make sure only *they* can open their locker and access *their* books? That's where **User Authentication** comes in!

Think of **Clerk** as the friendly, super-efficient security guard for our Atlas LMS AI. When you visit our online learning platform, <PERSON> is in charge of everything related to your user account. This includes:

*   **Signing Up:** When you're a new student joining the library.
*   **Logging In:** When you return to the library and need to show your ID.
*   **Managing Your Profile:** If you need to update your name or change your password.

Clerk ensures that your personal study materials and settings are private and secure, just like your locker at the library. When you sign up, <PERSON> gives you a unique ID, and every time you log in, it checks that ID to make sure it's really you.

---

### How Does Clerk Help Us? (Solving the Locker Problem)

Our main goal is to let users create accounts, log in, and access their personal dashboard. Clerk makes this incredibly easy!

#### 1. Letting Users Sign Up and Log In

Clerk provides ready-to-use "pages" for signing up and logging in. We don't have to build complex forms from scratch!

Here's how we tell our app to show <PERSON>'s sign-in page:

```jsx
// File: app\(auth)\sign-in\[[...sign-in]]\page.jsx

import { SignIn } from '@clerk/nextjs' // Import Clerk's SignIn component

export default function Page() {
  return (
    <div className='flex items-center justify-center h-screen'>
      <SignIn /> {/* Just use the SignIn component! */}
    </div>
  )
}
```

**What's happening here?** This small piece of code tells our application: "Hey, when someone goes to the sign-in page, just show Clerk's built-in login form." Clerk handles all the details like collecting email/password, showing "forgot password" links, and even allowing social logins (like with Google or GitHub).

Similarly, for signing up:

```jsx
// File: app\(auth)\sign-up\[[...sign-up]]\page.jsx

import { SignUp } from '@clerk/nextjs' // Import Clerk's SignUp component

export default function Page() {
  return (
    <div className='flex items-center justify-center h-screen'>
      <SignUp /> {/* Just use the SignUp component! */}
    </div>
  )
}
```

**And for signing up:** This is the same idea as sign-in. Clerk provides a pre-built sign-up form that lets new users create an account effortlessly.

#### 2. Protecting Private Pages

Once a user logs in, we want them to see their personalized "Dashboard" with their courses. We *only* want logged-in users to see this page.

Here's how we protect the dashboard:

```jsx
// File: app\dashboard\page.jsx

"use client" // This tells Next.js it's a client-side component

import { useUser } from '@clerk/nextjs' // Get user info from Clerk
import { useRouter } from 'next/navigation'; // For redirecting

function Dashboard() {
  const { user, isLoaded } = useUser(); // user holds info if logged in, isLoaded tells us if Clerk is ready
  const router = useRouter();

  // If Clerk is ready AND there's no user, redirect to sign-in page
  if (isLoaded && !user) {
    router.push('/sign-in');
    return null; // Don't show anything until redirect
  }

  // If we reach here, user is loaded and logged in!
  return (
    <div>
      {/* ... show dashboard content like WelcomeBanner and CourseList ... */}
    </div>
  )
}

export default Dashboard
```

**How it works:**
*   `useUser()` is a special "hook" from Clerk that tells us if someone is logged in and, if so, gives us their details (like their name, email, etc.).
*   We check `isLoaded` to make sure Clerk has finished checking.
*   If `isLoaded` is true and `user` is `null` (meaning no one is logged in), we use `router.push('/sign-in')` to send them straight back to the sign-in page. This keeps our dashboard secure!

#### 3. Showing User Profile

Clerk also gives us a simple way to display and manage a user's profile:

```jsx
// File: app\dashboard\profile\page.jsx

"use client"
import { UserProfile } from '@clerk/nextjs' // Clerk's UserProfile component
import React from 'react'

function Profile() {
  return (
    <div>
        <UserProfile/> {/* Simply embed the profile component */}
    </div>
  )
}

export default Profile
```

**What it does:** The `UserProfile` component automatically displays all the user's information managed by Clerk, like their email addresses, connected accounts, and security settings.

---

### What Happens Behind the Scenes? (The Security Guard in Action)

Let's look at how Clerk works with our application on a deeper level.

Imagine this sequence of events when a new user signs up:

```mermaid
sequenceDiagram
    participant User
    participant AtlasLMS as Atlas LMS AI (Your App)
    participant ClerkAPI as Clerk (Authentication Service)
    participant AtlasDB as Atlas DB (Our Database)

    User->>AtlasLMS: Tries to access Atlas LMS AI
    AtlasLMS->>ClerkAPI: "Hey, Clerk! Is this user logged in?"
    ClerkAPI-->>AtlasLMS: "Not yet! User needs to sign up."
    AtlasLMS->>User: Redirects to Clerk's Sign Up page

    User->>ClerkAPI: Fills Sign Up form (Email, Password)
    ClerkAPI->>ClerkAPI: Creates User Account securely
    ClerkAPI-->>AtlasLMS: "User signed up! Here's their info."

    AtlasLMS->>AtlasDB: "Is this new user already in our database?"
    AtlasDB-->>AtlasLMS: "No, they're brand new!"
    AtlasLMS->>AtlasDB: Saves new user's basic info (Email, Name)
    AtlasDB-->>AtlasLMS: User added to our database!

    AtlasLMS->>User: Redirects to Dashboard (now logged in)
```

This diagram shows two key interactions:
1.  **Clerk handles the direct user interaction for sign-up/sign-in.** Our app just sends the user to Clerk's pages.
2.  **Our app then talks to Clerk to get user information.** Once Clerk confirms who the user is, our app can then check if this user exists in *our own* database. If not, we add them! This is important because while Clerk manages the *authentication* (who you are), we still need to store *our application's specific data* about that user (like their generated courses or payment status) in our own database.

#### The Core Setup (`ClerkProvider`)

For Clerk to work across our entire application, we wrap our app with a special component called `ClerkProvider`. This is like setting up the main security gate for our whole library.

You'll find this setup in the `app/layout.js` file:

```jsx
// File: app\layout.js

import { ClerkProvider } from "@clerk/nextjs"; // Import Clerk's main provider

export default function RootLayout({ children }) {
  return (
    <ClerkProvider
      appearance={{ variables: { colorPrimary: '#000', }, }}
      signInUrl="/sign-in" // Where to send users for sign-in
      signUpUrl="/sign-up" // Where to send users for sign-up
      afterSignInUrl="/dashboard" // Where to go after successful sign-in
      afterSignUpUrl="/dashboard" // Where to go after successful sign-up
      publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY} // Our unique key from Clerk
      // ... other navigation settings ...
    >
      <html lang="en">
        <body>
          {/* Our application content goes here */}
          {children}
        </body>
      </html>
    </ClerkProvider>
  );
}
```

**Explanation:**
*   `ClerkProvider` makes Clerk's features available to all parts of our application.
*   `signInUrl`, `signUpUrl`, `afterSignInUrl`, and `afterSignUpUrl` tell Clerk which pages to use for authentication and where to redirect users after they log in or sign up.
*   `publishableKey` is like a secret password that connects our app to our specific Clerk account.

#### Syncing Users with Our Database (`Provider.js`)

While Clerk manages the user's identity (their email, password, profile picture), our application also needs to store some details about *our* users in *our own* database. For example, if a user is a premium member or how many courses they've created.

This is handled in `app/provider.js`:

```jsx
// File: app\provider.js

"use client" // This means it runs in the user's browser

import { db } from '@/configs/db'; // Our connection to the database
import { USER_TABLE } from '@/drizzle/schema'; // Our user table definition
import { useUser } from '@clerk/nextjs' // To get current user details from Clerk
import { eq } from 'drizzle-orm'; // A tool to compare values in the database
import React, { useEffect } from 'react'

function Provider({ children }) {
    const { user } = useUser(); // Get the currently logged-in user from Clerk

    // This code runs whenever the 'user' information from Clerk changes (e.g., after login)
    useEffect(() => {
        user && CheckIsNewUser(); // If 'user' exists, run CheckIsNewUser
    }, [user]) // Only re-run when 'user' object changes

    /**
     * Checks if the user is new and adds them to our database if they are.
     */
    const CheckIsNewUser = async () => {
        // Look up user in our database using their email from Clerk
        const result = await db.select().from(USER_TABLE)
            .where(eq(USER_TABLE.email, user?.primaryEmailAddress?.emailAddress))

        // If no user found in our database (meaning result is empty)
        if (result?.length == 0) {
            // Add the new user's name and email to our database
            await db.insert(USER_TABLE).values({
                name: user?.fullName || 'Atlas User', // Use Clerk's full name or a default
                email: user?.primaryEmailAddress?.emailAddress
            })
        }
    }

    return (
        <div className='page-transition'>
            {children} {/* Our entire application */}
        </div>
    )
}

export default Provider
```

**Breaking it down:**
*   This `Provider` component acts like a helpful assistant that runs when our app starts.
*   It uses `useUser()` (from Clerk) to get information about the logged-in person.
*   The `useEffect` block makes sure that `CheckIsNewUser()` runs as soon as we know who the `user` is from Clerk.
*   `CheckIsNewUser()` is super important! It checks our application's own database (using `db.select().from(USER_TABLE)`) to see if this user's email already exists.
*   If the user's email is *not* found in our database (`result?.length == 0`), it means they are a brand new user to our system. So, we `insert` their basic details (name and email) into `USER_TABLE`. This ensures that every user who logs in via Clerk also has a corresponding entry in our system, which we can use for features like tracking their courses or managing their subscription.
*   This process highlights how Clerk handles the **"who you are"** part, while our database (using [Data Persistence (Drizzle ORM)](02_data_persistence__drizzle_orm__.md)) handles the **"what you have done in our app"** part.

---

### Conclusion

In this chapter, we've learned that **Clerk** is our go-to solution for all things related to user accounts in Atlas LMS AI. It makes signing up, logging in, and managing profiles incredibly easy and secure, like having a dedicated security team for our application. We saw how simple it is to integrate Clerk's pre-built components and how we use Clerk's `useUser()` hook to protect pages and even sync user information with our own database.

Next, we'll dive into how we actually store all that important user data, along with their study materials and courses, in our very own database. Get ready to learn about [Data Persistence (Drizzle ORM)](02_data_persistence__drizzle_orm__.md)!

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)