# Chapter 5: Notifications & Communications

Welcome back to the `mytask` journey! In [Chapter 1: User Interface Components & Pages](01_user_interface_components___pages_.md), we learned how `mytask` builds all the visual parts you see. Then, in [Chapter 2: User & Authentication System](02_user___authentication_system_.md) and [Chapter 3: Atlas University Integration](03_atlas_university_integration_.md), we explored how `mytask` knows who you are and keeps your information secure. And most recently, in [Chapter 4: Task Management System](04_task_management_system_.md), we dove into the core of the application: how you create, organize, and track your tasks.

Now that tasks are flying around, how do we make sure everyone knows what's happening? Imagine you assign an urgent task to a colleague, or a task you're working on gets a new deadline. How does `mytask` tell you about these important updates without you having to constantly check the app? This is exactly the problem that **Notifications & Communications** solves!

### The Problem: Staying Informed in a Busy World

Think of a bustling office where everyone is working on different parts of a big project. If someone finishes their part, or if a new urgent task comes up, how does everyone involved find out quickly?

*   Do they yell across the office?
*   Do they send individual text messages?
*   Do they keep refreshing a giant shared spreadsheet every minute?

Without a good way to communicate important updates, things get missed, deadlines are forgotten, and people end up working on old information. This is messy, slow, and leads to mistakes. `mytask` faces the same challenge: how to efficiently inform users about crucial events related to their tasks.

### Solution: Your Intelligent Messenger Service

`mytask` solves this problem by acting as an intelligent messenger service. It ensures that critical information reaches the right people at the right time. It does this in two main ways:

1.  **In-App Notifications:** These are like little alert messages that pop up or appear within `mytask` itself, letting you know about updates when you're using the application.
2.  **External Communications (Emails & Push Notifications):** For very important events, `mytask` can also send emails or push notifications (if set up) to ensure you get the message even when you're not actively using the application. It uses a special bridge to the [Atlas University System](03_atlas_university_integration_.md) to send these.

Let's look at a common example: "Receiving a New Task Assignment Notification."

#### Central Use Case: Receiving a New Task Assignment Notification

Imagine you are a team leader, and you create a new task: "Finalize Project Report," and you assign it to your team member, Alice.

Here's what happens:

1.  **You create the task:** In `mytask`, you fill out the "Add Task" form, including assigning Alice to the task.
2.  **`mytask` acts:** As soon as you save the task, `mytask` realizes Alice needs to know about this.
3.  **Alice gets informed:** Alice might see a small notification pop up inside `mytask`, or she might receive an email in her inbox, alerting her to the new assignment.

### How it All Works Together (Under the Hood)

Let's trace the journey of a task assignment becoming a notification for Alice.

```mermaid
sequenceDiagram
    participant You
    participant MyTaskApp as MyTask App (Browser)
    participant MyTaskServer as MyTask Server
    participant NotisDB as Notifications Database
    participant AtlasAPI as Atlas University Communication System
    participant Alice

    You->>MyTaskApp: Create/Assign Task to Alice
    MyTaskApp->>MyTaskServer: Send "Create Task" request
    MyTaskServer->>NotisDB: Create In-App Notice for Alice
    MyTaskServer->>MyTaskApp: Task Created Confirmation
    MyTaskApp->>AtlasAPI: Send External Notification Request (for Alice)
    AtlasAPI-->>Alice: Send Email / Push Notification
    MyTaskApp->>Alice: Display In-App Notification
```

**Step-by-step Explanation:**

1.  **You assign the task:** You, the creator, use the `mytask` application in your web browser to create a new task and assign it to Alice.
2.  **App sends request:** Your `mytask` application sends this "Create Task" request to the `mytask` server.
3.  **Server saves task & creates in-app notice:** The `mytask` server does two important things:
    *   It saves the new task details to the main task database (as we saw in [Chapter 4: Task Management System](04_task_management_system_.md)).
    *   It then creates a new "Notice" record in a special **Notifications Database** specifically for Alice. This is for the in-app notification.
4.  **Server confirms task creation:** The server sends a confirmation back to your `mytask` application.
5.  **App requests external notification:** Your `mytask` application, upon confirmation, takes Alice's information and sends a request to the **Atlas University Communication System** (our bridge to external notifications, leveraging the [Atlas University Integration](03_atlas_university_integration_.md)).
6.  **Atlas sends notification:** The Atlas system processes this request and sends an actual email to Alice's university email address or a push notification to her device (if she has the Atlas app installed and configured).
7.  **Alice gets notified:** Alice receives the external notification (email/push) and, the next time she opens `mytask`, she also sees the in-app notification.

### Diving Deeper: The Code Behind Notifications

Let's look at how `mytask` achieves this.

#### 1. What is a "Notice" in `mytask`? (The Blueprint for In-App Notifications)

`mytask` stores a record for each in-app notification using a "Notice" blueprint. This tells `mytask` what to display in your notification feed.

```javascript
// server\models\notis.js
import mongoose, { Schema } from "mongoose";

const noticeSchema = new Schema(
  {
    team: [{ type: Schema.Types.ObjectId, ref: "User" }], // Who should see this notice
    text: { type: String },                               // The message itself
    task: { type: Schema.Types.ObjectId, ref: "Task" },   // Which task it's about
    notiType: { type: String, default: "alert" },         // Type of notice (alert, message)
    isRead: [{ type: Schema.Types.ObjectId, ref: "User" }], // Who has read it
  },
  { timestamps: true } // Adds creation and update times
);

const Notice = mongoose.model("Notice", noticeSchema);
export default Notice;
```
**Explanation:** This `noticeSchema` defines the structure for in-app notices. When a task is assigned, a new "Notice" document is created in the database. The `team` field lists the users who should receive this in-app notification (e.g., Alice's user ID). The `text` contains the message (e.g., "You have been assigned a new task...").

#### 2. When to Create an In-App Notice (Server-Side)

Whenever a significant event happens with a task on the `mytask` server, it decides if an in-app notice is needed.

```javascript
// server\controllers\taskController.js (Simplified createTask)
import Notice from "../models/notis.js"; // Our Notice blueprint
// ... other imports

const createTask = asyncHandler(async (req, res) => {
  try {
    const { userId } = req.user; // Who created the task
    const { title, description, team, date, priority, parentId } = req.body;
    // ... code to prepare taskData and create the task (as seen in Chapter 4)

    const task = await Task.create(taskData); // Save the new task to the database

    // --- NEW: Create an in-app notice for the team ---
    // Get user's language for the notification text
    const userLanguage = req.headers['accept-language']?.split(',')[0]?.split('-')[0] || 'en';
    let text = ""; // Prepare the notification message
    if (team?.length > 1) {
      text = getTranslation('newTaskAssignedMultiple', userLanguage).replace('{count}', team.length - 1);
    } else {
      text = getTranslation('newTaskAssigned', userLanguage);
    }
    // Add priority and date details to the message
    const formattedDate = new Date(date).toLocaleDateString(userLanguage === 'tr' ? 'tr-TR' : 'en-US');
    const priorityInfo = getTranslation('taskPriorityInfo', userLanguage).replace('{priority}', priority);
    const dateInfo = getTranslation('taskDateInfo', userLanguage).replace('{date}', formattedDate);
    text = `${text}. ${priorityInfo}. ${dateInfo}.`;

    await Notice.create({
      team,         // All members assigned to the task get the notice
      text,         // The message generated above
      task: task._id, // Link to the specific task
    });
    // --- END NEW ---

    res.status(200).json({ status: true, task, message: "Task created successfully." });
  } catch (error) {
    // ... error handling
  }
});
```
**Explanation:** After a task is successfully created (or updated, duplicated, restored from trash), the `taskController.js` specifically calls `Notice.create`. It builds a message (`text`) that includes details like priority and due date, translated into the user's preferred language. This `Notice` is saved, and `mytask` (client-side) can then fetch and display it to the relevant `team` members as an in-app notification.

You can also see a similar `Notice.create` call in `duplicateTask` and `restoreTask` functions in `taskController.js`. For `restoreTask`, there's also a helper function `notifyTeamMembers` which, in a full implementation, would trigger actual email/push notifications.

#### 3. Communicating with Atlas for External Notifications (Client-Side)

For emails and push notifications, `mytask` leverages the existing connection to the [Atlas University System](03_atlas_university_integration_.md). The client-side (your browser) uses its Atlas token (acquired during login) to send requests to Atlas's communication services.

```javascript
// client\src\redux\slices\api\atlasNotificationApiSlice.js (Simplified)
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { getAtlasToken } from "../../../utils/cookieUtils"; // Helps get Atlas token

const ATLAS_COMMUNICATION_API_BASE_URL = "https://apicommunication.atlas.edu.tr/api";

export const atlasNotificationApiSlice = createApi({
  reducerPath: 'atlasNotificationApi',
  baseQuery: fetchBaseQuery({
    baseUrl: ATLAS_COMMUNICATION_API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      let atlasToken = getAtlasToken(); // Get Atlas token securely from cookies
      if (atlasToken) {
        headers.set('Authorization', `Bearer ${atlasToken}`); // Add token to request
      }
      headers.set('Content-Type', 'application/json');
      return headers;
    }
  }),
  endpoints: (builder) => ({
    sendFcmNotification: builder.mutation({ // For push notifications
      query: (notificationData) => ({
        url: '/Myapi/bildirim/gonder',
        method: 'POST',
        body: notificationData,
      }),
    }),
  }),
});

export const { useSendFcmNotificationMutation } = atlasNotificationApiSlice;
```
**Explanation:** This part of the code defines how `mytask` talks to Atlas University's notification services. It has a `sendFcmNotification` function which is ready to send push notifications. Notice the `prepareHeaders` part: this is where `mytask` retrieves the special Atlas token (your "ID card" for Atlas, from [Chapter 3: Atlas University Integration](03_atlas_university_integration_.md)) and adds it to the request, so Atlas knows it's a legitimate request from `mytask`. There's also a similar hidden endpoint for sending emails.

#### 4. The `notificationUtils.js` - Orchestrating External Communications (Client-Side)

On the client side, a helper file brings everything together to send these external notifications.

```javascript
// client\src\utils\notificationUtils.js (Simplified)
// We import store and API slice dynamically here to avoid complex setups.

export const sendTaskNotificationToTeam = async (teamMembers, taskData, currentUser) => {
  try {
    // 1. Get required tools (Redux store, Atlas API slice)
    const storeInstance = await getStore(); // dynamically loads Redux store
    const apiSlice = await getAtlasNotificationApiSlice(); // dynamically loads Atlas API slice

    if (!teamMembers || teamMembers.length === 0) return;

    // 2. Extract usernames/emails from team members
    const usernames = teamMembers.map(member => member.email.split('@')[0]).filter(Boolean);
    const emails = teamMembers.map(member => member.email).filter(Boolean);

    // 3. (For push notifications) Get FCM tokens for users from Atlas
    //    This involves calling Atlas API using `apiSlice.endpoints.getFcmTokenByUsername.initiate(username)`
    const fcmTokenResults = []; // Imagine this array is filled with tokens after API calls

    // 4. If tokens found, prepare notification data and send FCM notification
    if (fcmTokenResults.length > 0) {
      const notificationData = {
        baslik: `Yeni Görev: ${taskData.title}`,
        govde: `Size yeni bir görev atandı: "${taskData.title}".`,
        userFcmTokenDtos: fcmTokenResults
      };
      await storeInstance.dispatch(apiSlice.endpoints.sendFcmNotification.initiate(notificationData));
    }

    // 5. (For emails) Prepare email content (HTML message)
    const emailSubject = `Yeni Görev Atandı: ${taskData.title}`;
    const emailMessage = `
        <!DOCTYPE html>... (full HTML email content here) ...
    `;

    // 6. Send emails to each team member via Atlas Email API
    const atlasToken = getAtlasToken(); // Get Atlas token
    for (const email of emails) {
      const emailData = {
        aliciEmail: email,
        konu: emailSubject,
        mesaj: emailMessage
      };
      // This part uses a direct fetch to Atlas Communication API for emails
      await fetch('https://apicommunication.atlas.edu.tr/api/Myapi/email/gonder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${atlasToken}` },
        body: JSON.stringify(emailData)
      });
    }
    return { success: true, message: 'Notifications sent.' };
  } catch (error) {
    console.error('Error sending notifications:', error);
    return { success: false, message: 'Failed to send notifications.' };
  }
};
```
**Explanation:** This `sendTaskNotificationToTeam` function is called by the `mytask` application after a task is created or updated. It performs several key steps:
1.  It collects the email addresses (and extracts usernames) of all assigned `teamMembers`.
2.  For push notifications, it makes requests to the Atlas API (via `atlasNotificationApiSlice`) to get the specific "FCM tokens" (unique IDs for a device) for each user.
3.  Once it has the FCM tokens, it prepares the notification message (in Turkish for Atlas system) and sends it to Atlas for delivery.
4.  For emails, it constructs a complete HTML email message.
5.  Then, for each email address, it sends a separate request to the Atlas Email API to send the email. This uses the `atlasToken` to authenticate with the Atlas system.

This comprehensive approach ensures that `mytask` users are kept well-informed, whether they are actively using the application or not, making `mytask` a truly effective collaboration tool.

### Conclusion

In this chapter, we explored the **Notifications & Communications** system of `mytask`. We learned how it keeps users informed about important events like new task assignments, task updates, or restorations from the trash. `mytask` uses a smart dual approach: by creating **in-app notices** (stored in its own database) and by leveraging the **Atlas University Communication System** to send external **emails** and **push notifications**. This ensures that relevant information always reaches the right people.

Now that tasks are managed and notifications are handled, what about all the files, documents, and images related to these tasks? In the next chapter, we'll dive into how `mytask` manages all your digital assets: [File and Asset Management](06_file_and_asset_management_.md).

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)