# Chapter 7: Execution Phases

Welcome back! In the [previous chapter](06_execution_plan_.md), we delved into the **Execution Plan**, understanding how your visual workflow design (nodes and edges) is translated into a structured, step-by-step sequence of tasks that the system will follow. The Execution Plan gives the overall order.

But what happens *during* a specific run of your workflow? Imagine you run the same workflow three times. Maybe the first time it succeeds, the second time a task fails because a website is down, and the third time it runs successfully but gets slightly different data. How do you track what happened in each *individual run*? How do you see the specific inputs, outputs, and logs for *that second run* where things went wrong?

This is where **Execution Phases** come in.

### What is an Execution Phase?

During a **[Workflow Execution](05_workflow_execution_.md)** (a single instance of your workflow running), every time a **[Task](02_tasks_.md)** or **[AI Agent](03_ai_agents_.md)** node from your [Execution Plan](06_execution_plan_.md) is processed, it corresponds to an **Execution Phase**.

Think of the [Execution Plan](06_execution_plan_.md) as the script for a play, listing all the scenes in order. A **Workflow Execution** is one specific performance of that play. The **Execution Phases** are like the individual scene performances *within that specific show*. For every scene listed in the script (the plan), there's a corresponding phase in the performance (the execution).

Execution Phases are essentially **records** created for each step of a specific workflow run. They capture everything that happened when that particular task instance ran.

They store crucial information, providing a detailed look into the workflow's progress and results:

*   **Status:** Was this task run `PENDING`, `RUNNING`, `COMPLETED`, or `FAILED`?
*   **Inputs:** What specific data values did this task receive *when it ran*?
*   **Outputs:** What specific data values did this task produce *after it finished*?
*   **Logs:** Any messages (info, warnings, errors) generated by the task during its execution.
*   **Credits Consumed:** How many credits did this specific task instance cost?
*   **Timing:** When did it start and finish?

Each Execution Phase is linked directly to a specific **[Workflow Execution](05_workflow_execution_.md)** record and corresponds to one of the task nodes defined in the workflow's [Execution Plan](06_execution_plan_.md).

### Execution Phases in Action: Viewing a Run

The most common way you'll interact with the concept of Execution Phases is by viewing the details of a specific workflow run in the `agentic-workflow` application.

When you go to the "Workflow Runs" page and click on a completed or running execution, you are taken to the "Execution Details" page ([app/workflow/runs/[workflowId]/[executionId]/page.tsx](agentic-workflow/app/workflow/runs/%5BworkflowId%5D/%5BexecutionId%5D/_components/ExecutionViewer.tsx)). This page uses the data stored in the `WorkflowExecution` record and its associated `ExecutionPhase` records to show you exactly what happened.

Look at the code that fetches the data for this page:

```typescript
// Inside actions/workflows/getWorkflowExecutionWithPhases.ts (simplified)
export async function GetWorkflowExecutionWithPhases(executionId: string) {
  // ... auth check ...

  return prisma.workflowExecution.findUnique({
    where: { id: executionId, userId },
    include: {
      phases: { // *** This fetches all the associated Execution Phase records! ***
        orderBy: { number: "asc" }, // Order them based on the execution plan sequence
      },
    },
  });
}
```

This server action fetches the main `workflowExecution` record *and* all the `phases` linked to it, ordered correctly according to the [Execution Plan](06_execution_plan_.md) sequence.

The `ExecutionViewer` component (`app/workflow/runs/[workflowId]/[executionId]/_components/ExecutionViewer.tsx`) then uses this data to display the overall execution status and a list of the phases:

```typescript
// Inside app/workflow/runs/[workflowId]/[executionId]/_components/ExecutionViewer.tsx (simplified)
export default function ExecutionViewer({ initialData }: { initialData: ExecutionData }) {
  // ... fetch data using useQuery, update based on status ...

  return (
    <div className="flex w-full h-full">
      <aside className="w-[440px] ...">
        {/* ... Overall Execution Info (Status, Duration, Credits) ... */}
        <div className="overflow-auto h-full px-2 py-4">
          {query.data?.phases.map((phase, index) => ( // *** Loop through fetched phases ***
            <Button
              key={phase.id}
              className="w-full justify-between"
              variant={selectedPhase === phase.id ? "secondary" : "ghost"}
              onClick={() => setSelectedPhase(phase.id)} // Allow selecting a phase
            >
              <div className="flex items-center gap-2">
                <Badge variant={"outline"}>{index + 1}</Badge> {/* Phase number */}
                <p className="font-semibold">{phase.name}</p> {/* Task Name */}
              </div>
              {/* Show the status of this specific phase */}
              <PhaseStatusBadge status={phase.status as ExecutionPhaseStatus} />
            </Button>
          ))}
        </div>
      </aside>
      {/* ... Area to display details of the selected phase ... */}
    </div>
  );
}
```

This code shows how the UI iterates through the list of `phase` objects fetched from the database. Each button in the sidebar represents one **Execution Phase** from this specific run. You can see its number in the sequence (derived from the [Execution Plan](06_execution_plan_.md)), the name of the task it corresponds to, and its individual status. The `PhaseStatusBadge` component simply displays an icon indicating the status:

```typescript
// Inside app/workflow/runs/[workflowId]/[executionId]/_components/PhaseStatusBadge.tsx
import { ExecutionPhaseStatus } from "@/types/workflow";
import { CircleCheckIcon, CircleDashedIcon, CircleXIcon, Loader2Icon } from "lucide-react";

export default function PhaseStatusBadge({ status }: { status: ExecutionPhaseStatus }) {
  switch (status) {
    case ExecutionPhaseStatus.PENDING: return <CircleDashedIcon size={20} className="stroke-muted-foreground" />;
    case ExecutionPhaseStatus.RUNNING: return <Loader2Icon size={20} className="animate-spin stroke-yellow-500" />;
    case ExecutionPhaseStatus.FAILED: return <CircleXIcon size={20} className="stroke-destructive" />;
    case ExecutionPhaseStatus.COMPLETED: return <CircleCheckIcon size={20} className="stroke-green-500" />;
    default: return null;
  }
}
```

When you click on one of these phase buttons in the UI, you're telling the application you want to see the specific details for *that particular instance* of that task within *that specific run*.

The `ExecutionViewer` component uses another query to fetch the detailed information for the selected phase:

```typescript
// Inside app/workflow/runs/[workflowId]/[executionId]/_components/ExecutionViewer.tsx (simplified)
  const phaseDetails = useQuery({
    queryKey: ["phaseDetails", selectedPhase, query.data?.status],
    enabled: selectedPhase !== null, // Only run this query if a phase is selected
    queryFn: () => GetWorkflowPhaseDetails(selectedPhase!), // Fetch details for the selected phase ID
  });

  // ... later in the return statement ...
  {!isRunning && selectedPhase && phaseDetails.data && ( // If not running, a phase is selected, and details are loaded
    <div className="flex flex-col py-4 container gap-4 overflow-auto">
      {/* ... Display Credits, Duration for this phase ... */}

      {/* Display Inputs */}
      <ParamaterViewer
        title="Inputs"
        subTitle="Inputs used for this phase"
        paramsJson={phaseDetails.data.inputs} // *** Pass the saved inputs JSON ***
      />

      {/* Display Outputs */}
      <ParamaterViewer
        title="Outputs"
        subTitle="Outputs generated by this phase"
        paramsJson={phaseDetails.data.outputs} // *** Pass the saved outputs JSON ***
      />

      {/* Display Logs */}
      <LogViewer logs={phaseDetails.data.logs} /> {/* *** Pass the saved logs *** */}
    </div>
  )}
```

This code fetches the specific `executionPhase` record using `GetWorkflowPhaseDetails`:

```typescript
// Inside actions/workflows/getWorkflowPhaseDetails.ts (simplified)
export async function GetWorkflowPhaseDetails(phaseId: string) {
  // ... auth check ...

  return prisma.executionPhase.findUnique({
    where: {
      id: phaseId,
      // ... ensure user owns the execution ...
    },
    include: {
      logs: { // *** Include the logs associated with this phase ***
        orderBy: { timestamp: "asc" },
      },
    },
  });
}
```

The data returned includes the `inputs` (as a JSON string), `outputs` (as a JSON string), `creditsConsumed`, timing information, and the associated `logs` for *that specific phase instance*. The `ParamaterViewer` and `LogViewer` components simply format this saved data for display.

This demonstrates the core value of Execution Phases: they give you granular visibility into exactly what happened at each step for any given run of your workflow.

### How Execution Phases Work Internally

Execution Phases are created and updated by the **[Workflow Execution](05_workflow_execution_.md)** engine (`ExecuteWorkflow`).

1.  **Creation:** When `ExecuteWorkflow` starts a run, it first creates the main `workflowExecution` record. At the same time (or immediately after), it iterates through the **[Execution Plan](06_execution_plan_.md)** derived from the workflow definition. For *each node* (task) in the ordered [Execution Plan](06_execution_plan_.md), it creates a corresponding `executionPhase` record in the database. These records are initially set to `CREATED` status and linked to the main `workflowExecution`.

    ```typescript
    // Inside actions/workflows/runWorkflow.ts (simplified creation)
    const execution = await prisma.workflowExecution.create({
      data: {
        // ... workflow and user data ...
        status: WorkflowExecutionStatus.PENDING,
        // ... timing and trigger ...
        definition: workflowDefinition,
        // *** Create phases based on the execution plan nodes ***
        phases: { create: executionPlan.flatMap(phase =>
           phase.nodes.map(node => ({
             name: TaskRegistry[node.data.type as any]?.label || node.data.type, // Store task name
             node: JSON.stringify(node), // Store the node data snapshot
             number: phase.phase, // Store the phase number from the plan
             status: ExecutionPhaseStatus.CREATED, // Initial status
           }))
         ) },
      },
      // ... select ID ...
    });
    ```
    This snippet shows that when the `workflowExecution` record is created, it also creates related `executionPhase` records using the structure defined by the `executionPlan`. Each phase record stores a snapshot of the node it represents and its position in the plan.

2.  **Execution and Updates:** As `ExecuteWorkflow` processes the [Execution Plan](06_execution_plan_.md) phase by phase, when it's time to run a specific task node (handled by `executeWorkflowPhase`), it performs the following steps related to the corresponding `executionPhase` record:
    *   Updates the phase record's status to `RUNNING`.
    *   Records the `startedAt` timestamp.
    *   Calculates the task's inputs based on preceding phases' outputs or manual values (as explained in [Chapter 5](05_workflow_execution_.md)) and saves these inputs (as JSON) into the phase record.
    *   Calls the specific **Executor** function for that task type.
    *   Collects logs generated by the Executor using the `LogCollector` (as discussed in [Chapter 5](05_workflow_execution_.md) and shown in `lib/log.ts`). These logs are temporarily stored in memory for that phase.
    *   Handles credit deduction *before* execution (updates user balance).
    *   After the Executor finishes, saves the generated outputs (as JSON) into the phase record.
    *   Saves all collected logs from the `LogCollector` into the database, linked to this specific phase record.
    *   Updates the phase record's status to `COMPLETED` or `FAILED` based on the Executor's result.
    *   Records the `completedAt` timestamp.
    *   Records the `creditsConsumed` for this phase.

    ```typescript
    // Inside lib/workflow/executeWorkflow.ts (simplified snippet from executeWorkflowPhase)
    async function executeWorkflowPhase(/* ... phase, environment, ... */) {
      // ... setup environment, get logCollector ...

      // Update the phase status to RUNNING and save inputs
      await prisma.executionPhase.update({
        where: { id: phase.id },
        data: {
          status: ExecutionPhaseStatus.RUNNING,
          startedAt: new Date(),
          inputs: JSON.stringify(environment.phases[node.id].inputs), // *** Save inputs ***
        },
      });

      // ... decrement credits ...

      let success = false;
      if (/* ... credits sufficient ... */) {
        // Call the actual Executor function
        // This function generates logs via environment.log and sets outputs via environment.setOutput
        success = await runFn(executionEnvironment);
      } else {
        // Handle insufficient credits failure
        logCollector.error("Insufficient balance.");
      }


      // Finalize the phase: update status, save outputs and logs
      const outputs = environment.phases[node.id].outputs;
      await finalizePhase(phase.id, success, outputs, logCollector, creditsConsumed);

      return { success, creditsConsumed };
    }

    // Simplified snippet from finalizePhase (called by executeWorkflowPhase)
    async function finalizePhase(
      phaseId: string, success: boolean, outputs: any, logCollector: LogCollector, creditsConsumed: number
    ) {
       const logs = logCollector.getAll(); // Get all logs collected during this phase

       await prisma.executionPhase.update({
         where: { id: phaseId },
         data: {
           status: success ? ExecutionPhaseStatus.COMPLETED : ExecutionPhaseStatus.FAILED, // *** Save final status ***
           completedAt: new Date(),
           outputs: JSON.stringify(outputs), // *** Save outputs ***
           creditsConsumed: creditsConsumed, // *** Save credits ***
           logs: {
             create: logs.map(log => ({ // *** Create log records linked to this phase ***
               message: log.message,
               logLevel: log.level,
               timestamp: log.timestamp,
             })),
           },
         },
       });
    }
    ```
    These simplified snippets show the core database interactions: updating the `executionPhase` record with its status, inputs, outputs, credits, and linked logs as it progresses and finishes.

### Execution Phases vs. Workflow Execution Status

It's helpful to distinguish between the status of the overall **[Workflow Execution](05_workflow_execution_.md)** and the status of individual **Execution Phases**.

*   The `WorkflowExecutionStatus` (e.g., `RUNNING`, `COMPLETED`, `FAILED`) reflects the state of the entire workflow run.
*   The `ExecutionPhaseStatus` (e.g., `PENDING`, `RUNNING`, `COMPLETED`, `FAILED`) reflects the state of a single task instance within that run.

For example, if just *one* task fails during a workflow run, the overall `WorkflowExecutionStatus` will become `FAILED`, and the status of that specific `executionPhase` record will also be `FAILED`. All preceding phases would likely be `COMPLETED`, and any subsequent phases that didn't get a chance to run might remain `PENDING` or similar.

You can see the overall execution status represented by the `ExecutionStatusIndicator` (or `ExecutionStatusLabel`):

```typescript
// Inside app/workflow/runs/[workflowId]/_components/ExecutionStatusIndicator.tsx
import { WorkflowExecutionStatus } from "@/types/workflow";
import { cn } from "@/lib/utils";

const indicatorColors: Record<WorkflowExecutionStatus, string> = {
  PENDING: "bg-slate-400",
  RUNNING: "bg-yellow-400",
  FAILED: "bg-red-400",
  COMPLETED: "bg-emerald-600",
};

export default function ExecutionStatusIndicator({ status }: { status: WorkflowExecutionStatus }) {
  return (
    <div className={cn("w-2 h-2 rounded-full", indicatorColors[status])} />
  );
}
// ... ExecutionStatusLabel also exists
```
This shows the simpler overall status, whereas the `PhaseStatusBadge` ([app/workflow/runs/[workflowId]/[executionId]/_components/PhaseStatusBadge.tsx](agentic-workflow/app/workflow/runs/%5BworkflowId%5D/%5BexecutionId%5D/_components/PhaseStatusBadge.tsx)) is used for the more granular phase status within the details view.

### Conclusion

**Execution Phases** are essential records that provide detailed visibility into what happens during a specific **[Workflow Execution](05_workflow_execution_.md)**. For each task instance run according to the **[Execution Plan](06_execution_plan_.md)**, an Execution Phase record captures its status, inputs, outputs, logs, timing, and credit cost. This allows you to easily monitor the progress of live runs and diagnose issues with past runs by examining the state and messages at each individual step.

Now that you understand how workflows are built (Workflows, Tasks, AI Agents, Flow Editor), planned (Execution Plan), run (Workflow Execution), and tracked in detail (Execution Phases), the next chapter will look at how the system knows *which code* to run for each specific Task type: the **[Task & Executor Registry](08_task___executor_registry_.md)**.

[Task & Executor Registry](08_task___executor_registry_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)