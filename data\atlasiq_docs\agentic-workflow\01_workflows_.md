# Chapter 1: Workflows

Welcome to the `agentic-workflow` tutorial! In this first chapter, we'll introduce you to the central concept of this project: **Workflows**.

Imagine you have a task you do repeatedly on your computer. Maybe you need to:
*   Visit a specific website, find a piece of information (like a product price or a stock quote), and save it somewhere.
*   Download data from an online service and then process it in a certain way.
*   Fill out a form online using data from a spreadsheet.

Doing these things manually every time can be tedious and time-consuming. This is where automation comes in!

### What is a Workflow?

Think of a Workflow in `agentic-workflow` like a **recipe** or a **blueprint** for automating a digital task. It doesn't *do* the task itself, but it *defines* exactly how the task should be done, step by step.

A workflow is essentially a sequence of instructions. Each instruction in this sequence is called a **[Task](02_tasks_.md)**. So, a Workflow is a collection of [Tasks](02_tasks_.md) linked together in a specific order to achieve a particular goal.

Using our examples above:

*   **Recipe for checking a product price:**
    1.  *Step 1:* Go to the online store page.
    2.  *Step 2:* Find the price of the product on that page.
    3.  *Step 3:* Write the price down (or save it to a file).
*   **Recipe for processing data:**
    1.  *Step 1:* Log in to the online service.
    2.  *Step 2:* Download the latest data file.
    3.  *Step 3:* Open the data file.
    4.  *Step 4:* Filter the data based on certain criteria.
    5.  *Step 5:* Save the filtered data.

In `agentic-workflow`, these steps (like "Go to page", "Find price", "Save data") are our **[Tasks](02_tasks_.md)**, and the complete list in order is our **Workflow**.

### Workflows in Action

`agentic-workflow` provides a place where you can:
1.  **Create** new workflows (your automation recipes).
2.  **Manage** your existing workflows (view them, edit them, duplicate them, delete them).
3.  **Run** your workflows (execute the recipe!).

When you use the application, the main screen often shows you a list of your saved Workflows. This is like looking at your personal collection of automation recipes.

You can see some of the code that handles listing workflows in the `app/(dashboard)/workflows/page.tsx` file. It fetches the workflows saved for the current user:

```typescript
// Inside app/(dashboard)/workflows/page.tsx
async function UserWorkflows() {
  // Get the workflows for the logged-in user
  const workflows = await GetWorkflowsForUser();

  // ... (rest of the code to display them)
  return (
    <div className="grid grid-cols-1 gap-4">
      {workflows.map((workflow) => (
        <WorkflowCard key={workflow.id} workflow={workflow} />
      ))}
    </div>
  );
}
```

This code uses a function `GetWorkflowsForUser` (defined in `actions/workflows/getWorkflowsForUser.ts`) to retrieve the workflows from storage.

When you create a new workflow, you're essentially starting a new recipe. You give it a name and description.

```typescript
// Inside actions/workflows/createWorkflow.ts
export async function CreateWorkflow(form: createWorkflowSchemaType) {
  // ... (validation and user check)

  // Define the initial structure of the workflow (starts empty or with a default task)
  const initialFlow: { nodes: AppNode[]; edges: Edge[] } = {
    nodes: [],
    edges: [],
  };

  // Add a default starting task (we'll learn about Tasks later!)
  initialFlow.nodes.push(CreateFlowNode(TaskType.TEST_1)); // Example initial node

  // Save the new workflow blueprint to the database
  const result = await prisma.workflow.create({
    data: {
      userId,
      status: WorkflowStatus.DRAFT, // It's a draft until you finish the recipe
      definition: JSON.stringify(initialFlow), // The recipe is stored here
      ...data, // name, description from the form
    },
  });

  // ... (handle result and redirect)
}
```

This code shows that when you create a workflow, a new entry is saved. Crucially, it includes `definition: JSON.stringify(initialFlow)`. This `definition` is the digital blueprint – it's the list of [Tasks](02_tasks_.md) and how they are connected. It's often stored as a JSON object.

### What's Inside a Workflow (The Definition)?

The core part of a saved Workflow is its `definition`. This is where the sequence and logic are stored. It contains information about:
*   Which [Tasks](02_tasks_.md) are part of this workflow?
*   In what order should these [Tasks](02_tasks_.md) run?
*   How are the outputs of one [Task](02_tasks_.md) used as inputs for the next [Task](02_tasks_.md)?

You typically build and edit this `definition` visually using the **[Flow Editor](04_flow_editor_.md)**, which you'll explore in a later chapter. It allows you to drag and drop [Tasks](02_tasks_.md) and draw connections between them, much like drawing a flowchart.

### Running a Workflow (Execution)

Once you've created and saved your workflow (your recipe), you can **run** it. Running a workflow is called a **[Workflow Execution](05_workflow_execution_.md)**. This is the part where the system actually *follows* the steps defined in your workflow's blueprint to perform the automated task.

You can trigger an execution manually (like starting a blender after adding all the ingredients) or schedule it to run automatically (like setting a timer on a slow cooker). The code managing workflow runs (like `actions/workflows/runWorkflow.ts` and the API endpoint `app/api/workflows/execute/route.ts`) retrieves the workflow's `definition` and starts processing the [Tasks](02_tasks_.md).

```typescript
// Inside actions/workflows/runWorkflow.ts (simplified)
export async function RunWorkflow(form: { workflowId: string; flowDefinition?: string }) {
  // ... (user and workflow lookup)

  let executionPlan: WorkflowExecutionPlan;
  let workflowDefinition = flowDefinition;

  if (workflow.status === WorkflowStatus.PUBLISHED) {
    // If published, use the saved execution plan (explained in a later chapter)
    executionPlan = JSON.parse(workflow.executionPlan!);
    workflowDefinition = workflow.definition;
  } else {
    // If draft, generate the execution plan from the current definition
    const flow = JSON.parse(flowDefinition!);
    const result = FlowToExecutionPlan(flow.nodes, flow.edges); // Convert blueprint to plan
    executionPlan = result.executionPlan!;
  }

  // Create a record of this specific run in the database
  const execution = await prisma.workflowExecution.create({
    data: {
      workflowId,
      userId,
      status: WorkflowExecutionStatus.PENDING,
      startedAt: new Date(),
      trigger: WorkflowExecutionTrigger.MANUAL, // or CRON
      definition: workflowDefinition,
      // Prepare the steps for execution based on the plan
      phases: { create: executionPlan.flatMap(...) },
    },
  });

  // Start the actual process that will run the steps in the background
  ExecuteWorkflow(execution.id); // This is where the magic happens!

  // ... (redirect to view the running execution)
}
```

This snippet shows that running a workflow involves creating a new "execution" record and then starting the process (`ExecuteWorkflow`) that follows the blueprint (`definition`/`executionPlan`).

### Workflow Management Flow

Here's a very simplified view of how workflows are managed:

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant Database

    User->>Frontend: Click "Create Workflow"
    Frontend->>User: Show Create Workflow Dialog
    User->>Frontend: Enter Name/Description, Click "Create"
    Frontend->>Backend: Call CreateWorkflow action
    Backend->>Database: Save new Workflow record (with initial definition)
    Database-->>Backend: Workflow ID
    Backend-->>Frontend: Success / Redirect URL
    Frontend->>User: Redirect to Workflow Editor
    User->>Frontend: Edit Workflow (add/connect Tasks)
    Frontend->>Backend: Call UpdateWorkflow action (Save changes)
    Backend->>Database: Update Workflow definition
    Database-->>Backend: Success
    Backend-->>Frontend: Confirmation
    User->>Frontend: Click "Run Workflow"
    Frontend->>Backend: Call RunWorkflow action
    Backend->>Database: Get Workflow definition
    Database-->>Backend: Workflow definition
    Backend->>Database: Create WorkflowExecution record
    Database-->>Backend: Execution ID
    Backend->>Backend: Start background execution process (ExecuteWorkflow)
    Backend-->>Frontend: Success / Redirect URL
    Frontend->>User: View running Workflow Execution
```

As you can see, the Workflow itself is the saved plan in the database. The `definition` field holds the detailed instructions (the linked [Tasks](02_tasks_.md)) that the system will follow when you trigger a **[Workflow Execution](05_workflow_execution_.md)**.

### Conclusion

In this chapter, you learned that Workflows are the core concept in `agentic-workflow`. They are reusable blueprints or recipes that define how to automate digital tasks by specifying a sequence of steps called [Tasks](02_tasks_.md). You can create, manage, and run these workflows to automate your work.

The next chapter will dive deeper into the individual building blocks that make up a workflow: **[Tasks](02_tasks_.md)**. You'll learn what they are and how they perform specific actions.

[Tasks](02_tasks_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)