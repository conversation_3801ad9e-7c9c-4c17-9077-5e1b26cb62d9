# <PERSON>er <PERSON> (MongoDB Harici)

<PERSON><PERSON> <PERSON><PERSON><PERSON>, Agentic RAG sistemini Docker ile MongoDB olmadan nasıl çalıştıracağınızı gösterir.

## Ön Ko<PERSON>ullar

1. **Docker ve Docker Compose** kurulu olmalı
2. **API Key'ler** ha<PERSON><PERSON><PERSON> (OpenAI, Tavily)

## Hızlı Başlangıç

### 1. Environment Dosyasını Hazırlayın
```bash
cp .env.example .env
# .env dosyasını düzenleyin:
# - OPENAI_API_KEY=your_key_here
# - TAVILY_API_KEY=your_key_here
```

### 2. Backend'i Başlatın

#### PowerShell ile (Önerilen)
```powershell
.\scripts\start-backend-only.ps1
```

#### Manuel olarak
```bash
docker-compose -f docker-compose.backend-only.yml up -d
```

## Kullanım

### API Erişimi
- **API**: http://localhost:8000
- **Dokümantasyon**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/

### Yönetim Komutları

```powershell
# Backend'i durdur
.\scripts\start-backend-only.ps1 -Stop

# Backend'i yeniden başlat
.\scripts\start-backend-only.ps1 -Restart

# Logları görüntüle
.\scripts\start-backend-only.ps1 -Logs

# Durum kontrolü
.\scripts\start-backend-only.ps1 -Status
```

### Manuel Docker Komutları

```bash
# Durumu kontrol et
docker-compose -f docker-compose.backend-only.yml ps

# Logları görüntüle
docker-compose -f docker-compose.backend-only.yml logs -f agentic-rag

# Container'a bağlan
docker-compose -f docker-compose.backend-only.yml exec agentic-rag bash

# Durdur
docker-compose -f docker-compose.backend-only.yml down
```

## Sorun Giderme

### Container Başlamıyor
```bash
# Logları kontrol edin
docker-compose -f docker-compose.backend-only.yml logs agentic-rag

# .env dosyasını kontrol edin
cat .env
```

### Port Çakışması
```bash
# 8000 portunu kullanan process'i bulun
netstat -ano | findstr :8000
# Process'i sonlandırın
taskkill /PID <PID> /F
```

## Yapılandırma

### Bot Yapılandırmaları
Bot yapılandırmaları `configs/` klasöründe bulunur:
- `student_bot.yaml`
- `academic_bot.yaml`
- `admin_bot.yaml`
- `atlasiq_bot.yaml`

Her bot kendi MongoDB ve SQL bağlantı ayarlarına sahiptir.

## Güvenlik

- Container non-root user ile çalışır
- Host network modu kullanılır (harici servislere erişim için)
- API key'ler environment variable'lar ile geçirilir
- Hassas dosyalar read-only mount edilir

## Performans

- Multi-stage Docker build kullanılır
- Minimal Python image (slim)
- Health check'ler otomatik olarak çalışır
- Resource monitoring için `docker stats` kullanın
