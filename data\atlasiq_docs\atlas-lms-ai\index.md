# Tutorial: atlas-lms-ai

Atlas LMS AI is an **intelligent learning platform** that uses *Google Gemini AI* to instantly create personalized study materials. Users input topics, course types, and difficulty, and the AI generates detailed course outlines, chapter notes, flashcards, and quizzes, acting as a *customized digital tutor*.


**Source Repository:** [None](None)

```mermaid
flowchart TD
    A0["AI Content Generation Engine
"]
    A1["Data Persistence (Drizzle ORM)
"]
    A2["User Authentication (Clerk)
"]
    A3["Background Task Processing (Inngest)
"]
    A4["Study Material & Course Management
"]
    A5["UI Component Library (Shadcn UI)
"]
    A6["Stripe Payment Integration
"]
    A0 -- "Stores Output" --> A1
    A0 -- "Queues Tasks" --> A3
    A2 -- "Persists User Data" --> A1
    A3 -- "Triggers AI Processing" --> A0
    A3 -- "Saves Processed Data" --> A1
    A4 -- "Requests AI Generation" --> A0
    A4 -- "Manages Course Data" --> A1
    A4 -- "Authenticates Access" --> A2
    A4 -- "Uses UI Components" --> A5
    A4 -- "Handles Payments" --> A6
    A6 -- "Updates Membership" --> A1
    A6 -- "Presents Payment UI" --> A5
```

## Chapters

1. [User Authentication (Clerk)
](01_user_authentication__clerk__.md)
2. [Data Persistence (Drizzle ORM)
](02_data_persistence__drizzle_orm__.md)
3. [Study Material & Course Management
](03_study_material___course_management_.md)
4. [AI Content Generation Engine
](04_ai_content_generation_engine_.md)
5. [Background Task Processing (Inngest)
](05_background_task_processing__inngest__.md)
6. [UI Component Library (Shadcn UI)
](06_ui_component_library__shadcn_ui__.md)
7. [Stripe Payment Integration
](07_stripe_payment_integration_.md)


---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)