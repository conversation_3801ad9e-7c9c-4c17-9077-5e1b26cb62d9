"""
API request and response models for the Agentic RAG system.
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


class QueryRequest(BaseModel):
    """Request model for querying a bot."""

    query: str = Field(..., description="The user's query")
    session_id: Optional[str] = Field(
        None, description="Session ID for conversation context"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata for the query"
    )


class ToolResponse(BaseModel):
    """Response from a tool."""

    tool_name: str = Field(
        ..., description="The name of the tool that generated this response"
    )
    content: Any = Field(..., description="The content of the tool's response")
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )


class QueryResponse(BaseModel):
    """Response model for a bot query."""

    bot_name: str = Field(
        ..., description="The name of the bot that processed the query"
    )
    query: str = Field(..., description="The original query")
    response: str = Field(..., description="The bot's response to the query")
    tool_responses: Optional[List[ToolResponse]] = Field(
        None, description="Responses from individual tools"
    )
    selected_tools: Optional[List[str]] = Field(
        None, description="List of tool names that were selected for this query"
    )
    tool_selection_reasoning: Optional[str] = Field(
        None, description="The reasoning behind the tool selection"
    )
    raw_llm_output: Optional[Dict[str, Any]] = Field(
        None, description="Raw output from the LLM tool selection process"
    )
    session_id: Optional[str] = Field(
        None, description="Session ID for conversation context"
    )
    execution_time: Optional[float] = Field(
        None, description="Query execution time in seconds (only shown in test mode)"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )


class ErrorResponse(BaseModel):
    """Error response model."""

    error: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(
        None, description="Additional error details"
    )


class BotInfo(BaseModel):
    """Information about a bot."""

    name: str = Field(..., description="The name of the bot")
    description: str = Field("", description="A description of the bot")
    tools: List[str] = Field(
        default_factory=list, description="The tools available to the bot"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )


class BotsListResponse(BaseModel):
    """Response model for listing available bots."""

    bots: List[BotInfo] = Field(..., description="List of available bots")


class DocumentUploadResponse(BaseModel):
    """Response model for document upload processing."""

    success: bool = Field(
        ..., description="Whether the upload and processing was successful"
    )
    message: str = Field(..., description="Human-readable message about the operation")
    collection_name: Optional[str] = Field(
        None, description="Name of the collection where documents were stored"
    )
    uploaded_files: Optional[List[str]] = Field(
        None, description="List of successfully uploaded file names"
    )
    processed_files: Optional[int] = Field(
        None, description="Number of files successfully processed"
    )
    failed_files: Optional[int] = Field(
        None, description="Number of files that failed to process"
    )
    temp_directory: Optional[str] = Field(
        None, description="Temporary directory used for processing"
    )
    error: Optional[str] = Field(None, description="Error message if processing failed")
    details: Optional[Dict[str, Any]] = Field(
        None, description="Additional processing details"
    )


class SqlConnectionRequest(BaseModel):
    """Request model for SQL database connection test."""

    connection_string: str = Field(..., description="SQL Server connection string")


class SqlConnectionResponse(BaseModel):
    """Response model for SQL database connection test."""

    success: bool = Field(..., description="Whether the connection was successful")
    message: str = Field(..., description="Connection result message")
    error: Optional[str] = Field(None, description="Error message if connection failed")


class SqlQueryRequest(BaseModel):
    """Request model for SQL database query test."""

    connection_string: str = Field(..., description="SQL Server connection string")


class SqlQueryResponse(BaseModel):
    """Response model for SQL database query test."""

    success: bool = Field(..., description="Whether the query was successful")
    message: str = Field(..., description="Query result message")
    tables: Optional[List[str]] = Field(None, description="List of loaded table names")
    sample_data: Optional[Dict[str, Any]] = Field(
        None, description="Sample data from the first table"
    )
    error: Optional[str] = Field(None, description="Error message if query failed")
