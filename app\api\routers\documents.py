"""
Document processing API endpoints.
"""

from datetime import datetime
from pathlib import Path
from fastapi import APIRouter, Depends, Form

from app.document_processing.document_processor import DocumentProcessor
from app.models.api_models import DocumentUploadResponse
from app.api.dependencies import get_document_processor
from app.core.logging_config import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/documents", tags=["Documents"])


@router.post(
    "/process-directory",
    response_model=DocumentUploadResponse,
)
async def process_directory_path(
    collection_name: str = Form(
        ..., description="Name of the collection to store documents"
    ),
    directory_path: str = Form(
        ..., description="Path to the directory containing documents to process"
    ),
    recursive: bool = Form(
        True, description="Whether to process subdirectories recursively"
    ),
    processor: DocumentProcessor = Depends(get_document_processor),
):
    """Process documents from a specified directory path."""

    try:
        logger.info(f"Starting directory processing for collection: {collection_name}")
        logger.info(f"Directory path: {directory_path}")

        # Validate directory path
        dir_path = Path(directory_path)
        if not dir_path.exists():
            return DocumentUploadResponse(
                success=False,
                message="Directory does not exist",
                error=f"The specified directory path does not exist: {directory_path}",
            )

        if not dir_path.is_dir():
            return DocumentUploadResponse(
                success=False,
                message="Path is not a directory",
                error=f"The specified path is not a directory: {directory_path}",
            )

        # Get list of files in directory for logging
        files_in_dir = []
        if recursive:
            files_in_dir = [f.name for f in dir_path.rglob("*") if f.is_file()]
        else:
            files_in_dir = [f.name for f in dir_path.iterdir() if f.is_file()]

        logger.info(f"Found {len(files_in_dir)} files to process")

        # Process the directory
        custom_metadata = {
            "source_type": "directory_path",
            "processed_by": "api_client",
            "processing_timestamp": datetime.now().isoformat(),
            "source_directory": str(dir_path.resolve()),
        }

        result = await processor.process_directory(
            directory_path=directory_path,
            collection_name=collection_name,
            recursive=recursive,
            custom_metadata=custom_metadata,
        )

        if result["success"]:
            message = f"Successfully processed directory: {directory_path}"
            if result["failed_count"] > 0:
                message += f" ({result['failed_count']} files failed to process)"

            logger.info(f"Directory processing completed: {message}")

            return DocumentUploadResponse(
                success=True,
                message=message,
                collection_name=collection_name,
                uploaded_files=files_in_dir,  # Files that were found in directory
                processed_files=result["successful_count"],
                failed_files=result["failed_count"],
                temp_directory=directory_path,  # Original directory path
                details={
                    "total_files": result["total_files"],
                    "processing_results": result["results"],
                    "source_directory": str(dir_path.resolve()),
                    "recursive": recursive,
                },
            )
        else:
            logger.error(
                f"Directory processing failed: {result.get('error', 'Unknown error')}"
            )
            return DocumentUploadResponse(
                success=False,
                message="Directory processing failed",
                collection_name=collection_name,
                uploaded_files=files_in_dir,
                processed_files=0,
                failed_files=len(files_in_dir),
                temp_directory=directory_path,
                error=result.get("error", "Unknown error"),
            )

    except Exception as e:
        error_msg = f"Error during directory processing: {str(e)}"
        logger.error(error_msg)
        import traceback

        logger.error(f"Directory processing traceback: {traceback.format_exc()}")

        return DocumentUploadResponse(
            success=False,
            message="Directory processing failed",
            collection_name=collection_name,
            error=error_msg,
        )
