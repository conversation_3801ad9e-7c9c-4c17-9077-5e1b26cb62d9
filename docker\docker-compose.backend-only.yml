services:
  # Agentic RAG Backend
  agentic-rag:
    build:
      # The build context is now the parent directory (project root)
      # so that the Dockerfile can find all necessary files (app, configs, etc.).
      context: ..
      # The Dockerfile path is relative to the build context (project root).
      # It should point to the file inside the 'docker' directory.
      dockerfile: docker/Dockerfile.backend-only
      target: production
    container_name: agentic-rag-backend
    restart: unless-stopped
    # When using network_mode: "host", the container shares the host's network.
    # Port mapping is not needed and is ignored. The service will be available on localhost:8000.
    # ports:
    #   - "8000:8000"
    environment:
      - HOST=0.0.0.0
      - PORT=3820
      - DEBUG=true
      - LOG_LEVEL=INFO
      - LOG_DIR=logs
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - TAVILY_API_KEY=${TAVILY_API_KEY}
    volumes:
      # Volume paths must now point to the parent directory.
      - ../data:/app/data
      - ../configs:/app/configs:ro
      - ../prompts:/app/prompts:ro
      - ../logs:/app/logs
    # 'host' network mode removes network isolation between the container and the host.
    # This is often used for development to simplify networking.
    network_mode: "host" 
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3820/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
