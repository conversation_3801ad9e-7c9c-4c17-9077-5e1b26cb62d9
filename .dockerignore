# Git
.git
.gitignore
.gitattributes

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Documentation (keep docs/ for container)
README.md

# Data files (exclude from build, mount as volumes)
data/raw/*
data/processed/*
data/chroma_stores/*
*.sqlite
*.db

# Temporary files
tmp/
temp/
.tmp/

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Environment files (will be passed as env vars)
.env
.env.*

# Tests (keep examples/ for container)
tests/
test_*

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints/

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
